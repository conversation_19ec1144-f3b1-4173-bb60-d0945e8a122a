import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { createUserSchema, updateUserSchema, CreateUserFormData, UpdateUserFormData, LocalUser } from '../data'

export function useCreateAccountForm() {
  const form = useForm<CreateUserFormData>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      permissions: []
    }
  })

  const resetForm = () => {
    form.reset({
      username: '',
      email: '',
      password: '',
      permissions: []
    })
  }

  return {
    form,
    resetForm,
    isValid: form.formState.isValid,
    errors: form.formState.errors
  }
}

export function useUpdateAccountForm(user?: LocalUser) {
  const form = useForm<UpdateUserFormData>({
    resolver: zodResolver(updateUserSchema),
    defaultValues: {
      username: user?.username || '',
      email: user?.email || '',
      password: '',
      permissions: user?.permissions.map(p => p.id) || [],
      status: user?.status || 'active'
    }
  })

  const resetForm = (userData?: LocalUser) => {
    form.reset({
      username: userData?.username || '',
      email: userData?.email || '',
      password: '',
      permissions: userData?.permissions.map(p => p.id) || [],
      status: userData?.status || 'active'
    })
  }

  return {
    form,
    resetForm,
    isValid: form.formState.isValid,
    errors: form.formState.errors
  }
}
