import { useState, useMemo } from 'react'

import type { NormalCombo, SpecialCombo, NormalCombosResponse, SpecialCombosResponse } from '@/types/api/combo'
import type { MenuItem, UpdateMenuItemRequest } from '@/types/api/menu-items'

import { useNormalCombos, useSpecialCombos } from '@/hooks/api/use-menu-combos'
import { useMenuItems, useUpdateMenuItem, useUpdateCombo } from '@/hooks/api/use-menu-items'

type MenuItemFormData = {
  item_id: string
  item_name: string
  description?: string
  ots_price: number
  ta_price: number
  sort: number
  item_type_id: string
  customizations?: string
  allow_take_away: boolean
  allow_self_order: boolean
  is_eat_with: boolean
  item_image_path?: string
}

const transformNormalComboToMenuItem = (combo: NormalCombo): MenuItem => ({
  Id: 0,
  Pos_Id: 0,
  Item_Name: combo.combo_name,
  Item_Id: combo.combo_id,
  Item_Type_Id: combo.type,
  Item_Master_Id: 0,
  Item_Type_Master_Id: 0,
  Item_Image_Path: '',
  Item_Image_Path_Thumb: '',
  Last_Updated: combo.update_at,
  Description: '',
  Description_Fb: '',
  Ots_Price: combo.ots_value,
  Ta_Price: combo.ta_value,
  Point: 0,
  Is_Gift: 0,
  Allow_Take_Away: combo.allow_take_away,
  Show_On_Web: 0,
  Special_Type: 0,
  Show_Price_On_Web: 0,
  Active: combo.active,
  Is_Eat_With: 0,
  Require_Eat_With: 0,
  Item_Id_Eat_With: '',
  Sort: combo.sort,
  Is_Featured: 0,
  Is_Parent: 0,
  Is_Sub: 0,
  Time_Sale_Date_Week: 0,
  Time_Sale_Hour_Day: 0,
  Customizations: '',
  Allow_Self_Order: 0
})

const transformSpecialComboToMenuItem = (combo: SpecialCombo): MenuItem => ({
  Id: 0,
  Pos_Id: 0,
  Item_Name: combo.combo_name,
  Item_Id: combo.combo_id,
  Item_Type_Id: combo.type,
  Item_Master_Id: 0,
  Item_Type_Master_Id: 0,
  Item_Image_Path: combo.image || '',
  Item_Image_Path_Thumb: '',
  Last_Updated: combo.update_at,
  Description: '',
  Description_Fb: '',
  Ots_Price: combo.ots_value,
  Ta_Price: combo.ta_value,
  Point: 0,
  Is_Gift: 0,
  Allow_Take_Away: combo.allow_take_away,
  Show_On_Web: 0,
  Special_Type: 0,
  Show_Price_On_Web: 0,
  Active: combo.active,
  Is_Eat_With: 0,
  Require_Eat_With: 0,
  Item_Id_Eat_With: '',
  Sort: combo.sort,
  Is_Featured: 0,
  Is_Parent: 0,
  Is_Sub: 0,
  Time_Sale_Date_Week: combo.date_time_week || 0,
  Time_Sale_Hour_Day: combo.hour_time_day || 0,
  Customizations: '',
  Allow_Self_Order: 0
})

const checkNameMatch = (item: MenuItem, searchName: string): boolean => {
  if (!searchName) return true

  const searchTerm = searchName.toLowerCase()
  const itemNameMatches = item.Item_Name.toLowerCase().includes(searchTerm)
  const itemIdMatches = item.Item_Id.toLowerCase().includes(searchTerm)

  return itemNameMatches || itemIdMatches
}

const checkGroupMatch = (item: MenuItem, groupFilter: string): boolean => {
  if (!groupFilter || groupFilter === 'all') return true
  return item.Item_Type_Id === groupFilter
}

const checkTypeMatch = (item: MenuItem, typeFilter: string): boolean => {
  if (!typeFilter || typeFilter === 'all') return true

  const isEatWith = item.Is_Eat_With === 1
  const isNormal = item.Is_Eat_With === 0 && item.Is_Parent === 0 && item.Is_Sub === 0
  const isParent = item.Is_Parent === 1
  const isChild = item.Is_Sub === 1

  switch (typeFilter) {
    case 'eat_with':
      return isEatWith
    case 'normal':
      return isNormal
    case 'parent':
      return isParent
    case 'child':
      return isChild
    default:
      return true
  }
}

const checkTakeAwayMatch = (item: MenuItem, takeAwayFilter: string): boolean => {
  if (!takeAwayFilter || takeAwayFilter === 'all') return true

  const allowsTakeAway = item.Allow_Take_Away === 1
  return takeAwayFilter === 'yes' ? allowsTakeAway : !allowsTakeAway
}

const checkSelfOrderMatch = (item: MenuItem, selfOrderFilter: string): boolean => {
  if (!selfOrderFilter || selfOrderFilter === 'all') return true

  const allowsSelfOrder = item.Allow_Self_Order === 1
  return selfOrderFilter === 'yes' ? allowsSelfOrder : !allowsSelfOrder
}

export const useMenuContentEditor = (posParent: string) => {
  const [currentPage, setCurrentPage] = useState(0)
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null)
  const [selectedComboData, setSelectedComboData] = useState<any>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [filters, setFilters] = useState({
    itemName: '',
    itemGroup: 'all',
    itemType: 'all',
    allowTakeAway: 'all',
    allowSelfOrder: 'all',
    activeTab: 'menu'
  })

  const {
    data: menuItemsData,
    isLoading: isLoadingMenuItems,
    error: menuItemsError
  } = useMenuItems(currentPage, posParent, {
    enabled: !!posParent && filters.activeTab === 'menu'
  })

  const {
    data: normalCombosData,
    isLoading: isLoadingNormalCombos,
    error: normalCombosError
  } = useNormalCombos(currentPage + 1, posParent, {
    enabled: !!posParent && filters.activeTab === 'combo'
  })

  const {
    data: specialCombosData,
    isLoading: isLoadingSpecialCombos,
    error: specialCombosError
  } = useSpecialCombos(currentPage + 1, posParent, {
    enabled: !!posParent && filters.activeTab === 'special-combo'
  })

  const { mutate: updateMenuItem, isPending: isUpdatingMenuItem } = useUpdateMenuItem()
  const { mutate: updateCombo, isPending: isUpdatingCombo } = useUpdateCombo()

  const isUpdating = isUpdatingMenuItem || isUpdatingCombo

  const isLoading = isLoadingMenuItems || isLoadingNormalCombos || isLoadingSpecialCombos
  const error = menuItemsError || normalCombosError || specialCombosError
  const transformedData = useMemo(() => {
    const activeTab = filters.activeTab

    if (activeTab === 'menu') {
      return menuItemsData?.list_item || []
    }

    if (activeTab === 'combo') {
      const normalCombos = (normalCombosData as NormalCombosResponse)?.data || []
      return normalCombos.map(transformNormalComboToMenuItem)
    }

    if (activeTab === 'special-combo') {
      const specialCombos = (specialCombosData as SpecialCombosResponse)?.data || []
      return specialCombos.map(transformSpecialComboToMenuItem)
    }

    return []
  }, [
    filters.activeTab,
    menuItemsData?.list_item,
    (normalCombosData as NormalCombosResponse)?.data,
    (specialCombosData as SpecialCombosResponse)?.data
  ])

  const filteredItems = useMemo(() => {
    if (!transformedData.length) return []

    return transformedData.filter((item: MenuItem) => {
      const matchesName = checkNameMatch(item, filters.itemName)
      const matchesGroup = checkGroupMatch(item, filters.itemGroup)
      const matchesType = checkTypeMatch(item, filters.itemType)
      const matchesTakeAway = checkTakeAwayMatch(item, filters.allowTakeAway)
      const matchesSelfOrder = checkSelfOrderMatch(item, filters.allowSelfOrder)

      return matchesName && matchesGroup && matchesType && matchesTakeAway && matchesSelfOrder
    })
  }, [transformedData, filters])

  const handleEditItem = (item: MenuItem) => {
    setSelectedItem(item)

    if (filters.activeTab === 'combo') {
      const originalCombo = (normalCombosData as any)?.data?.find((combo: any) => combo.combo_id === item.Item_Id)
      setSelectedComboData(originalCombo)
    } else if (filters.activeTab === 'special-combo') {
      const originalCombo = (specialCombosData as any)?.data?.find((combo: any) => combo.combo_id === item.Item_Id)
      setSelectedComboData(originalCombo)
    } else {
      setSelectedComboData(null)
    }

    setIsEditDialogOpen(true)
  }

  const handleSaveItem = (formData: MenuItemFormData) => {
    if (!selectedItem) return

    const updateData: UpdateMenuItemRequest = {
      data: {
        Item_Id: formData.item_id,
        Ots_Price: formData.ots_price,
        Ta_Price: formData.ta_price,
        Item_Name: formData.item_name,
        Description: formData.description || '@',
        Sort: formData.sort,
        Item_Type_Id: formData.item_type_id,
        Customizations: formData.customizations || '',
        Is_Parent: 0,
        Is_Sub: 0,
        Is_Eat_With: formData.is_eat_with ? 1 : 0,
        Item_Id_Eat_With: ' ',
        Allow_Take_Away: formData.allow_take_away ? 1 : 0,
        Allow_Self_Order: formData.allow_self_order ? 1 : 0,
        Item_Image_Path: formData.item_image_path || '',
        Item_Image_Path_Thumb: ''
      },
      id_value: formData.item_id,
      table_name: 'DM_ITEM',
      list_pos: selectedItem.Pos_Id.toString(),
      pos_parent: posParent,
      sync_fields:
        'Item_Name,Description,Sort,Allow_Self_Order,Allow_Take_Away,Item_Type_Id,Is_Eat_With,Is_Parent,Is_Sub,Item_Id_Eat_With,Customizations,Item_Image_Path,Item_Image_Path_Thumb'
    }

    updateMenuItem(updateData, {
      onSuccess: () => {
        setIsEditDialogOpen(false)
        setSelectedItem(null)
      }
    })
  }

  const handleSaveCombo = (formData: any) => {
    if (!selectedItem) return

    const updateData: UpdateMenuItemRequest = {
      data: {
        Item_Id: formData.combo_id,
        Ots_Price: formData.ots_price,
        Ta_Price: formData.ta_price,
        Item_Name: formData.combo_name,
        Description: formData.description || '@',
        Sort: formData.sort,
        Item_Type_Id: selectedItem.Item_Type_Id,
        Customizations: '',
        Is_Parent: 0,
        Is_Sub: 0,
        Is_Eat_With: 0,
        Item_Id_Eat_With: ' ',
        Allow_Take_Away: formData.allow_take_away ? 1 : 0,
        Allow_Self_Order: 1,
        Item_Image_Path: formData.combo_image_path || '',
        Item_Image_Path_Thumb: ''
      },
      id_value: formData.combo_id,
      table_name: 'DM_ITEM',
      list_pos: selectedItem.Pos_Id.toString(),
      pos_parent: posParent,
      sync_fields:
        'Item_Name,Description,Sort,Allow_Self_Order,Allow_Take_Away,Item_Type_Id,Is_Eat_With,Is_Parent,Is_Sub,Item_Id_Eat_With,Customizations,Item_Image_Path,Item_Image_Path_Thumb'
    }

    updateCombo(updateData, {
      onSuccess: () => {
        setIsEditDialogOpen(false)
        setSelectedItem(null)
      }
    })
  }

  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false)
    setSelectedItem(null)
  }

  const updateFilter = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      itemName: '',
      itemGroup: 'all',
      itemType: 'all',
      allowTakeAway: 'all',
      allowSelfOrder: 'all',
      activeTab: 'menu'
    })
  }

  const itemGroups = useMemo(() => {
    if (!transformedData.length) return []
    const groups = [...new Set(transformedData.map((item: MenuItem) => item.Item_Type_Id))]
    return groups.filter(Boolean) as string[]
  }, [transformedData])
  const totalItems = useMemo(() => {
    if (filters.activeTab === 'menu') {
      return menuItemsData?.count || 0
    } else if (filters.activeTab === 'combo') {
      return (normalCombosData as NormalCombosResponse)?.count || 0
    } else if (filters.activeTab === 'special-combo') {
      return (specialCombosData as SpecialCombosResponse)?.count || 0
    }
    return 0
  }, [
    filters.activeTab,
    menuItemsData?.count,
    (normalCombosData as NormalCombosResponse)?.count,
    (specialCombosData as SpecialCombosResponse)?.count
  ])

  return {
    items: filteredItems,
    totalItems,
    totalPages: Math.ceil(totalItems / 20),
    itemGroups,
    isLoading,
    isUpdating,
    error,
    currentPage,
    setCurrentPage,
    filters,
    updateFilter,
    clearFilters,
    selectedItem,
    selectedComboData,
    isEditDialogOpen,
    handleEditItem,
    handleSaveItem,
    handleSaveCombo,
    handleCloseEditDialog
  }
}
