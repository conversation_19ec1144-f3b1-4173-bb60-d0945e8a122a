import { useState, useEffect } from 'react'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import type { User } from '@/types'

import { useAuthStore } from '@/stores/authStore'

import { useRolesData } from '@/hooks/api'

import { createFormSchema, type CreateEmployeeFormData } from '../components/form/schema/create-employee-form-schema'
import { isOwnerRole, needsStoreSelection, needsTableSelection, getBrandIdFromStore } from '../utils'

type CreateEmployeeSubmitData = {
  email: string
  full_name: string
  phone?: string
  role_uid: string
  password?: string
  brand_access?: string[]
}

interface UseCreateEmployeeFormProps {
  initialData?: User
  isEditMode?: boolean
  onSubmit: (data: CreateEmployeeSubmitData) => Promise<void>
}

export function useCreateEmployeeForm({ initialData, isEditMode = false, onSubmit }: UseCreateEmployeeFormProps) {
  const [showBrandSelector, setShowBrandSelector] = useState(false)
  const [showStoreSelector, setShowStoreSelector] = useState(false)
  const [showTableSelector, setShowTableSelector] = useState(false)

  const [selectedBrandAccess, setSelectedBrandAccess] = useState<string[]>([])
  const [selectedStore, setSelectedStore] = useState<string>('')
  const [selectedTables, setSelectedTables] = useState<string[]>([])

  const { data: roles = [], isLoading: rolesLoading } = useRolesData()
  const { stores } = useAuthStore(state => state.auth)

  const formSchema = createFormSchema(isEditMode)

  const getDefaultValues = () => ({
    email: initialData?.email || '',
    full_name: initialData?.full_name || '',
    phone: initialData?.phone || '',
    role_uid: initialData?.role_uid || '',
    password: '',
    confirmPassword: '',
    brand_access: initialData?.brand_access || []
  })

  const form = useForm<CreateEmployeeFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues()
  })

  useEffect(() => {
    if (initialData && isEditMode) {
      if (initialData.brand_access && initialData.brand_access.length > 0) {
        setSelectedBrandAccess(initialData.brand_access)

        const selectedRoleObj = roles.find(role => role.id === initialData.role_uid)
        if (needsStoreSelection(selectedRoleObj) && initialData.brand_access.length === 1) {
          setSelectedStore(initialData.brand_access[0])

          if (needsTableSelection(selectedRoleObj) && initialData.user_permissions?.tables) {
            const storeId = initialData.brand_access[0].replace('store:', '')
            const storeTables = initialData.user_permissions.tables[storeId]
            if (storeTables && Array.isArray(storeTables)) {
              setSelectedTables(storeTables)
            }
          }
        }
      } else {
        console.log('🔍 No brand_access found in initialData')
      }
    }
  }, [initialData, isEditMode, roles])

  useEffect(() => {
    if (initialData && isEditMode) {
      const formData = {
        email: initialData.email || '',
        full_name: initialData.full_name || '',
        phone: initialData.phone || '',
        role_uid: initialData.role_uid || '',
        password: '',
        confirmPassword: '',
        brand_access: initialData.brand_access || []
      }

      form.reset(formData)

      Object.entries(formData).forEach(([key, value]) => {
        form.setValue(key as keyof CreateEmployeeFormData, value, {
          shouldValidate: false,
          shouldDirty: false
        })
      })
    }
  }, [initialData, isEditMode, form])

  const selectedRole = form.watch('role_uid')
  const selectedRoleObj = roles.find(role => role.id === selectedRole)
  const isOwner = isOwnerRole(selectedRoleObj)
  const needsStore = needsStoreSelection(selectedRoleObj)
  const needsTables = needsTableSelection(selectedRoleObj)

  const handleSubmit = async (values: CreateEmployeeFormData) => {
    try {
      const { confirmPassword, ...submitData } = values

      let brandAccess: string[] = []
      if (!isOwner) {
        if (needsStore) {
          brandAccess = selectedStore ? [selectedStore] : []
        } else {
          brandAccess = selectedBrandAccess
        }
      }

      const finalSubmitData = {
        ...submitData,
        brand_access: brandAccess
      }

      await onSubmit(finalSubmitData)
    } catch (error) {
      console.error('Error creating employee:', error)
    }
  }

  const handleBrandAccessSave = (brandAccess: string[]) => {
    setSelectedBrandAccess(brandAccess)
    setShowBrandSelector(false)
  }

  const handleStoreSave = (store: string) => {
    setSelectedStore(store)
    setShowStoreSelector(false)
    setSelectedTables([])
  }

  const handleTablesSave = (tables: string[]) => {
    setSelectedTables(tables)
    setShowTableSelector(false)
  }

  return {
    form,
    handleSubmit,
    roles,
    rolesLoading,
    stores,
    selectedRole,
    selectedRoleObj,
    isOwner,
    needsStore,
    needsTables,
    selectedBrandAccess,
    selectedStore,
    selectedTables,
    showBrandSelector,
    setShowBrandSelector,
    showStoreSelector,
    setShowStoreSelector,
    showTableSelector,
    setShowTableSelector,
    handleBrandAccessSave,
    handleStoreSave,
    handleTablesSave,
    getBrandIdFromStore: (store: string) => getBrandIdFromStore(store, stores)
  }
}
