import { ParsedCustomizationData } from '@/types/customizations'
import { toast } from 'sonner'

export function useExcelParser() {
  const parseExcelFile = async (
    file: File,
    _stores: unknown[] = [],
    isImport = false
  ): Promise<ParsedCustomizationData[]> => {
    try {
      const XLSX = await import('xlsx')

      const data = await file.arrayBuffer()
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

      const parsedCustomizations: ParsedCustomizationData[] = []

      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i] as unknown[]

        if (isImport) {
          // Import format: name, appliedItemCodes, groupName, minRequired, maxAllowed, groupItemCodes
          if (row && row.length >= 6) {
            const parsed = {
              id: '', // ID will be generated by the API
              name: String(row[0] || '').trim(),
              cityName: '', // Not needed for store-based import
              storeName: '', // Will be populated from current store
              appliedItemCodes: String(row[1] || '').trim(),
              groupName: String(row[2] || '').trim(),
              minRequired: Number(row[3]) || 0,
              maxAllowed: Number(row[4]) || 0,
              groupItemCodes: String(row[5] || '').trim()
            }

            parsedCustomizations.push(parsed)
          }
        } else {
          if (row && row.length >= 6) {
            parsedCustomizations.push({
              id: '', // ID will be generated for export data
              name: String(row[0] || '').trim(),
              cityName: '', // Not included in store-based export
              storeName: '', // Will be populated from current store
              appliedItemCodes: String(row[1] || '').trim(),
              groupName: String(row[2] || '').trim(),
              minRequired: Number(row[3]) || 0,
              maxAllowed: Number(row[4]) || 0,
              groupItemCodes: String(row[5] || '').trim()
            })
          }
        }
      }

      return parsedCustomizations
    } catch (error) {
      toast.error('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.')
      throw error
    }
  }

  return {
    parseExcelFile
  }
}
