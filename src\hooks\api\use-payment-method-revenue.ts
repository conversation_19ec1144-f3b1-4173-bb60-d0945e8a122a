import { useQuery } from '@tanstack/react-query'

import type { PaymentMethodRevenueParams, PaymentMethodRevenueResponse } from '@/types/api'

import { getPaymentMethodRevenue } from '@/lib/api/pos'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UsePaymentMethodRevenueOptions {
  startDate: number
  endDate: number
  selectedStoreIds: string[]
  companyUid: string
  brandUid: string
  limit?: number
  enabled?: boolean
}

export const usePaymentMethodRevenue = ({
  startDate,
  endDate,
  selectedStoreIds,
  companyUid,
  brandUid,
  limit,
  enabled = true
}: UsePaymentMethodRevenueOptions) => {
  const queryKey = [
    QUERY_KEYS.REPORTS_PAYMENT_METHOD_REVENUE,
    companyUid,
    brandUid,
    startDate,
    endDate,
    selectedStoreIds.sort().join(','),
    limit
  ]

  const queryFn = async (): Promise<PaymentMethodRevenueResponse> => {
    if (!selectedStoreIds.length) {
      return {
        data: [],
        message: 'No stores selected',
        track_id: ''
      }
    }

    const params: PaymentMethodRevenueParams = {
      brand_uid: brandUid,
      company_uid: companyUid,
      start_date: startDate,
      end_date: endDate,
      list_store_uid: selectedStoreIds.join(','),
      store_open_at: 0,
      by_days: 1,
      ...(limit && { limit })
    }

    return getPaymentMethodRevenue(params)
  }

  const query = useQuery({
    queryKey,
    queryFn,
    enabled: enabled && selectedStoreIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: false
  })

  return {
    data: query.data?.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasData: (query.data?.data || []).length > 0
  }
}
