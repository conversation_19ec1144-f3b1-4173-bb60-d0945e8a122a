import React, { useState } from 'react'

import { usePosStores } from '@/stores'

import { usePosData } from '@/hooks/use-pos-data'

import { Card } from '@/components/ui/card'
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Badge } from '@/components/ui'

import { useSourceContext } from '../context'
import { InvoiceSourceModal } from './invoice-source-modal'

const fmt = (n: number) => new Intl.NumberFormat('vi-VN').format(n)

export const SourceTable: React.FC = () => {
  const {
    tableRows,
    isLoading,
    compareCities,
    compareStores,
    selectedStoreIds,
    compareStoreIds,
    compareRawData,
    startDate,
    endDate
  } = useSourceContext() as any
  const { currentBrandStores } = usePosStores()
  const { company, activeBrands } = usePosData()

  const [selectedSource, setSelectedSource] = useState<{ name: string; id: string } | null>(null)
  const [showInvoiceModal, setShowInvoiceModal] = useState(false)

  const hasCompareData = (compareCities?.length || 0) > 0 || (compareStores?.length || 0) > 0

  const totals = React.useMemo(() => {
    return tableRows.reduce(
      (acc: any, r: any) => ({
        total_bill: acc.total_bill + (r.total_bill || 0),
        revenue_net: acc.revenue_net + (r.revenue_net || 0),
        revenue_gross: acc.revenue_gross + (r.revenue_gross || 0),
        peo_count: acc.peo_count + (r.peo_count || 0),
        commission_amount: acc.commission_amount + (r.commission_amount || 0),
        discount_amount: acc.discount_amount + (r.discount_amount || 0),
        partner_marketing_amount: acc.partner_marketing_amount + (r.partner_marketing_amount || 0)
      }),
      {
        total_bill: 0,
        revenue_net: 0,
        revenue_gross: 0,
        peo_count: 0,
        commission_amount: 0,
        discount_amount: 0,
        partner_marketing_amount: 0
      }
    )
  }, [tableRows])

  const compareMap = React.useMemo(() => {
    const map = new Map<string, any>()
    ;(compareRawData || []).forEach((item: any) => {
      const key = item.source_id || item.source_name
      if (key) map.set(String(key), item)
    })
    return map
  }, [compareRawData])

  const compareTotals = React.useMemo(() => {
    return (compareRawData || []).reduce(
      (acc: any, r: any) => ({
        total_bill: acc.total_bill + (r.total_bill || 0),
        revenue_net: acc.revenue_net + (r.revenue_net || 0),
        revenue_gross: acc.revenue_gross + (r.revenue_gross || 0),
        commission_amount: acc.commission_amount + (r.commission_amount || 0),
        discount_amount: acc.discount_amount + (r.discount_amount || 0)
      }),
      { total_bill: 0, revenue_net: 0, revenue_gross: 0, commission_amount: 0, discount_amount: 0 }
    )
  }, [compareRawData])

  const renderStoresHeader = (ids: string[]) => {
    const stores = ids
      .map(id => currentBrandStores.find(s => (s.id || s.store_id) === id))
      .filter(Boolean) as Array<any>
    const firstStoreName = stores[0]?.store_name || 'Tất cả cửa hàng'
    const extraCount = Math.max(0, stores.length - 1)

    return (
      <div className='flex items-center justify-center gap-2'>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant='outline' className='cursor-default'>
              {firstStoreName}
            </Badge>
          </TooltipTrigger>
          {stores.length > 0 && (
            <TooltipContent side='bottom'>
              <div className='max-h-60 max-w-[260px] overflow-auto pr-1'>
                {stores.map(s => (
                  <div key={s.id || s.store_id} className='whitespace-nowrap'>
                    {s.store_name}
                  </div>
                ))}
              </div>
            </TooltipContent>
          )}
        </Tooltip>
        {extraCount > 0 && <Badge variant='secondary'>+{extraCount}</Badge>}
      </div>
    )
  }

  const handleRowClick = (row: any) => {
    setSelectedSource({
      name: row.source_name,
      id: row.source_id || row.source_name || ''
    })
    setShowInvoiceModal(true)
  }

  if (hasCompareData) {
    if (isLoading) {
      return (
        <Card>
          <div className='text-muted-foreground flex h-32 items-center justify-center'>Đang tải dữ liệu...</div>
        </Card>
      )
    }
    return (
      <div className='overflow-x-auto rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='border-b-2' colSpan={2} />
              <TableHead className='border-r-2 border-b-2 text-center' colSpan={9}>
                {selectedStoreIds?.length > 0 ? renderStoresHeader(selectedStoreIds) : 'Tất cả cửa hàng'}
              </TableHead>
              <TableHead className='border-b-2 text-center' colSpan={9}>
                {compareStoreIds?.length > 0 ? renderStoresHeader(compareStoreIds) : 'So sánh'}
              </TableHead>
            </TableRow>
            <TableRow>
              <TableHead className='w-10'>#</TableHead>
              <TableHead>Nguồn đơn</TableHead>
              <TableHead className='text-right'>Tổng hoá đơn</TableHead>
              <TableHead className='text-right'>Số khách</TableHead>
              <TableHead className='text-right'>Hoa hồng</TableHead>
              <TableHead className='text-right'>Giảm giá (merchant)</TableHead>
              <TableHead className='text-right'>Giảm giá (đối tác)</TableHead>
              <TableHead className='text-right'>Thuế khấu trừ</TableHead>
              <TableHead className='text-right'>Tổng chi phí</TableHead>
              <TableHead className='text-right'>Doanh thu (net)</TableHead>
              <TableHead className='border-r-2 text-right'>Doanh thu (gross)</TableHead>
              <TableHead className='border-l-2 text-right'>Tổng hoá đơn</TableHead>
              <TableHead className='text-right'>Số khách</TableHead>
              <TableHead className='text-right'>Hoa hồng</TableHead>
              <TableHead className='text-right'>Giảm giá (merchant)</TableHead>
              <TableHead className='text-right'>Giảm giá (đối tác)</TableHead>
              <TableHead className='text-right'>Thuế khấu trừ</TableHead>
              <TableHead className='text-right'>Tổng chi phí</TableHead>
              <TableHead className='text-right'>Doanh thu (net)</TableHead>
              <TableHead className='text-right'>Doanh thu (gross)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tableRows.map((row: any, idx: number) => {
              const cmp = compareMap.get(String(row.source_id)) || compareMap.get(String(row.source_name)) || {}
              const totalCost =
                (row.commission_amount || 0) + (row.discount_amount || 0) + (row.partner_marketing_amount || 0)
              const baseRevenue = row.revenue_gross || row.revenue_net || 0
              const lostRate = baseRevenue > 0 ? ((totalCost / baseRevenue) * 100).toFixed(2) : '0'
              const cmpTotalCost =
                (cmp.commission_amount || 0) + (cmp.discount_amount || 0) + (cmp.partner_marketing_amount || 0)
              const cmpBaseRevenue = (cmp.revenue_gross || cmp.revenue_net || 0) as number
              const cmpLostRate = cmpBaseRevenue > 0 ? ((cmpTotalCost / cmpBaseRevenue) * 100).toFixed(2) : '0'
              return (
                <TableRow
                  key={row.source_id || row.source_name}
                  className='cursor-pointer hover:bg-gray-50'
                  onClick={() => handleRowClick(row)}
                >
                  <TableCell className='font-mon text-left'>{idx + 1}</TableCell>
                  <TableCell className='font-mon text-left'>{row.source_name}</TableCell>

                  {/* Totals and costs (main) */}
                  <TableCell className='text-right font-mono'>{fmt(row.total_bill)}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.peo_count || 0)}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.commission_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.discount_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.partner_marketing_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>0.00 đ</TableCell>
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(totalCost)} đ</div>
                    <div className='text-muted-foreground text-xs'>Chi phí mất {lostRate}% doanh thu</div>
                  </TableCell>
                  {/* Net, Gross */}
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(row.revenue_net)} đ</div>
                    <div className='text-muted-foreground text-xs'>
                      Chiếm {totals.revenue_net > 0 ? ((row.revenue_net / totals.revenue_net) * 100).toFixed(2) : '0'}%
                      tổng doanh thu net
                    </div>
                  </TableCell>
                  <TableCell className='border-r-2 text-right'>
                    <div className='font-mono'>{fmt(row.revenue_gross)} đ</div>
                    <div className='text-muted-foreground text-xs'>
                      Chiếm{' '}
                      {totals.revenue_gross > 0 ? ((row.revenue_gross / totals.revenue_gross) * 100).toFixed(2) : '0'}%
                      tổng doanh thu gross
                    </div>
                  </TableCell>

                  {/* Compare cluster */}
                  <TableCell className='border-l-2 text-right font-mono'>{fmt(cmp.total_bill || 0)}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(cmp.peo_count || 0)}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(cmp.commission_amount || 0)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(cmp.discount_amount || 0)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(cmp.partner_marketing_amount || 0)} đ</TableCell>
                  <TableCell className='text-right font-mono'>0.00 đ</TableCell>
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(cmpTotalCost)} đ</div>
                    <div className='text-muted-foreground text-xs'>Chi phí mất {cmpLostRate}% doanh thu</div>
                  </TableCell>
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(cmp.revenue_net || 0)} đ</div>
                    <div className='text-muted-foreground text-xs'>
                      Chiếm{' '}
                      {compareTotals.revenue_net > 0
                        ? (((cmp.revenue_net || 0) / compareTotals.revenue_net) * 100).toFixed(2)
                        : '0'}
                      % tổng doanh thu net
                    </div>
                  </TableCell>
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(cmp.revenue_gross || 0)} đ</div>
                    <div className='text-muted-foreground text-xs'>
                      Chiếm{' '}
                      {compareTotals.revenue_gross > 0
                        ? (((cmp.revenue_gross || 0) / compareTotals.revenue_gross) * 100).toFixed(2)
                        : '0'}
                      % tổng doanh thu gross
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>
    )
  }

  if (isLoading) {
    return (
      <Card>
        <div className='text-muted-foreground flex h-32 items-center justify-center'>Đang tải dữ liệu...</div>
      </Card>
    )
  }
  return (
    <>
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='w-10'>#</TableHead>
              <TableHead>Mã nguồn</TableHead>
              <TableHead>Tên nguồn</TableHead>
              <TableHead className='text-right'>Tổng hoá đơn</TableHead>
              <TableHead className='text-right'>Số khách</TableHead>
              <TableHead className='text-right'>Hoa hồng (1)</TableHead>
              <TableHead className='text-right'>Giảm giá (merchant) (2)</TableHead>
              <TableHead className='text-right'>Giảm giá (đối tác) (3)</TableHead>
              <TableHead className='text-right'>Thuế khấu trừ</TableHead>
              <TableHead className='text-right'>Tổng chi phí (1) + (2) + (3)</TableHead>
              <TableHead className='text-right'>Doanh thu (net)</TableHead>
              <TableHead className='text-right'>Doanh thu (gross)</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {tableRows.map((row: any, idx: number) => {
              const totalCost =
                (row.commission_amount || 0) + (row.discount_amount || 0) + (row.partner_marketing_amount || 0)
              const baseRevenue = row.revenue_gross || row.revenue_net || 0
              const lostRate = baseRevenue > 0 ? ((totalCost / baseRevenue) * 100).toFixed(2) : '0'
              return (
                <TableRow
                  key={row.source_id || row.source_name}
                  className='cursor-pointer hover:bg-gray-50'
                  onClick={() => handleRowClick(row)}
                >
                  <TableCell>{idx + 1}</TableCell>
                  <TableCell className='font-mono'>{row.source_id || '-'}</TableCell>
                  <TableCell>{row.source_name}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.total_bill)}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.peo_count || 0)}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.commission_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.discount_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.partner_marketing_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>0.00 đ</TableCell>
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(totalCost)} đ</div>
                    <div className='text-muted-foreground text-xs'>Chi phí mất {lostRate}% doanh thu</div>
                  </TableCell>
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(row.revenue_net)} đ</div>
                    <div className='text-muted-foreground text-xs'>
                      Chiếm {totals.revenue_net > 0 ? ((row.revenue_net / totals.revenue_net) * 100).toFixed(2) : '0'}%
                      tổng doanh thu net
                    </div>
                  </TableCell>
                  <TableCell className='text-right'>
                    <div className='font-mono'>{fmt(row.revenue_gross)} đ</div>
                    <div className='text-muted-foreground text-xs'>
                      Chiếm{' '}
                      {totals.revenue_gross > 0 ? ((row.revenue_gross / totals.revenue_gross) * 100).toFixed(2) : '0'}%
                      tổng doanh thu gross
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
            {tableRows.length > 0 && (
              <TableRow>
                <TableCell className='font-semibold'>Tổng</TableCell>
                <TableCell />
                <TableCell />
                <TableCell className='text-right font-mono'>{fmt(totals.total_bill)}</TableCell>
                <TableCell className='text-right font-mono'>{fmt(totals.peo_count)}</TableCell>
                <TableCell className='text-right font-mono'>{fmt(totals.commission_amount)} đ</TableCell>
                <TableCell className='text-right font-mono'>{fmt(totals.discount_amount)} đ</TableCell>
                <TableCell className='text-right font-mono'>{fmt(totals.partner_marketing_amount)} đ</TableCell>
                <TableCell className='text-right font-mono'>0.00 đ</TableCell>
                <TableCell className='text-right font-mono'>
                  {fmt(totals.commission_amount + totals.discount_amount + totals.partner_marketing_amount)} đ
                </TableCell>
                <TableCell className='text-right font-mono'>{fmt(totals.revenue_net)} đ</TableCell>
                <TableCell className='text-right font-mono'>{fmt(totals.revenue_gross)} đ</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        {tableRows.length === 0 && (
          <div className='text-muted-foreground flex h-24 items-center justify-center'>Không có dữ liệu</div>
        )}
      </div>

      {/* Invoice Source Modal */}
      <InvoiceSourceModal
        open={showInvoiceModal}
        onOpenChange={setShowInvoiceModal}
        sourceName={selectedSource?.name}
        sourceId={selectedSource?.id}
        companyUid={company?.id}
        brandUid={activeBrands[0]?.id}
        startDate={startDate}
        endDate={endDate}
        storeUids={selectedStoreIds.length > 0 ? selectedStoreIds : currentBrandStores.map(store => store.id)}
      />
    </>
  )
}
