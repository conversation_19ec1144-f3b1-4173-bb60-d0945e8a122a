import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useCurrentUser } from '@/stores/posStore'

import { menuScheduleApi } from '@/lib/menu-schedule-api'

import type { MenuItem } from '../types/menu-schedule-api'
import { convertMenuItemToUpdatePayload } from '../utils/payload-converter'

interface UpdateItemScheduleParams {
  menuItems: MenuItem[]
  scheduleData: {
    company_uid: string
    brand_uid: string
    store_uid: string
    city_uid: string
    time: number
    end_time?: number | null
    schedule_id?: string
  }
}

export const useUpdateItemSchedule = () => {
  const queryClient = useQueryClient()
  const { user } = useCurrentUser()

  return useMutation({
    mutationFn: async ({ menuItems, scheduleData }: UpdateItemScheduleParams) => {
      const userEmail = user?.email || '<EMAIL>'

      const payload = menuItems.map(menuItem =>
        convertMenuItemToUpdatePayload(menuItem, scheduleData, userEmail)
      )

      return menuScheduleApi.updateItemSchedule(payload)
    },
    onSuccess: data => {
      toast.success('Cập nhật menu schedule thành công!')

      queryClient.invalidateQueries({ queryKey: ['menu-schedules'] })
      queryClient.invalidateQueries({ queryKey: ['menu-schedule-by-params'] })

      return data
    },
    onError: () => {
      toast.error('Có lỗi xảy ra khi cập nhật menu schedule')
    }
  })
}
