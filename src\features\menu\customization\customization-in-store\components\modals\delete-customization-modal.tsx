import { ConfirmModal } from '@/components/pos'
import { Customization } from '@/types/customizations'

interface DeleteCustomizationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedCustomization: Customization | null
  onCancel: () => void
  onConfirm: () => void
  isLoading: boolean
}

export function DeleteCustomizationModal({
  open,
  onOpenChange,
  selectedCustomization,
  onCancel,
  onConfirm,
  isLoading,
}: DeleteCustomizationModalProps) {
  return (
    <ConfirmModal
      open={open}
      onOpenChange={onOpenChange}
      title='Xác nhận xóa customization'
      content={`Bạn có chắc muốn xóa customization "${selectedCustomization?.name}"?`}
      onCancel={onCancel}
      onConfirm={onConfirm}
      confirmText='Xóa'
      cancelText='Hủy'
      isLoading={isLoading}
    />
  )
}
