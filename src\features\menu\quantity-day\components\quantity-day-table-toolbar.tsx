'use client'

import { Table } from '@tanstack/react-table'

import { X } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface QuantityDayTableToolbarProps<TData> {
  table: Table<TData>
  storesData?: { id: string; name: string }[]
  selectedStoreUid?: string
  onStoreChange?: (storeUid: string) => void
  selectedItemUid?: string
  onItemChange?: (itemUid: string) => void
  itemsData?: Array<{ id: string; item_name: string; ots_price: number }>
}

export function QuantityDayTableToolbar<TData>({
  table,
  storesData = [],
  selectedStoreUid = 'all',
  onStoreChange,
  selectedItemUid = 'all',
  onItemChange,
  itemsData = []
}: QuantityDayTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder='Tìm kiếm ...'
          value={(table.getColumn('appliedItems')?.getFilterValue() as string) ?? ''}
          onChange={event => table.getColumn('appliedItems')?.setFilterValue(event.target.value)}
          className='h-8 w-[150px] lg:w-[250px]'
        />

        {/* Store Filter */}
        <Select value={selectedStoreUid} onValueChange={onStoreChange}>
          <SelectTrigger className='h-8 w-[180px]'>
            <SelectValue placeholder='Chọn cửa hàng' />
          </SelectTrigger>
          <SelectContent>
            {storesData.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Items Filter */}
        <Select value={selectedItemUid} onValueChange={onItemChange}>
          <SelectTrigger className='h-8 w-[200px]'>
            <SelectValue placeholder='Chọn món' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='all'>Tất cả món</SelectItem>
            {itemsData?.map(item => (
              <SelectItem key={item.id} value={item.id}>
                {item.item_name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {isFiltered && (
          <Button variant='ghost' onClick={() => table.resetColumnFilters()} className='h-8 px-2 lg:px-3'>
            Reset
            <X className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
    </div>
  )
}
