import { useState } from 'react'

import { IconChevronLeft, IconChevronRight } from '@tabler/icons-react'

import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

interface VietnameseDatePickerProps {
  selectedMonth: string
  onMonthChange: (month: string) => void
  className?: string
}

const vietnameseMonths = [
  'Tháng 1',
  'Tháng 2',
  'Tháng 3',
  'Tháng 4',
  'Tháng 5',
  'Tháng 6',
  'Tháng 7',
  'Tháng 8',
  'Tháng 9',
  'Tháng 10',
  'Tháng 11',
  'Tháng 12'
]

export function VietnameseDatePicker({ selectedMonth, onMonthChange, className }: VietnameseDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [currentYear, setCurrentYear] = useState(() => {
    const [year] = selectedMonth.split('-')
    return parseInt(year)
  })

  const [currentMonth, setCurrentMonth] = useState(() => {
    const [, month] = selectedMonth.split('-')
    return parseInt(month) - 1 // Calendar months are 0-indexed
  })

  const handleMonthChange = (month: number) => {
    setCurrentMonth(month)
    const formattedMonth = String(month + 1).padStart(2, '0')
    const newMonthString = `${currentYear}-${formattedMonth}`
    onMonthChange(newMonthString)
    setIsOpen(false)
  }

  const handleYearChange = (year: number) => {
    setCurrentYear(year)
    const formattedMonth = String(currentMonth + 1).padStart(2, '0')
    const newMonthString = `${year}-${formattedMonth}`
    onMonthChange(newMonthString)
  }

  const formatDisplayValue = (monthString: string) => {
    const [year, month] = monthString.split('-')
    return `${month}/${year}`
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          className={cn(
            'w-[200px] justify-start text-left font-normal',
            !selectedMonth && 'text-muted-foreground',
            className
          )}
        >
          {selectedMonth ? formatDisplayValue(selectedMonth) : 'Chọn tháng'}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0' align='start'>
        <div className='p-3'>
          <div className='mb-2 flex items-center justify-between'>
            <Button variant='ghost' size='sm' onClick={() => handleYearChange(currentYear - 1)}>
              <IconChevronLeft className='h-4 w-4' />
            </Button>
            <span className='text-sm font-medium'>{currentYear}</span>
            <Button variant='ghost' size='sm' onClick={() => handleYearChange(currentYear + 1)}>
              <IconChevronRight className='h-4 w-4' />
            </Button>
          </div>
          <div className='grid grid-cols-3 gap-1'>
            {vietnameseMonths.map((month, index) => (
              <Button
                key={index}
                variant={currentMonth === index ? 'default' : 'ghost'}
                size='sm'
                onClick={() => handleMonthChange(index)}
                className='text-xs'
              >
                {month}
              </Button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
