import React, { useState, useMemo } from 'react'

import { usePosCitiesData } from '@/hooks'
import { usePosStores } from '@/stores'
import { ChevronDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import {
  Button,
  Checkbox,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui'

interface StoreSelectionDropdownProps {
  selectedCities: string[]
  selectedStores: string[]
  onCitiesChange: (cities: string[]) => void
  onStoresChange: (stores: string[]) => void
  className?: string
}

export const StoreSelectionDropdown: React.FC<StoreSelectionDropdownProps> = ({
  selectedCities,
  selectedStores,
  onCitiesChange,
  onStoresChange,
  className
}) => {
  const [open, setOpen] = useState(false)
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())

  const { cities } = usePosCitiesData()
  const { currentBrandStores } = usePosStores()

  const getStoresByCity = (cityId: string) => {
    return currentBrandStores.filter(store => store.city_uid === cityId)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) newExpanded.delete(cityId)
    if (!newExpanded.has(cityId)) newExpanded.add(cityId)

    setExpandedCities(newExpanded)
  }

  const handleCityChange = (cityId: string, checked: boolean) => {
    if (checked) {
      onCitiesChange([...selectedCities, cityId])
      const cityStores = getStoresByCity(cityId)
      const cityStoreIds = cityStores.map(store => store.id)
      // Remove 'all-stores' when selecting specific cities/stores
      const currentStores = selectedStores.filter(id => id !== 'all-stores')
      const newSelectedStores = [...new Set([...currentStores, ...cityStoreIds])]
      onStoresChange(newSelectedStores)
    }
    if (!checked) {
      onCitiesChange(selectedCities.filter(id => id !== cityId))
      const cityStores = getStoresByCity(cityId)
      const cityStoreIds = cityStores.map(store => store.id)
      const newSelectedStores = selectedStores.filter(id => !cityStoreIds.includes(id))
      // If no stores left, add 'all-stores' back
      if (newSelectedStores.length === 0) {
        onStoresChange(['all-stores'])
      } else {
        onStoresChange(newSelectedStores)
      }
    }
  }

  const handleStoreChange = (storeId: string, checked: boolean) => {
    if (checked) {
      const newStores = selectedStores.filter(id => id !== 'all-stores')
      onStoresChange([...newStores, storeId])
    }
    if (!checked) {
      const newStores = selectedStores.filter(id => id !== storeId)
      if (newStores.length === 0) {
        onStoresChange(['all-stores'])
      } else {
        onStoresChange(newStores)
      }
    }
  }

  const isCitySelected = (cityId: string) => {
    return selectedCities.includes(cityId)
  }

  const isCityPartiallySelected = (cityId: string) => {
    const cityStores = getStoresByCity(cityId)
    const selectedCityStores = cityStores.filter(store => selectedStores.includes(store.id))
    return selectedCityStores.length > 0 && selectedCityStores.length < cityStores.length
  }

  const displayText = useMemo(() => {
    const isAllStoresSelected = selectedStores.includes('all-stores')
    const actualSelectedStores = selectedStores.filter(store => store !== 'all-stores')
    const totalSelected = selectedCities.length + actualSelectedStores.length

    if (isAllStoresSelected && totalSelected === 0) {
      return 'Tất cả cửa hàng'
    }
    if (totalSelected === 0) {
      return 'Chọn cửa hàng'
    }
    if (selectedCities.length > 0 && actualSelectedStores.length === 0) {
      return `${selectedCities.length} thành phố`
    }
    if (selectedCities.length === 0 && actualSelectedStores.length > 0) {
      return `${actualSelectedStores.length} cửa hàng`
    }
    return `${selectedCities.length} thành phố, ${actualSelectedStores.length} cửa hàng`
  }, [selectedCities.length, selectedStores])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className={cn('w-full justify-between', className)}
        >
          <span className='truncate'>{displayText}</span>
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-80 p-0' align='start'>
        <div className='max-h-96 overflow-y-auto'>
          <div className='p-2'>
            <div className='text-muted-foreground mb-2 text-sm font-medium'>Chọn theo thành phố và cửa hàng</div>

            {cities
              .filter(city => {
                const cityId = city.id || city.city_id
                const cityStores = getStoresByCity(cityId)
                return cityStores.length > 0
              })
              .map(city => {
                const cityId = city.id || city.city_id
                const cityStores = getStoresByCity(cityId)
                const isExpanded = expandedCities.has(cityId)
                const isSelected = isCitySelected(cityId)
                const isPartiallySelected = isCityPartiallySelected(cityId)

                return (
                  <div key={cityId} className='mb-2'>
                    <Collapsible open={isExpanded} onOpenChange={() => toggleCityExpansion(cityId)}>
                      <div className='hover:bg-accent flex items-center space-x-2 rounded-md p-2'>
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={checked => handleCityChange(cityId, checked as boolean)}
                          className={isPartiallySelected ? 'data-[state=unchecked]:bg-muted' : ''}
                        />
                        <CollapsibleTrigger asChild>
                          <Button variant='ghost' size='sm' className='h-auto p-0 font-medium'>
                            <ChevronDown
                              className={cn('mr-2 h-4 w-4 transition-transform', isExpanded ? 'rotate-180' : '')}
                            />
                            {city.city_name} ({cityStores.length})
                          </Button>
                        </CollapsibleTrigger>
                      </div>
                      <CollapsibleContent className='ml-6'>
                        <div className='space-y-1'>
                          {cityStores.map(store => (
                            <div key={store.id} className='hover:bg-accent flex items-center space-x-2 rounded-md p-2'>
                              <Checkbox
                                checked={selectedStores.includes(store.id)}
                                onCheckedChange={checked => handleStoreChange(store.id, checked as boolean)}
                              />
                              <span className='text-sm'>{store.store_name}</span>
                            </div>
                          ))}
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  </div>
                )
              })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
