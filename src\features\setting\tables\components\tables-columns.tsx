import { useState } from 'react'

import { ColumnDef } from '@tanstack/react-table'

import { Trash2 } from 'lucide-react'

import type { Table } from '@/lib/tables-api'

import { useToggleTableStatus, useDeleteTable } from '@/hooks/api/use-tables'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'

import { ConfirmModal, StatusBadge } from '@/components/pos'

import { useItemsInStoreForTable } from '@/features/menu/items/items-in-store/hooks'

function getStoreName(storeUid: string): string {
  try {
    const posStoresData = localStorage.getItem('pos_stores_data')
    if (posStoresData) {
      const stores = JSON.parse(posStoresData)
      const store = stores.find((s: any) => s.id === storeUid)
      return store?.store_name || 'Không xác định'
    }
    return 'Không xác định'
  } catch (error) {
    return 'Không xác định'
  }
}

function ActionsCell({ table }: { table: Table }) {
  const { toggleTableStatus, isToggling } = useToggleTableStatus()

  const handleToggleStatus = () => {
    toggleTableStatus(table)
  }

  return (
    <div className='flex items-center justify-center gap-2'>
      <button
        onClick={handleToggleStatus}
        disabled={isToggling}
        className='cursor-pointer disabled:cursor-not-allowed disabled:opacity-50'
      >
        <StatusBadge isActive={table.active === 1} activeText='Active' inactiveText='Deactive' />
      </button>
    </div>
  )
}

function DeleteCell({ table, storeUid }: { table: Table; storeUid: string }) {
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const { deleteTable, isDeleting } = useDeleteTable(storeUid)

  const handleDeleteClick = () => {
    setConfirmModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    try {
      await deleteTable(table.id)
      setConfirmModalOpen(false)
    } catch (error) {}
  }

  return (
    <>
      <div className='flex items-center justify-center'>
        <Button
          variant='ghost'
          size='sm'
          className='h-8 w-8 p-0 text-red-500 hover:bg-red-50 hover:text-red-700'
          onClick={handleDeleteClick}
          disabled={isDeleting}
        >
          <Trash2 className='h-4 w-4' />
        </Button>
      </div>

      <ConfirmModal
        open={confirmModalOpen}
        onOpenChange={setConfirmModalOpen}
        content={`Bạn có muốn xóa bàn "${table.table_name}"?`}
        confirmText='Xóa'
        onConfirm={handleConfirmDelete}
        isLoading={isDeleting}
      />
    </>
  )
}

// Component to display pre-orders with item names
function PreOrdersCell({ table }: { table: Table }) {
  const orderList = table.extra_data?.order_list || []

  const { data: items = [] } = useItemsInStoreForTable({
    params: {
      store_uid: table.store_uid
    },
    enabled: !!table.store_uid && orderList.length > 0
  })

  if (orderList.length === 0) {
    return (
      <div className='text-center'>
        <span className='text-sm text-gray-500'>-</span>
      </div>
    )
  }

  const orderDetails = orderList
    .map((order: { item_id: string; quantity: number }) => {
      const foundItem = items.find(item => item.originalData?.item_id === order.item_id)
      const itemName = foundItem?.name || order.item_id
      return `${itemName} x ${order.quantity}`
    })
    .join(', ')

  return (
    <div className='text-center'>
      <span className='text-sm'>{orderDetails}</span>
    </div>
  )
}

export const createTablesColumns = (storeUid: string): ColumnDef<Table>[] => [
  {
    id: 'select',
    header: ({ table }) => (
      <div className='flex justify-center'>
        <Checkbox
          checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
          onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
          aria-label='Select all'
          className='translate-y-[2px]'
        />
      </div>
    ),
    cell: ({ row }) => (
      <div className='flex justify-center'>
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={value => row.toggleSelected(!!value)}
          aria-label='Select row'
          className='translate-y-[2px]'
        />
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
    size: 50
  },
  {
    id: 'index',
    header: () => <div className='text-center'>#</div>,
    cell: ({ row }) => <div className='w-[50px] text-center font-medium'>{row.index + 1}</div>,
    enableSorting: false,
    enableHiding: false,
    size: 60
  },
  {
    accessorKey: 'table_name',
    header: () => <div className='text-center'>Tên bàn</div>,
    cell: ({ row }) => (
      <div className='text-center'>
        <span className='font-medium'>{row.getValue('table_name')}</span>
      </div>
    ),
    size: 200
  },
  {
    accessorKey: 'table_id',
    header: () => <div className='text-center'>Mã bàn</div>,
    cell: ({ row }) => (
      <div className='text-center'>
        <span className='font-mono text-sm'>{row.getValue('table_id')}</span>
      </div>
    ),
    size: 150
  },
  {
    id: 'applied_stores',
    header: () => <div className='text-center'>Điểm áp dụng</div>,
    cell: ({ row }) => {
      const table = row.original
      const storeName = getStoreName(table.store_uid || '')
      return (
        <div className='text-center'>
          <span className='text-sm'>{storeName}</span>
        </div>
      )
    },
    enableSorting: false,
    size: 120
  },
  {
    id: 'area_name',
    header: () => <div className='text-center'>Khu vực</div>,
    cell: ({ row }) => {
      const table = row.original
      return (
        <div className='text-center'>
          <span className='text-sm'>{table.area?.area_name || 'Không xác định'}</span>
        </div>
      )
    },
    enableSorting: false,
    size: 120
  },
  {
    id: 'pre_orders',
    header: () => <div className='text-center'>Món đặt trước</div>,
    cell: ({ row }) => <PreOrdersCell table={row.original} />,
    enableSorting: false,
    size: 150
  },
  {
    id: 'actions',
    header: () => <div className='text-center'>Thao tác</div>,
    cell: ({ row }) => <ActionsCell table={row.original} />,
    enableSorting: false,
    size: 100
  },
  {
    id: 'delete',
    header: () => <div className='text-center'></div>,
    cell: ({ row }) => <DeleteCell table={row.original} storeUid={storeUid} />,
    enableSorting: false,
    size: 60
  }
]
