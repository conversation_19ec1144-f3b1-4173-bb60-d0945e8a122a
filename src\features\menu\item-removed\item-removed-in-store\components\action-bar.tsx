import { IconDownload, IconChevronDown } from '@tabler/icons-react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { Button, Input } from '@/components/ui'

interface Store {
  id: string
  name: string
}

interface ActionBarProps {
  searchQuery: string
  onSearchQueryChange: (value: string) => void
  onSearchSubmit: (searchTerm: string) => void
  selectedStoreId: string
  onStoreChange: (storeId: string) => void
  stores: Store[]
  isExporting: boolean
  onExportBySelectedStore: () => void
  onExportAllStores: () => void
}

export function ActionBar({
  searchQuery,
  onSearchQueryChange,
  onSearchSubmit,
  selectedStoreId,
  onStoreChange,
  stores,
  isExporting,
  onExportBySelectedStore,
  onExportAllStores
}: ActionBarProps) {
  return (
    <div className='mb-2 flex items-center justify-between'>
      <div className='flex items-center gap-4'>
        <h2 className='text-xl font-semibold'>Món đã xóa tại cửa hàng</h2>
        <Input
          placeholder='Tìm kiếm món đã xóa...'
          className='w-64'
          value={searchQuery}
          onChange={e => onSearchQueryChange(e.target.value)}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.preventDefault()
              onSearchSubmit(searchQuery)
            }
          }}
        />
        <Select value={selectedStoreId} onValueChange={onStoreChange}>
          <SelectTrigger className='w-48'>
            <SelectValue placeholder='Chọn cửa hàng' />
          </SelectTrigger>
          <SelectContent>
            {stores.map(store => (
              <SelectItem key={store.id} value={store.id}>
                {store.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button size='sm' disabled={isExporting}>
            <IconDownload className='mr-2 h-4 w-4' />
            {isExporting ? 'Đang xuất...' : 'Xuất báo cáo'}
            <IconChevronDown className='ml-2 h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuItem onClick={onExportBySelectedStore}>
            <IconDownload className='mr-2 h-4 w-4' />
            Xuất báo cáo theo cửa hàng đã chọn
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onExportAllStores}>
            <IconDownload className='mr-2 h-4 w-4' />
            Xuất báo cáo tất cả cửa hàng
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
