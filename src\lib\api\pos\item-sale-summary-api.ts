import type { 
  ItemSaleSummaryParams, 
  ItemSaleSummaryResponse 
} from '@/types/api/revenue-promotion'

import { apiClient } from './pos-api'

export const getItemSaleSummary = async (
  params: ItemSaleSummaryParams
): Promise<ItemSaleSummaryResponse> => {
  const response = await apiClient.get<ItemSaleSummaryResponse>(
    '/v1/reports/sale-summary/items',
    { params }
  )
  return response.data
}
