import { useState, useEffect } from 'react'
import { useRoleById } from '@/hooks/api/use-roles'

interface UseRoleFormProps {
  roleId?: string
  copyFromRoleId?: string
}

export const useRoleForm = ({ roleId, copyFromRoleId }: UseRoleFormProps = {}) => {
  const [roleName, setRoleName] = useState('')
  const [description, setDescription] = useState('')

  const isEditMode = !!roleId
  const isCopyMode = !!copyFromRoleId

  const { data: roleData, isLoading } = useRoleById(roleId || '', isEditMode)
  const { data: copyFromRoleData, isLoading: isCopyDataLoading } = useRoleById(copyFromRoleId || '', isCopyMode)

  useEffect(() => {
    if (isEditMode && roleData) {
      setRoleName(roleData.role_name || '')
      setDescription(roleData.description || '')
    } else if (isCopyMode && copyFromRoleData) {
      // For copy mode, prepend "Copy of " to the role name
      setRoleName(`Sao chép của ${copyFromRoleData.role_name || ''}`)
      setDescription(copyFromRoleData.description || '')
    }
  }, [isEditMode, roleData, isCopyMode, copyFromRoleData])

  const isFormValid = roleName.trim() !== '' && description.trim() !== ''

  const resetForm = () => {
    setRoleName('')
    setDescription('')
  }

  return {
    roleName,
    setRoleName,
    description,
    setDescription,
    isFormValid,
    resetForm,
    isEditMode,
    isCopyMode,
    roleData,
    copyFromRoleData,
    isLoading: isLoading || isCopyDataLoading
  }
}
