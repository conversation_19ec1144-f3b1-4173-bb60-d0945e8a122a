export interface SystemLog {
  id: string
  pos_parent: string
  user_id: string
  user_name: string
  request_path: string
  request_data: string
  request_at: string
  response_at: string
  response: string
}

export interface SystemLogFilters {
  dateRange: {
    from: Date | null
    to: Date | null
  }
  request_path: string
  user_name: string
}

export interface SystemLogApiResponse {
  data: {
    data: SystemLog[]
    count: number
    totalPage: number
    page: number
    pageSize: number
  }
}

// Mock data based on actual API response
export const MOCK_SYSTEM_LOGS: SystemLog[] = [
  {
    id: '689bf187de86ec0001ef91b8',
    pos_parent: 'BRAND-953H',
    user_id: '92930',
    user_name: '<EMAIL>',
    request_path: '/crm/setting/crm_config/update',
    request_data: 'params: pos_parent=BRAND-953H&access_token=&session_token=',
    request_at: '2025-08-13 08:59:34',
    response_at: '2025-08-13 08:59:35',
    response: '250813-085934-076016'
  },
  {
    id: '689bf160de86ec0001ef91b7',
    pos_parent: 'BRAND-953H',
    user_id: '92930',
    user_name: '<EMAIL>',
    request_path: '/crm/setting/crm_config/update',
    request_data: 'params: pos_parent=BRAND-953H&access_token=&session_token=',
    request_at: '2025-08-13 08:58:56',
    response_at: '2025-08-13 08:58:56',
    response: '250813-085856-321062'
  },
  {
    id: '689bf160de86ec0001ef91b5',
    pos_parent: 'BRAND-953H',
    user_id: '92930',
    user_name: '<EMAIL>',
    request_path: '/crm/setting/update_pos_parent',
    request_data: 'pos_parent=BRAND-953H&access_token=&session_token=',
    request_at: '2025-08-13 08:58:56',
    response_at: '2025-08-13 08:58:56',
    response: '1'
  },
  {
    id: '689bf160de86ec0001ef91b6',
    pos_parent: 'BRAND-953H',
    user_id: '92930',
    user_name: '<EMAIL>',
    request_path: '/crm/setting/update_pos_parent',
    request_data:
      'params: pos_parent=BRAND-953H&access_token=&session_token=&pos_parent=BRAND-953H&access_token=&session_token=d32367a00bbe63be56920bfe28fbe35204447296',
    request_at: '2025-08-13 08:58:56',
    response_at: '2025-08-13 08:58:56',
    response: '250813-085856-215811'
  },
  {
    id: '689bf1449c46170001a9c7d4',
    pos_parent: 'BRAND-953H',
    user_id: '92930',
    user_name: '<EMAIL>',
    request_path: '/crm/setting/update_pos_parent',
    request_data: 'pos_parent=BRAND-953H&access_token=&session_token=',
    request_at: '2025-08-13 08:58:28',
    response_at: '2025-08-13 08:58:28',
    response: '1'
  },
  {
    id: '689bf1449c46170001a9c7d5',
    pos_parent: 'BRAND-953H',
    user_id: '92930',
    user_name: '<EMAIL>',
    request_path: '/crm/setting/update_pos_parent',
    request_data:
      'params: pos_parent=BRAND-953H&access_token=&session_token=&pos_parent=BRAND-953H&access_token=&session_token=d32367a00bbe63be56920bfe28fbe35204447296',
    request_at: '2025-08-13 08:58:28',
    response_at: '2025-08-13 08:58:28',
    response: '250813-085828-326940'
  }
]

// Generate more mock data for pagination testing
export const generateMockLogs = (count: number): SystemLog[] => {
  const requestPaths = [
    '/crm/setting/crm_config/update',
    '/crm/setting/update_pos_parent',
    '/crm/setting/update-setting-config',
    '/crm/setting/update-setting-loyalty',
    '/crm/setting/update-cheat-config'
  ]

  const users = [
    { id: '92930', name: '<EMAIL>' },
    { id: '92931', name: '<EMAIL>' },
    { id: '92932', name: '<EMAIL>' },
    { id: '92933', name: '<EMAIL>' }
  ]

  const logs: SystemLog[] = []

  for (let i = 0; i < count; i++) {
    const randomPath = requestPaths[Math.floor(Math.random() * requestPaths.length)]
    const randomUser = users[Math.floor(Math.random() * users.length)]
    const randomDate = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Random date within last 30 days
    const dateStr = randomDate.toISOString().slice(0, 19).replace('T', ' ')

    logs.push({
      id: `mock-${i + 7}`,
      pos_parent: 'BRAND-953H',
      user_id: randomUser.id,
      user_name: randomUser.name,
      request_path: randomPath,
      request_data: `params: pos_parent=BRAND-953H&access_token=&session_token=`,
      request_at: dateStr,
      response_at: dateStr,
      response: `${dateStr.replace(/[-:\s]/g, '').slice(2)}-${Math.floor(Math.random() * 999999)
        .toString()
        .padStart(6, '0')}`
    })
  }

  return logs
}

export const ALL_MOCK_LOGS = [...MOCK_SYSTEM_LOGS, ...generateMockLogs(136)] // Total 156 logs for pagination
