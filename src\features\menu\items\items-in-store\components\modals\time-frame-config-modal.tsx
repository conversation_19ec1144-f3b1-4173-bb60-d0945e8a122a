import { useMemo, useState } from 'react'

import { Search, Check } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

import { useStoresData, useItemTypesData } from '@/hooks/api'
import { useItemsInStoreData, useBulkUpdateItemsInStore } from '../../hooks'
import type { ItemsInStore } from '../../data'

interface TimeFrameConfigModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function TimeFrameConfigModal({ open, onOpenChange }: TimeFrameConfigModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('all')
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('')
  const [selectedDaysBits, setSelectedDaysBits] = useState<number>(0)
  const [selectedHoursBits, setSelectedHoursBits] = useState<number>(0)
  // Pending per-item action: 'selected' -> set to selected bits, 'zero' -> set both to 0
  const [pendingActions, setPendingActions] = useState<Record<string, 'selected' | 'zero'>>({})

  // Stores and item types
  const { data: stores = [] } = useStoresData({ enabled: open })
  const { data: itemTypes = [] } = useItemTypesData({
    ...(selectedStoreUid && selectedStoreUid !== 'all' ? { store_uid: selectedStoreUid } : {}),
    enabled: open && !!selectedStoreUid
  })

  // Items
  const { data: items = [], isLoading } = useItemsInStoreData({
    params: {
      ...(selectedStoreUid ? { store_uid: selectedStoreUid } : {}),
      ...(selectedItemTypeUid !== 'all' && selectedItemTypeUid ? { item_type_uid: selectedItemTypeUid } : {}),
      active: '1'
    },
    enabled: open && !!selectedStoreUid
  })

  const filteredItems = useMemo(() => {
    const q = searchTerm.trim().toLowerCase()
    if (!q) return items
    return items
      .filter((item: ItemsInStore) => item.active === 1)
      .filter((item: ItemsInStore) =>
        item.item_name.toLowerCase().includes(q) || (item.item_id || '').toLowerCase().includes(q)
      )
  }, [items, searchTerm])

  // Reset state when closing
  const handleOpenChange = (isOpen: boolean) => {
    onOpenChange(isOpen)
    if (!isOpen) {
      setTimeout(() => {
        setSearchTerm('')
        setSelectedItemTypeUid('all')
        setSelectedStoreUid('')
        setSelectedDaysBits(0)
        setSelectedHoursBits(0)
        setPendingActions({})
      }, 300)
    }
  }

  const toggleBit = (current: number, bitValue: number) => {
    const isSelected = (current & bitValue) !== 0
    return isSelected ? current & ~bitValue : current | bitValue
  }

  const bulkUpdateMutation = useBulkUpdateItemsInStore()

  const handleSave = () => {
    if (!selectedStoreUid || Object.keys(pendingActions).length === 0) {
      onOpenChange(false)
      return
    }

    // Prepare items payload only for items that have a pending action
    const payload = items
      .filter((item: ItemsInStore) => pendingActions[item.id])
      .map((item: ItemsInStore) => {
        const action = pendingActions[item.id]
        if (action === 'selected') {
          return {
            ...item,
            time_sale_date_week: Number(selectedDaysBits || 0),
            time_sale_hour_day: Number(selectedHoursBits || 0)
          }
        }
        // action === 'zero'
        return {
          ...item,
          time_sale_date_week: 0,
          time_sale_hour_day: 0
        }
      })

    if (payload.length === 0) {
      onOpenChange(false)
      return
    }

    bulkUpdateMutation.mutate(payload as any, {
      onSuccess: () => {
        setPendingActions({})
        onOpenChange(false)
      }
    })
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="h-[90vh] max-w-7xl sm:max-w-5xl flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-center">Cấu hình khung giờ bán hàng</DialogTitle>
        </DialogHeader>

        {/* Warning */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-2 flex-shrink-0">
          <p className="text-sm text-yellow-800">
            Các món tại thành phố bị thay đổi vị trí sẽ chuyển thành món tại cửa hàng
          </p>
        </div>

        {/* Filters */}
        <div className="flex items-center gap-3 flex-shrink-0">
          <div className="relative flex-1">
            <Input
              placeholder="Nhập tên hoặc mã món"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          </div>

          <div className="w-56">
            <Select value={selectedItemTypeUid} onValueChange={setSelectedItemTypeUid}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn nhóm món" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả nhóm món</SelectItem>
                {itemTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>{type.item_type_name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="w-56">
            <Select value={selectedStoreUid} onValueChange={setSelectedStoreUid}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn điểm áp dụng" />
              </SelectTrigger>
              <SelectContent>
                {stores.map((store) => (
                  <SelectItem key={store.id} value={store.id}>{store.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Days selection */}
        <div className="mt-3 space-y-2 flex-shrink-0">
          <div className="grid grid-cols-7 gap-2">
            {[
              { name: 'Thứ 2', bit: 4 },
              { name: 'Thứ 3', bit: 8 },
              { name: 'Thứ 4', bit: 16 },
              { name: 'Thứ 5', bit: 32 },
              { name: 'Thứ 6', bit: 64 },
              { name: 'Thứ 7', bit: 128 },
              { name: 'Chủ nhật', bit: 2 }
            ].map(({ name, bit }) => {
              const isSelected = (selectedDaysBits & bit) !== 0
              return (
                <Button
                  key={name}
                  type="button"
                  variant={isSelected ? 'default' : 'outline'}
                  size="sm"
                  className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                  onClick={() => setSelectedDaysBits((v) => toggleBit(v, bit))}
                >
                  {name}
                </Button>
              )
            })}
          </div>
          <div className="grid grid-cols-10 gap-2">
            {Array.from({ length: 24 }, (_, i) => ({ hour: i, label: `${i}h` })).map(({ hour, label }) => {
              const bitValue = 1 << hour
              const isSelected = (selectedHoursBits & bitValue) !== 0
              return (
                <Button
                  key={hour}
                  type="button"
                  variant={isSelected ? 'default' : 'outline'}
                  size="sm"
                  className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                  onClick={() => setSelectedHoursBits((v) => toggleBit(v, bitValue))}
                >
                  {label}
                </Button>
              )
            })}
          </div>
        </div>

        {/* Items area */}
        <div className="flex-1 min-h-0 mt-3">
          {!selectedStoreUid ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <p>Chọn cửa hàng để cấu hình thời gian bán món ăn</p>
            </div>
          ) : (
            <div className="h-full border rounded-md">
              <ScrollArea className="h-full w-full">
                <div className="p-3 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                  {isLoading ? (
                    <div className="col-span-full text-center py-10 text-muted-foreground">Đang tải...</div>
                  ) : filteredItems.length === 0 ? (
                    <div className="col-span-full text-center py-10 text-muted-foreground">Không có món ăn</div>
                  ) : (
                    filteredItems.map((item: ItemsInStore) => {
                      const isGreenTick =
                        Number(item.time_sale_date_week || 0) === Number(selectedDaysBits || 0) &&
                        Number(item.time_sale_hour_day || 0) === Number(selectedHoursBits || 0)

                      const pending = pendingActions[item.id]

                      const handleItemToggle = () => {
                        setPendingActions(prev => {
                          const current = prev[item.id]
                          if (!isGreenTick) {
                            // Cycle: none -> selected -> zero -> none
                            if (!current) return { ...prev, [item.id]: 'selected' }
                            if (current === 'selected') return { ...prev, [item.id]: 'zero' }
                            const { [item.id]: _, ...rest } = prev
                            return rest
                          } else {
                            // From green: green -> yellow -> green
                            if (!current) return { ...prev, [item.id]: 'zero' }
                            // current is 'zero' => back to green (remove)
                            const { [item.id]: _, ...rest } = prev
                            return rest
                          }
                        })
                      }

                      return (
                        <button
                          type="button"
                          key={item.id}
                          onClick={handleItemToggle}
                          className="relative rounded-md border bg-neutral-800 text-white p-3 h-28 flex flex-col justify-center text-left"
                        >
                          {/* Overlay ticks */}
                          {isGreenTick && !pending && (
                            <div className="absolute right-2 top-2 h-6 w-6 rounded-full bg-green-500 flex items-center justify-center">
                              <Check className="h-4 w-4 text-white" />
                            </div>
                          )}
                          {pending === 'selected' && (
                            <div className="absolute right-2 top-2 h-6 w-6 rounded-full bg-green-500 flex items-center justify-center">
                              <Check className="h-4 w-4 text-white" />
                            </div>
                          )}
                          {pending === 'zero' && (
                            <div className="absolute right-2 top-2 h-6 w-6 rounded-full bg-yellow-500 flex items-center justify-center">
                              <Check className="h-4 w-4 text-white" />
                            </div>
                          )}

                          <div className="text-sm font-medium truncate" title={item.item_name}>{item.item_name}</div>
                          <div className="mt-2 text-xs opacity-80 truncate">{item.item_id}</div>
                          <div className="mt-1 text-sm font-semibold">
                            {(item.ots_price || 0).toLocaleString('vi-VN')} đ
                          </div>
                        </button>
                      )
                    })
                  )}
                </div>
                <ScrollBar orientation="vertical" />
              </ScrollArea>
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={() => handleOpenChange(false)}>Hủy</Button>
          <Button onClick={handleSave} disabled={!selectedStoreUid || bulkUpdateMutation.isPending || Object.keys(pendingActions).length === 0}>
            {bulkUpdateMutation.isPending ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}


