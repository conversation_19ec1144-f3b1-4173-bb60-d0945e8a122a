import { useState } from 'react'
import { toast } from 'sonner'

import { useBulkImportAreas } from '@/hooks/api/use-areas'

import type { ParsedTableData } from './use-tables-import-excel-parser'

interface AreaToCreate {
  area_name: string
  description?: string
}

interface AreaMapping {
  [area_name: string]: string 
}

export function useAutoCreateAreas() {
  const [isCreatingAreas, setIsCreatingAreas] = useState(false)
  const bulkImportAreasMutation = useBulkImportAreas()

  /**
   * Kiểm tra và tự động tạo các khu vực chưa tồn tại
   * @param storeUid - ID của cửa hàng
   * @param parsedTables - Dữ liệu bàn đã parse từ file
   * @param existingAreas - Danh sách khu vực hiện có
   * @returns Promise<AreaMapping> - Mapping từ area_name sang area_uid
   */
  const checkAndCreateAreas = async (
    storeUid: string,
    parsedTables: ParsedTableData[],
    existingAreas: Array<{ id: string; area_name: string }>
  ): Promise<AreaMapping> => {
    try {
      setIsCreatingAreas(true)

      const importedAreaNames = Array.from(
        new Set(parsedTables.map(table => table.area_name.trim()).filter(Boolean))
      )

      const areaMapping: AreaMapping = {}
      const existingAreaNames = new Set<string>()

      existingAreas.forEach(area => {
        const normalizedName = area.area_name.trim()
        areaMapping[normalizedName] = area.id
        existingAreaNames.add(normalizedName)
      })

      const areasToCreate: AreaToCreate[] = importedAreaNames
        .filter(areaName => !existingAreaNames.has(areaName))
        .map(areaName => ({
          area_name: areaName,
          description: `Khu vực được tạo tự động từ import bàn`
        }))

      if (areasToCreate.length > 0) {
        toast.info(`Đang tạo ${areasToCreate.length} khu vực mới...`)

        const createdAreas = await bulkImportAreasMutation.mutateAsync({
          storeUid,
          areas: areasToCreate
        })

        createdAreas.forEach((area, index) => {
          const areaName = areasToCreate[index].area_name
          areaMapping[areaName] = area.id
        })

        toast.success(`Đã tạo thành công ${areasToCreate.length} khu vực mới!`)
      }

      return areaMapping
    } catch (error) {
      console.error('Error creating areas:', error)
      toast.error('Có lỗi xảy ra khi tạo khu vực. Vui lòng thử lại.')
      throw error
    } finally {
      setIsCreatingAreas(false)
    }
  }

  /**
   * Lấy danh sách khu vực sẽ được tạo mới
   * @param parsedTables - Dữ liệu bàn đã parse từ file
   * @param existingAreas - Danh sách khu vực hiện có
   * @returns Array<string> - Danh sách tên khu vực sẽ được tạo
   */
  const getAreasToCreate = (
    parsedTables: ParsedTableData[],
    existingAreas: Array<{ area_name: string }>
  ): string[] => {
    const importedAreaNames = Array.from(
      new Set(parsedTables.map(table => table.area_name.trim()).filter(Boolean))
    )

    const existingAreaNames = new Set(
      existingAreas.map(area => area.area_name.trim())
    )

    return importedAreaNames.filter(areaName => !existingAreaNames.has(areaName))
  }

  return {
    checkAndCreateAreas,
    getAreasToCreate,
    isCreatingAreas: isCreatingAreas || bulkImportAreasMutation.isPending
  }
}
