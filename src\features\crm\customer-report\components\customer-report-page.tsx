import { useState } from 'react'

import { useCustomerReport } from '@/hooks/crm'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export function CustomerReportPage() {
  const [dateRange, setDateRange] = useState({
    start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end_date: new Date().toISOString().split('T')[0]
  })

  const { data, isLoading, error, refetch, updateParams } = useCustomerReport({
    start_date: dateRange.start_date,
    end_date: dateRange.end_date
  })

  const handleDateChange = () => {
    updateParams({
      start_date: dateRange.start_date,
      end_date: dateRange.end_date
    })
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center space-y-4 py-8">
        <div className="text-center">
          <h3 className="text-destructive text-lg font-semibold">Lỗi khi tải dữ liệu</h3>
          <p className="text-muted-foreground text-sm">{error}</p>
          <Button onClick={refetch} className="mt-4">
            Thử lại
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto space-y-6 py-6">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold tracking-tight">Báo cáo khách hàng</h1>

        <Card>
          <CardHeader>
            <CardTitle>Chọn khoảng thời gian</CardTitle>
            <CardDescription>
              Chọn khoảng thời gian để xem báo cáo khách hàng
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-end space-x-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Từ ngày</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={dateRange.start_date}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start_date: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="end_date">Đến ngày</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={dateRange.end_date}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end_date: e.target.value }))}
                />
              </div>
              <Button onClick={handleDateChange} disabled={isLoading}>
                {isLoading ? 'Đang tải...' : 'Cập nhật'}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Biểu đồ số lượng khách đăng ký</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Đang tải dữ liệu...</div>
              </div>
            ) : data.registrationReport ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {data.registrationReport.member_register_count}
                    </div>
                    <div className="text-sm text-muted-foreground">Khách đăng ký</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {data.registrationReport.first_time_count}
                    </div>
                    <div className="text-sm text-muted-foreground">Lần đầu</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {data.registrationReport.second_times_count}
                    </div>
                    <div className="text-sm text-muted-foreground">Lần hai</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {data.registrationReport.three_and_above_times_count}
                    </div>
                    <div className="text-sm text-muted-foreground">Lần ba+</div>
                  </div>
                </div>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(data.registrationReport, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Không có dữ liệu
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Biểu đồ thay đổi hạng thành viên</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Đang tải dữ liệu...</div>
              </div>
            ) : data.membershipTypeStats ? (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">
                    {data.membershipTypeStats.total_count || 0}
                  </div>
                  <div className="text-sm text-muted-foreground">Tổng thay đổi</div>
                </div>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(data.membershipTypeStats, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Không có dữ liệu
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Biểu đồ giao dịch theo hạng thành viên</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Đang tải dữ liệu...</div>
              </div>
            ) : data.orderByMembershipType ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-emerald-600">
                      {data.orderByMembershipType.total_orders || 0}
                    </div>
                    <div className="text-sm text-muted-foreground">Tổng đơn hàng</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-rose-600">
                      {(data.orderByMembershipType.total_amount || 0).toLocaleString('vi-VN')} VNĐ
                    </div>
                    <div className="text-sm text-muted-foreground">Tổng doanh thu</div>
                  </div>
                </div>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(data.orderByMembershipType, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Không có dữ liệu
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
