import React, { useCallback } from 'react'

import DOMPurify from 'dompurify'
import parse from 'html-react-parser'
import QRCode from 'react-qr-code'

import { useSaveBillTemplate } from '../../hooks'
import { BillData, SaveBillTemplateRequest } from '../../types'
import { EditableField } from '../editable-field'

interface BillTemplate1Props {
  billData: BillData
  billTemplateData?: SaveBillTemplateRequest
}

export function BillTemplate1({ billData, billTemplateData }: BillTemplate1Props) {
  const saveBillTemplate = useSaveBillTemplate()

  const data = (billTemplateData?.extra_data || {}) as any

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      currency: 'VND'
    }).format(amount)
  }

  const handleFieldChange = useCallback(
    (fieldName: string, value: string) => {
      if (billTemplateData) {
        const updatedPayload: SaveBillTemplateRequest = {
          ...billTemplateData,
          revision: billTemplateData.revision + 1,
          updated_at: new Date().toISOString(),
          extra_data: {
            ...(billTemplateData.extra_data || {}),
            [fieldName]: value
          } as any
        }

        saveBillTemplate.mutate(updatedPayload)
      }
    },
    [billTemplateData, saveBillTemplate]
  )

  return (
    <div
      className={`relative border border-gray-300 bg-white font-sans shadow-md ${saveBillTemplate.isPending ? 'opacity-75' : ''}`}
      style={{
        width: data.enable_vat_rate || data.show_vat_amount ? '500px' : '415px',
        fontSize: `${(16 * (data.font_size || 100)) / 100}px`
      }}
    >
      {saveBillTemplate.isPending && (
        <div className='bg-opacity-50 absolute inset-0 z-10 flex items-center justify-center bg-white'>
          <div className='text-sm text-gray-600'>Đang lưu...</div>
        </div>
      )}
      <div className='px-4'>
        {/* Logo Section */}
        {billTemplateData?.logo && (
          <div className='mb-4 flex justify-center pt-5'>
            <div
              className='border border-gray-200 bg-contain bg-center bg-no-repeat'
              style={{
                backgroundImage: `url(${billTemplateData.logo})`,
                width: '410px',
                height: '165px'
              }}
            />
          </div>
        )}

        {/* Header */}
        <h3 className='mb-0 pt-3 text-center font-bold text-black'>
          <EditableField
            value={(billTemplateData?.extra_data as any)?.title_custom || 'HOÁ ĐƠN THANH TOÁN'}
            onSave={value => handleFieldChange('title_custom', value)}
            className='text-3xl font-bold'
          />
        </h3>
        <div className='mb-3 text-center font-bold text-black' style={{ fontSize: '1.3rem' }}>
          {data.is_group_source_and_tranno
            ? `${billData.orderSource} ${billData.billNumber}`
            : `Số HĐ: ${billData.billNumber}`}
        </div>

        {/* Transaction Details */}
        <div className='flex justify-between'>
          <div>
            <p className='mb-0'>
              <EditableField
                value={(billTemplateData?.extra_data as any)?.title_code || 'Mã HĐ'}
                onSave={value => handleFieldChange('title_code', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.billId}
            </p>
            <p className='mb-0'>
              <EditableField
                value={(billTemplateData?.extra_data as any)?.title_table || 'Bàn'}
                onSave={value => handleFieldChange('title_table', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.table}
            </p>
            <p className='mb-0'>
              <EditableField
                value={(billTemplateData?.extra_data as any)?.title_time_in || 'Giờ vào'}
                onSave={value => handleFieldChange('title_time_in', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.checkInTime}
            </p>
          </div>
          <div>
            <p className='mb-0'>
              <EditableField
                value={(billTemplateData?.extra_data as any)?.title_cashier || 'TN'}
                onSave={value => handleFieldChange('title_cashier', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.cashier}
            </p>
            <p className='mb-0'>
              <EditableField
                value={(billTemplateData?.extra_data as any)?.title_date || 'Ngày'}
                onSave={value => handleFieldChange('title_date', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.date}
            </p>
            <p className='mb-0'>
              <EditableField
                value={(billTemplateData?.extra_data as any)?.title_time_out || 'Giờ ra'}
                onSave={value => handleFieldChange('title_time_out', value)}
                className='font-bold text-gray-900'
              />
              <span className='mx-0 text-black'>: </span>
              {billData.checkOutTime}
            </p>
          </div>
        </div>

        {/* Items Table */}
        <div className='border-b border-dashed border-gray-300 pb-3'>
          <table
            className={`w-full border-collapse text-black ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
          >
            <thead>
              <tr>
                <th className='border border-gray-800 bg-gray-50 px-1 text-center text-sm font-bold'>
                  <EditableField
                    value={(billTemplateData?.extra_data as any)?.title_stt || 'TT'}
                    onSave={value => handleFieldChange('title_stt', value)}
                  />
                </th>
                <th className='border border-gray-800 bg-gray-50 px-1 text-center text-sm font-bold'>
                  <EditableField
                    value={(billTemplateData?.extra_data as any)?.title_name_item || 'Tên món'}
                    onSave={value => handleFieldChange('title_name_item', value)}
                  />
                </th>
                <th className='border border-gray-800 bg-gray-50 px-1 text-center text-sm font-bold'>
                  <EditableField
                    value={(billTemplateData?.extra_data as any)?.title_quantity || 'SL'}
                    onSave={value => handleFieldChange('title_quantity', value)}
                  />
                </th>
                {data.enable_topping && (
                  <th className='border border-gray-800 bg-gray-50 px-1 text-center text-sm font-bold'>
                    <EditableField
                      value={(billTemplateData?.extra_data as any)?.title_price || 'Đơn giá'}
                      onSave={value => handleFieldChange('title_price', value)}
                    />
                  </th>
                )}
                {data.enable_vat_rate && (
                  <th className='border border-gray-800 bg-gray-50 px-1 text-center text-sm font-bold'>
                    <EditableField
                      value={(billTemplateData?.extra_data as any)?.title_vat_rate || 'VAT'}
                      onSave={value => handleFieldChange('title_vat_rate', value)}
                    />
                  </th>
                )}
                {data.enable_vat_amount && (
                  <th className='border border-gray-800 bg-gray-50 px-1 text-center text-sm font-bold'>
                    <EditableField
                      value={(billTemplateData?.extra_data as any)?.title_vat_amount || 'Tiền VAT'}
                      onSave={value => handleFieldChange('title_vat_amount', value)}
                    />
                  </th>
                )}
                {data.enable_discount && (
                  <th className='border border-gray-800 bg-gray-50 px-1 text-center text-sm font-bold'>
                    <EditableField
                      value={(billTemplateData?.extra_data as any)?.title_discount || 'GG'}
                      onSave={value => handleFieldChange('title_discount', value)}
                    />
                  </th>
                )}
                <th className='border border-gray-800 bg-gray-50 px-1 text-right text-sm font-bold'>
                  <EditableField
                    value={(billTemplateData?.extra_data as any)?.title_amount || 'Thành tiền'}
                    onSave={value => handleFieldChange('title_amount', value)}
                  />
                </th>
              </tr>
            </thead>
            <tbody>
              {(() => {
                if (data.show_item_class) {
                  // Group items by itemType
                  const groupedItems = billData.items.reduce(
                    (groups, item) => {
                      const type = item.itemType || 'Không có loại'
                      if (!groups[type]) {
                        groups[type] = []
                      }
                      groups[type].push(item)
                      return groups
                    },
                    {} as Record<string, typeof billData.items>
                  )

                  let globalIndex = 1
                  return Object.entries(groupedItems).map(([itemType, items]) => {
                    const itemTypeTotal = items.reduce((total, item) => {
                      let itemTotal = item.subtotal
                      if (data.enable_topping && item.toppings) {
                        itemTotal += item.toppings.reduce((toppingTotal, topping) => toppingTotal + topping.subtotal, 0)
                      }
                      return total + itemTotal
                    }, 0)

                    return (
                      <React.Fragment key={itemType}>
                        {/* Item Type Header with Total */}
                        <tr>
                          <td
                            colSpan={
                              2 +
                              (data.show_discount ? 1 : 0) +
                              (data.enable_vat_rate ? 1 : 0) +
                              (data.show_vat_amount ? 1 : 0) +
                              1
                            }
                            className={`px-1 font-bold text-black ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                          >
                            {itemType}
                            {data.show_total_item_class_amount ? (
                              <span className='float-right'>{formatCurrency(itemTypeTotal)}</span>
                            ) : null}
                          </td>
                        </tr>
                        {/* Items in this type */}
                        {items.map(item => (
                          <tr key={item.id}>
                            <td
                              valign='top'
                              className={`min-h-8 px-1 ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                            >
                              {globalIndex++}
                            </td>
                            <td
                              valign='top'
                              className={`min-h-8 px-1 text-black ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                            >
                              <div>{item.name}</div>

                              {data.enable_topping &&
                                item.toppings &&
                                item.toppings
                                  .filter(topping => topping.quantity > 0)
                                  .map((topping, toppingIndex) => (
                                    <small key={toppingIndex} className='mb-0 ml-1 text-black'>
                                      + {topping.name}
                                    </small>
                                  ))}

                              {data.show_item_note && item.notes ? (
                                <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                                  <span className='underline'>Ghi chú</span>
                                  <span>: {item.notes}</span>
                                </div>
                              ) : null}
                            </td>
                            <td
                              valign='top'
                              className={`min-h-8 px-1 text-center text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                            >
                              <div>
                                {item.quantity}
                                {data.is_show_unit ? ` ${item.unit || 'MON'}` : ''}
                              </div>
                              {data.enable_topping &&
                                item.toppings &&
                                item.toppings
                                  .filter(topping => topping.quantity > 0)
                                  .map((topping, toppingIndex) => (
                                    <div key={toppingIndex} className='ml-4'>
                                      {topping.quantity}
                                      {data.is_show_unit ? ` ${topping.unit || 'MON'}` : ''}
                                    </div>
                                  ))}
                            </td>
                            <td
                              valign='top'
                              className={`min-h-8 px-1 text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                            >
                              <div>{formatCurrency(item.unitPrice)}</div>
                              {data.enable_topping &&
                                item.toppings &&
                                item.toppings
                                  .filter(topping => topping.quantity > 0)
                                  .map((topping, toppingIndex) => (
                                    <div key={toppingIndex} className='ml-4'>
                                      {formatCurrency(topping.unitPrice)}
                                    </div>
                                  ))}
                            </td>
                            {data.enable_vat_rate && (
                              <td
                                valign='top'
                                className={`min-h-8 px-1 text-center text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                              >
                                <div>{item.vatPercentage ? `${item.vatPercentage}%` : '10%'}</div>
                                {data.enable_topping &&
                                  item.toppings &&
                                  item.toppings
                                    .filter(topping => topping.quantity > 0)
                                    .map((topping, toppingIndex) => (
                                      <div key={toppingIndex} className='ml-4'>
                                        {topping.vatPercentage || 0}%
                                      </div>
                                    ))}
                              </td>
                            )}
                            {data.show_vat_amount && (
                              <td
                                valign='top'
                                className={`min-h-8 px-1 text-center text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                              >
                                <div>{formatCurrency(item.vatAmount || 0)}</div>
                                {data.enable_topping &&
                                  !data.enable_vat_rate &&
                                  item.toppings &&
                                  item.toppings
                                    .filter(topping => topping.quantity > 0)
                                    .map((topping, toppingIndex) => (
                                      <div key={toppingIndex} className='ml-4'>
                                        {formatCurrency(topping.vatAmount || 0)}
                                      </div>
                                    ))}
                              </td>
                            )}
                            {data.show_discount && (
                              <td
                                valign='top'
                                className={`min-h-8 px-1 text-center text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                              >
                                <div>{formatCurrency(item.discountAmount || 0)}</div>
                                {data.enable_topping &&
                                  item.toppings &&
                                  item.toppings
                                    .filter(topping => topping.quantity > 0)
                                    .map((topping, toppingIndex) => (
                                      <div key={toppingIndex} className='ml-4'>
                                        {formatCurrency(topping.discountAmount || 0)}
                                      </div>
                                    ))}
                              </td>
                            )}
                            <td
                              valign='top'
                              className={`min-h-8 px-1 text-right text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                            >
                              <div>
                                {formatCurrency(
                                  data.enable_discount
                                    ? item.subtotal - (item.subtotal * (item.discountPercentage || 10)) / 100
                                    : item.subtotal
                                )}
                              </div>
                              {data.enable_topping &&
                                item.toppings &&
                                item.toppings
                                  .filter(topping => topping.quantity > 0)
                                  .map((topping, toppingIndex) => (
                                    <div key={toppingIndex} className='ml-4'>
                                      {formatCurrency(topping.subtotal)}
                                    </div>
                                  ))}
                            </td>
                          </tr>
                        ))}
                      </React.Fragment>
                    )
                  })
                } else {
                  // Original rendering without grouping
                  return billData.items.map((item, index) => (
                    <tr key={item.id}>
                      <td
                        valign='top'
                        className={`min-h-8 px-1 ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                      >
                        {index + 1}
                      </td>
                      <td
                        valign='top'
                        className={`min-h-8 px-1 ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                      >
                        <div>{item.name}</div>

                        {data.enable_topping &&
                          item.toppings &&
                          item.toppings
                            .filter(topping => topping.quantity > 0)
                            .map((topping, toppingIndex) => (
                              <div key={toppingIndex} className='ml-4 text-sm'>
                                + {topping.name}
                              </div>
                            ))}

                        {data.show_item_note && item.notes ? (
                          <div className='text-xs text-black' style={{ fontSize: '11px' }}>
                            <span className='underline'>Ghi chú</span>
                            <span>: {item.notes}</span>
                          </div>
                        ) : null}
                      </td>
                      <td
                        valign='top'
                        className={`min-h-8 px-1 text-center text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                      >
                        <div>
                          {item.quantity}
                          {data.is_show_unit ? ` ${item.unit || 'MON'}` : ''}
                        </div>
                        {data.enable_topping &&
                          item.toppings &&
                          item.toppings
                            .filter(topping => topping.quantity > 0)
                            .map((topping, toppingIndex) => (
                              <div key={toppingIndex} className='pt-1'>
                                {topping.quantity}
                                {data.is_show_unit ? ` ${topping.unit || 'MON'}` : ''}
                              </div>
                            ))}
                      </td>
                      {data.enable_topping && (
                        <td
                          valign='top'
                          className={`min-h-8 px-1 text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                        >
                          <div>{formatCurrency(item.unitPrice)}</div>
                          {data.enable_topping &&
                            item.toppings &&
                            item.toppings
                              .filter(topping => topping.quantity > 0)
                              .map((topping, toppingIndex) => (
                                <div key={toppingIndex} className='pt-1'>
                                  {formatCurrency(topping.unitPrice)}
                                </div>
                              ))}
                        </td>
                      )}
                      {data.enable_vat_rate && (
                        <td
                          valign='top'
                          className={`min-h-8 px-1 text-center ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                        >
                          <div>{item.vatPercentage ? `${item.vatPercentage}%` : '10%'}</div>
                          {data.enable_topping &&
                            item.toppings &&
                            item.toppings
                              .filter(topping => topping.quantity > 0)
                              .map((topping, toppingIndex) => (
                                <div key={toppingIndex}>{topping.vatPercentage || 0}%</div>
                              ))}
                        </td>
                      )}
                      {data.enable_vat_amount && (
                        <td
                          valign='top'
                          className={`min-h-8 px-1 text-center ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                        >
                          <div>{formatCurrency(item.vatAmount || 0)}</div>
                          {data.enable_topping &&
                            item.toppings &&
                            item.toppings
                              .filter(topping => topping.quantity > 0)
                              .map((topping, toppingIndex) => (
                                <div key={toppingIndex}>{formatCurrency(topping.vatAmount || 0)}</div>
                              ))}
                        </td>
                      )}
                      {data.enable_discount && (
                        <td
                          valign='top'
                          className={`min-h-8 px-1 text-center ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                        >
                          <div>{item.discountPercentage ? `${item.discountPercentage}%` : '10%'}</div>
                          {data.enable_topping &&
                            item.toppings &&
                            item.toppings
                              .filter(topping => topping.quantity > 0)
                              .map((topping, toppingIndex) => (
                                <div key={toppingIndex}>
                                  {topping.discountPercentage ? `${topping.discountPercentage}%` : '10%'}
                                </div>
                              ))}
                        </td>
                      )}
                      <td
                        valign='top'
                        className={`min-h-8 px-1 text-right text-sm ${data.enable_border_bill ? 'border border-gray-800' : ''}`}
                      >
                        <div>
                          {formatCurrency(
                            data.enable_discount
                              ? item.subtotal - (item.subtotal * (item.discountPercentage || 10)) / 100
                              : item.subtotal
                          )}
                        </div>
                        {data.enable_topping &&
                          item.toppings &&
                          item.toppings
                            .filter(topping => topping.quantity > 0)
                            .map((topping, toppingIndex) => (
                              <div key={toppingIndex} className='pt-1'>
                                {formatCurrency(
                                  data.enable_discount
                                    ? topping.subtotal - (topping.subtotal * (topping.discountPercentage || 10)) / 100
                                    : topping.subtotal
                                )}
                              </div>
                            ))}
                      </td>
                    </tr>
                  ))
                }
              })()}
            </tbody>
          </table>
        </div>

        {/* Summary Section */}
        <div className='border-b border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Thành tiền:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(billData.totalWithVat)} ₫</p>
          </div>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm text-black'>Tiền chiết khấu:</p>
            <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.discountAmount)} ₫</p>
          </div>
          {!data.enable_discount && (
            <div className='mb-2 flex items-center justify-between'>
              <p className='mb-0 text-sm text-black'>Tiền giảm giá món:</p>
              <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.itemDiscountAmount)} ₫</p>
            </div>
          )}
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm text-black'>Tiền thuế(VAT):</p>
            <p className='mb-0 text-sm text-black'>{formatCurrency(billData.vatAmount)} ₫</p>
          </div>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Thành tiền VAT:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(billData.totalWithVat)} ₫</p>
          </div>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm text-black'>Phí vận chuyển:</p>
            <p className='mb-0 text-sm text-black'>{formatCurrency(billData.shippingFee)} ₫</p>
          </div>
          <div className='flex items-center justify-between'>
            <p className='mb-0 text-sm text-black'>Phiếu giảm giá:</p>
            <p className='mb-0 text-sm text-black'>- {formatCurrency(billData.voucherDiscount)} ₫</p>
          </div>
        </div>

        {/* Total Section */}
        <div className='border-b border-dashed border-gray-300 py-3'>
          <div className='mb-2 flex items-center justify-between'>
            <p className='mb-0 text-sm font-bold text-black'>Tổng cộng:</p>
            <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(billData.grandTotal)} ₫</p>
          </div>

          {/* Amount Received and Change Section */}
          {data.enable_cash_change && (
            <>
              <div className='mb-2 flex items-center justify-between'>
                <p className='mb-0 text-sm text-black'>Tiền nhận:</p>
                <p className='mb-0 text-sm text-black'>{formatCurrency(billData.amountReceived)} ₫</p>
              </div>
              <div className='flex items-center justify-between'>
                <p className='mb-0 text-sm text-black'>Tiền thừa:</p>
                <p className='mb-0 text-sm text-black'>{formatCurrency(billData.changeAmount)} ₫</p>
              </div>
            </>
          )}
        </div>

        {/* Payment Section */}
        <div className='py-3'>
          {data.show_vat_reverse ? (
            <div className='mb-2 flex justify-between'>
              <p className='mb-0 text-sm text-black'>+ Tổng tiền trên đã bao gồm</p>
              <p className='mb-0 text-sm font-bold text-black'>{formatCurrency(billData.vatAmount)} ₫ VAT</p>
            </div>
          ) : null}
          {data.is_show_payment_fee && (
            <div className='mb-2 flex justify-between'>
              <p className='mb-0 text-sm text-black'>+ Phí cà thẻ</p>
              <p className='mb-0 text-sm text-black'>{formatCurrency(billData.cardFeeAmount)} ₫</p>
            </div>
          )}
          <div className='flex justify-between'>
            <p className='mb-0 text-center text-sm text-black'>+ Thanh toán {billData.paymentMethod}</p>
            <p className='mb-0 text-sm text-black'>{formatCurrency(billData.grandTotal)} ₫</p>
          </div>
        </div>

        {/* Restaurant Info */}
        <div className='pb-3'>
          <p className='mb-0 text-center text-sm font-bold text-black'>{billData.restaurantName}</p>
          <p className='mb-0 text-center text-sm text-black'>Địa chỉ: {billData.restaurantAddress}</p>
          {data?.hotline && <p className='mb-0 text-center text-sm text-black'>Hotline: {data?.hotline}</p>}
          <div className='mx-auto mt-3 mb-1 h-px w-1/4 border border-black bg-black'></div>
          <div className='mx-4 mt-2'>
            <p className='mb-0 text-sm text-black'>
              Khách hàng: {billData.customerName} -{' '}
              <span>
                {data.show_customer_phone
                  ? billData.customerPhone.substring(0, 2) + '*'.repeat(billData.customerPhone.length - 2)
                  : billData.customerPhone}
              </span>
            </p>
            {data.show_points ? (
              <p className='mb-0 text-sm text-black'>
                Số điểm đã tích: {new Intl.NumberFormat('vi-VN').format(billData.accumulatedPoints)}
              </p>
            ) : null}
          </div>

          {/* Voucher Name Section */}
          {data.show_voucher_gift && billData.voucherName ? (
            <div className='mt-2 text-center'>
              <p className='mb-0 text-sm font-bold text-black'>{billData.voucherName}</p>
            </div>
          ) : null}
          {billData.customText1 && (
            <div
              className='mt-2 mb-0 text-center text-sm text-black'
              style={{ fontSize: `${(16 * (data.font_size || 100)) / 100}px` }}
            >
              {parse(
                DOMPurify.sanitize(data?.custom_text_1 || '', {
                  ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'span'],
                  ALLOWED_ATTR: ['class', 'style']
                })
              )}
            </div>
          )}

          {/* VAT Information Section */}
          {data.show_vat_info ? (
            <div className='mx-4 mt-2'>
              <p className='mb-0 text-sm text-black'>Thông tin khách hàng</p>
              <p className='mb-0 text-sm text-black'>Tên khách hàng: {billData.customerName}</p>
              <p className='mb-0 text-sm text-black'>Mã số thuế: {billData.customerTaxId}</p>
              <p className='mb-0 text-sm text-black'>Địa chỉ: {billData.customerAddress}</p>
            </div>
          ) : null}

          {/* Custom Scan QR Section */}
          {data.enable_qr_code ? (
            <div className='mx-4 mt-3 text-center'>
              {data.qr_title && <p className='text-md mb-2 text-black'>{data.qr_title}</p>}
              {data.qr_content ? (
                <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
                  <QRCode
                    value={data.qr_content.trim()}
                    level='H'
                    bgColor='#fff'
                    fgColor='#000'
                    size={150}
                    style={{ width: '100%', height: '100%' }}
                  />
                </div>
              ) : (
                <span className='text-gray-500'>Nhập nội dung để tạo mã QR</span>
              )}
            </div>
          ) : null}

          {/* VAT QR Code Section */}
          {data.show_qr_vat_info ? (
            <div className='mx-4 mt-3 text-center'>
              <p className='mb-2 text-sm text-black'>Quét mã QR dưới đây để cung cấp thông tin hoá đơn điện tử</p>
              <div className='mx-auto' style={{ width: '100px', height: '100px' }}>
                <QRCode
                  value={'https://fabi.ipos.vn/'}
                  level='H'
                  bgColor='#fff'
                  fgColor='#000'
                  size={150}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </div>
          ) : null}
        </div>

        {/* Footer */}
        <div className='border-t border-gray-300 py-3'>
          <p className='mb-0 text-center text-sm font-bold text-black'>{data?.custom_text_2}</p>
          <p className='mb-0 text-center text-sm text-black'>Powered by iPOS.vn</p>
        </div>
      </div>
    </div>
  )
}
