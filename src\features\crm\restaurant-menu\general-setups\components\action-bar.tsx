import { Button } from '@/components/ui/button'
import { RotateCcw, Trash2 } from 'lucide-react'

interface ActionBarProps {
  onSyncMenu: () => void
  onDeleteMenu: () => void
  variant?: 'desktop' | 'mobile'
  className?: string
}

export function ActionBar({ 
  onSyncMenu, 
  onDeleteMenu, 
  variant = 'desktop',
  className = '' 
}: ActionBarProps) {
  const isDesktop = variant === 'desktop'

  const containerClasses = isDesktop 
    ? `flex gap-2 ${className}`
    : `flex flex-col gap-2 w-full md:w-auto md:flex-shrink-0 ${className}`

  const buttonClasses = isDesktop 
    ? '' 
    : 'text-sm'

  return (
    <div className={containerClasses}>
      <Button
        variant='outline'
        onClick={onSyncMenu}
        className={`text-orange-600 border-orange-600 hover:bg-orange-50 ${buttonClasses}`}
      >
        <RotateCcw className='h-4 w-4 mr-2' />
        <PERSON><PERSON><PERSON> bộ thực đơn từ POS
      </Button>
      <Button
        variant='outline'
        onClick={onDeleteMenu}
        className={`text-red-600 border-red-600 hover:bg-red-50 ${buttonClasses}`}
      >
        <Trash2 className='h-4 w-4 mr-2' />
        Xóa thực đơn
      </Button>
    </div>
  )
}
