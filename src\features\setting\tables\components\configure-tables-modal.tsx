import React, { useState, useMemo, useEffect } from 'react'
import { X, Search } from 'lucide-react'

import { Button } from '@/components/ui'
import { PosModal } from '@/components/pos'

import { useAreasData } from '@/hooks/api/use-areas'
import { useTablesData, useBulkUpdateTables } from '@/hooks/api/use-tables'

interface ConfigureTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
}

interface Store {
  id: string
  store_name: string
  active: number
}

interface TableConfig {
  color: string
  fontSize: string
}

const COLOR_PRESETS = [
  '#1E40AF', '#EA580C', '#C026D3', '#FCD34D', '#6B7280', '#000000',
  '#FF4500', '#32CD32', '#00BFFF', '#4169E1', '#FF1493', '#8B4513',
  '#FFB6C1', '#DDA0DD', '#CD5C5C', '#A0522D', '#8B0000', '#2F4F4F'
]

export const ConfigureTablesModal: React.FC<ConfigureTablesModalProps> = ({
  open,
  onOpenChange,
  onCancel
}) => {

  const handleClose = () => {
    setSelectedStoreId('')
    setSelectedAreaId('')
    setSearchTerm('')
    setSelectedTables(new Set())
    setShowColorPicker(false)
    setSelectAllInArea(false)
    setApplyToAllTables(false)
    setConfig({
      color: '#1E40AF',
      fontSize: '15'
    })
    onOpenChange(false)
    onCancel()
  }
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [selectedAreaId, setSelectedAreaId] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTables, setSelectedTables] = useState<Set<string>>(new Set())
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [config, setConfig] = useState<TableConfig>({
    color: '#1E40AF',
    fontSize: '15'
  })
  const [selectAllInArea, setSelectAllInArea] = useState(false)
  const [applyToAllTables, setApplyToAllTables] = useState(false)




  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData)
          ? storesData.filter((store: Store) => store.active === 1)
          : []
      }
      return []
    } catch (error) {
      return []
    }
  }, [])


  const { data: areas = [] } = useAreasData({
    storeUid: selectedStoreId,
    page: 1,
    results_per_page: 1000
  })

  const { data: tables = [], isLoading: isLoadingTables } = useTablesData({
    storeUid: selectedStoreId,
    page: 1,
    limit: 1000
  })

  useEffect(() => {
    if (areas.length > 0 && !selectedAreaId && selectedStoreId) {
      setSelectedAreaId(areas[0].id)
    }
  }, [areas, selectedAreaId, selectedStoreId])




  const { mutateAsync: bulkUpdateTables, isPending: isUpdating } = useBulkUpdateTables()


  const filteredTables = useMemo(() => {
    if (!searchTerm) return tables
    return tables.filter(table =>
      table.table_name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [tables, searchTerm])


  const tablesByArea = useMemo(() => {
    const grouped: Record<string, typeof tables> = {}

    const tablesToShow = selectedAreaId
      ? filteredTables.filter(table => table.area_uid === selectedAreaId)
      : filteredTables

    tablesToShow.forEach(table => {
      const areaName = table.area?.area_name || 'Không có khu vực'
      if (!grouped[areaName]) {
        grouped[areaName] = []
      }
      grouped[areaName].push(table)
    })
    return grouped
  }, [filteredTables, selectedAreaId])

  const handleStoreChange = (storeId: string) => {
    setSelectedStoreId(storeId)
    setSelectedAreaId('')
    setSelectedTables(new Set())
    setSearchTerm('')
    setSelectAllInArea(false)
    setApplyToAllTables(false)
  }

  const handleTableSelect = (tableId: string) => {
    const newSelected = new Set(selectedTables)
    if (newSelected.has(tableId)) {
      newSelected.delete(tableId)
    } else {
      newSelected.add(tableId)
    }
    setSelectedTables(newSelected)
  }

  const handleSelectAllInArea = (checked: boolean) => {
    setSelectAllInArea(checked)
    if (checked && selectedAreaId) {
      const areaName = areas.find(area => area.id === selectedAreaId)?.area_name
      if (areaName && tablesByArea[areaName]) {
        const areaTableIds = tablesByArea[areaName].map(table => table.id)
        setSelectedTables(new Set([...selectedTables, ...areaTableIds]))
      }
    } else if (!checked && selectedAreaId) {

      const areaName = areas.find(area => area.id === selectedAreaId)?.area_name
      if (areaName && tablesByArea[areaName]) {
        const areaTableIds = new Set(tablesByArea[areaName].map(table => table.id))
        const newSelected = new Set([...selectedTables].filter(id => !areaTableIds.has(id)))
        setSelectedTables(newSelected)
      }
    }
  }

  const handleApplyToAllTables = (checked: boolean) => {
    setApplyToAllTables(checked)
    if (checked) {
      const allTableIds = tables.map(table => table.id)
      setSelectedTables(new Set(allTableIds))
    } else {
      setSelectedTables(new Set())
    }
  }

  const handleColorSelect = (color: string) => {
    setConfig(prev => ({ ...prev, color }))
    setShowColorPicker(false)
  }

  const handleSave = async () => {
    if (selectedTables.size === 0) {
      return
    }

    try {
      const selectedTableObjects = tables.filter(table => selectedTables.has(table.id))

      const updatedTables = selectedTableObjects.map(table => ({
        ...table,
        store_uid: table.store_uid || selectedStoreId,
        company_uid: table.company_uid || '',
        brand_uid: table.brand_uid || '',
        source_id: table.source_id || '',
        area_uid: table.area_uid || '',
        sort: table.sort || 1,
        description: table.description || '',
        extra_data: {
          ...table.extra_data,
          color: config.color,
          font_size: config.fontSize,
          order_list: table.extra_data?.order_list || []
        }
      }))

      await bulkUpdateTables({
        storeUid: selectedStoreId,
        tables: updatedTables
      })

      handleClose()
    } catch (error) {
      console.error('Error updating tables:', error)
    }
  }

  return (
    <PosModal
      title=''
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleClose}
      onConfirm={() => {}}
      confirmText='Lưu'
      cancelText='Đóng'
      centerTitle={true}
      maxWidth='sm:max-w-7xl'
      isLoading={false}
      confirmDisabled={false}
      hideButtons={true}
    >
      <div className='flex flex-col h-[85vh]'>

        <div className='flex items-center justify-between p-6 border-b'>
          <h2 className='text-xl font-semibold'>Cấu hình bàn</h2>
          <Button variant='ghost' size='sm' onClick={handleClose}>
            <X className='w-4 h-4' />
          </Button>
        </div>


        <div className='flex items-center gap-6 p-6 border-b bg-gray-50'>

          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium whitespace-nowrap'>Chọn điểm áp dụng</label>
            <select
              value={selectedStoreId}
              onChange={(e) => handleStoreChange(e.target.value)}
              className='px-3 py-2 border rounded-md bg-white min-w-[200px]'
            >
              <option value=''>Chọn cửa hàng</option>
              {stores.map(store => (
                <option key={store.id} value={store.id}>
                  {store.store_name}
                </option>
              ))}
            </select>
          </div>


          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium'>Màu chữ</label>
            <div className='relative'>
              <div
                className='w-8 h-8 rounded border cursor-pointer'
                style={{ backgroundColor: config.color }}
                onClick={() => setShowColorPicker(!showColorPicker)}
              />
              {showColorPicker && (
                <div className='absolute top-10 left-0 z-50 bg-white border rounded-lg shadow-lg p-4 w-80'>
                  <h3 className='text-sm font-medium mb-3'>Chọn ảnh hoặc màu</h3>
                  <div className='mb-4'>
                    <p className='text-xs text-gray-600 mb-2'>Hoặc chọn màu dưới đây</p>
                    <div className='space-y-2'>
                      <div className='grid grid-cols-6 gap-2'>
                        {COLOR_PRESETS.slice(0, 6).map(color => (
                          <div
                            key={color}
                            className={`w-8 h-8 rounded cursor-pointer border-2 ${
                              config.color === color ? 'border-blue-500' : 'border-gray-300'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => handleColorSelect(color)}
                          />
                        ))}
                      </div>
                      <div className='grid grid-cols-6 gap-2'>
                        {COLOR_PRESETS.slice(6, 12).map(color => (
                          <div
                            key={color}
                            className={`w-8 h-8 rounded cursor-pointer border-2 ${
                              config.color === color ? 'border-blue-500' : 'border-gray-300'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => handleColorSelect(color)}
                          />
                        ))}
                      </div>
                      <div className='grid grid-cols-6 gap-2'>
                        {COLOR_PRESETS.slice(12, 18).map(color => (
                          <div
                            key={color}
                            className={`w-8 h-8 rounded cursor-pointer border-2 ${
                              config.color === color ? 'border-blue-500' : 'border-gray-300'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => handleColorSelect(color)}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className='flex gap-2'>
                    <Button variant='outline' size='sm' onClick={() => setShowColorPicker(false)}>
                      Hủy
                    </Button>
                    <Button size='sm' onClick={() => setShowColorPicker(false)}>
                      Xong
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>


          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium'>Kích cỡ chữ</label>
            <input
              type='range'
              min='10'
              max='30'
              value={config.fontSize}
              onChange={(e) => setConfig(prev => ({ ...prev, fontSize: e.target.value }))}
              className='w-20'
            />
            <span className='text-sm min-w-[40px]'>{config.fontSize}px</span>
          </div>
        </div>


        <div className='flex flex-1 overflow-hidden'>

          <div className='w-80 border-r bg-gray-50 overflow-y-auto'>
            {isLoadingTables ? (
              <div className='p-4 text-center text-gray-500'>
                Đang tải dữ liệu bàn...
              </div>
            ) : (
              areas.map(area => {
                const areaTableCount = tables.filter(table => table.area_uid === area.id).length
                return (
                  <div
                    key={area.id}
                    className={`p-3 border-b cursor-pointer hover:bg-gray-100 ${
                      selectedAreaId === area.id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                    onClick={() => {
                      setSelectedAreaId(area.id)
                      setSelectAllInArea(false)
                      setSearchTerm('')
                    }}
                  >
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <span className='text-sm'>📍</span>
                        <span className='text-sm font-medium'>{area.area_name}</span>
                      </div>
                      <span className='text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded'>
                        {areaTableCount} bàn
                      </span>
                    </div>
                  </div>
                )
              })
            )}
          </div>


          <div className='flex-1 flex flex-col'>
            {selectedStoreId ? (
              <>

                <div className='p-4 border-b space-y-3'>

                  {selectedAreaId && (
                    <div className='bg-gray-50 p-3 rounded-md'>
                      <h3 className='font-medium text-gray-900'>
                        {areas.find(area => area.id === selectedAreaId)?.area_name || 'Khu vực'}
                      </h3>
                      <p className='text-sm text-gray-600'>Vui lòng chọn bàn</p>
                    </div>
                  )}


                  <div className='flex items-center gap-4 flex-wrap'>
                    <div className='flex items-center gap-2'>
                      <input
                        type='checkbox'
                        checked={selectAllInArea}
                        onChange={(e) => handleSelectAllInArea(e.target.checked)}
                        disabled={!selectedAreaId}
                      />
                      <span className='text-sm whitespace-nowrap'>Chọn tất cả bàn trong khu vực</span>
                    </div>

                    <div className='flex items-center gap-2'>
                      <input
                        type='checkbox'
                        checked={applyToAllTables}
                        onChange={(e) => handleApplyToAllTables(e.target.checked)}
                      />
                      <span className='text-sm whitespace-nowrap'>Áp dụng với toàn bộ bàn tại cửa hàng</span>
                    </div>

                    <div className='flex-1 relative min-w-[200px]'>
                      <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
                      <input
                        type='text'
                        placeholder='Tìm kiếm tên bàn'
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className='w-full pl-10 pr-4 py-2 border rounded-md'
                      />
                    </div>

                  </div>
                </div>


                <div className='flex-1 overflow-y-auto p-4'>
                  {Object.keys(tablesByArea).length === 0 ? (
                    <div className='flex items-center justify-center h-full text-gray-500'>
                      {selectedAreaId
                        ? 'Không có bàn nào trong khu vực này'
                        : 'Vui lòng chọn khu vực để xem danh sách bàn'
                      }
                    </div>
                  ) : (
                    Object.entries(tablesByArea).map(([areaName, areaTables]) => (
                      <div key={areaName} className='mb-6'>
                        <h3 className='text-lg font-semibold mb-3'>{areaName}</h3>
                        <div className='grid grid-cols-4 gap-4'>
                          {areaTables.map(table => {
                            const isSelected = selectedTables.has(table.id)
                            const currentColor = (table.extra_data as any)?.color || '#6B7280'
                            const currentFontSize = (table.extra_data as any)?.font_size || '15'
                            const displayColor = isSelected ? config.color : currentColor
                            const displayFontSize = isSelected ? config.fontSize : currentFontSize

                            return (
                              <div
                                key={table.id}
                                className={`
                                  relative cursor-pointer rounded-lg transition-all shadow-sm border overflow-hidden
                                  ${isSelected
                                    ? 'border-blue-500 bg-blue-50 shadow-md'
                                    : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                                  }
                                `}
                                onClick={() => handleTableSelect(table.id)}
                              >
                                <div
                                  className='h-1 w-full'
                                  style={{ backgroundColor: displayColor }}
                                ></div>

                                <div className='p-4 space-y-3'>
                                  <div className='flex items-center justify-between'>
                                    <div
                                      className='font-semibold text-lg transition-all'
                                      style={{
                                        color: displayColor,
                                        fontSize: `${displayFontSize}px`
                                      }}
                                    >
                                      {table.table_name}
                                    </div>
                                    {isSelected && (
                                      <div className='w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center'>
                                        <span className='text-white text-xs font-bold'>✓</span>
                                      </div>
                                    )}
                                  </div>


                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </>
            ) : (
              <div className='flex-1 flex items-center justify-center text-gray-500'>
                Vui lòng chọn cửa hàng
              </div>
            )}
          </div>
        </div>


        <div className='flex justify-end gap-3 p-6 border-t'>
          <Button variant='outline' onClick={handleClose} disabled={isUpdating}>
            Đóng
          </Button>
          <Button onClick={handleSave} disabled={selectedTables.size === 0 || isUpdating}>
            {isUpdating ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </div>
    </PosModal>
  )
}
