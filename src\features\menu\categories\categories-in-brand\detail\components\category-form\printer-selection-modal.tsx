import React, { useState, useMemo } from 'react'

import type { ItemCategory } from '@/types/item-categories'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { toast } from 'sonner'

import type { CreatedItemCategory } from '@/lib/item-categories-api'
import type { PrinterPosition } from '@/lib/printer-position-api'

import { useUpdatePrinterPositionCategory } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Dialog, DialogContent, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'

interface PrinterSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  printerPositions: PrinterPosition[]
  selectedPrinters: string[]
  onPrintersSelected: (selectedPrinterIds: string[]) => void
  newlyCreatedCategory?: CreatedItemCategory | null
  currentCategory?: ItemCategory | null // For edit mode
}

export function PrinterSelectionModal({
  open,
  onOpenChange,
  printerPositions,
  selectedPrinters,
  onPrintersSelected,
  newlyCreatedCategory,
  currentCategory
}: PrinterSelectionModalProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCollapsed, setSelectedCollapsed] = useState(false)
  const [remainingCollapsed, setRemainingCollapsed] = useState(false)
  const [localSelectedPrinters, setLocalSelectedPrinters] = useState<string[]>(selectedPrinters)
  const [isUpdating, setIsUpdating] = useState(false)

  const updatePrinterPositionCategory = useUpdatePrinterPositionCategory()

  // Filter printer positions based on search query
  const filteredPrinters = useMemo(() => {
    if (!searchQuery.trim()) return printerPositions

    const searchLower = searchQuery.toLowerCase()
    return printerPositions.filter(
      printer =>
        printer.printer_position_name.toLowerCase().includes(searchLower) ||
        printer.printer_position_id.toLowerCase().includes(searchLower)
    )
  }, [printerPositions, searchQuery])

  // Separate selected and remaining printer positions
  const selectedPrintersData = filteredPrinters.filter(printer =>
    localSelectedPrinters.includes(printer.printer_position_id)
  )
  const remainingPrinters = filteredPrinters.filter(
    printer => !localSelectedPrinters.includes(printer.printer_position_id)
  )

  const handlePrinterToggle = (printerId: string) => {
    const newSelectedPrinters = localSelectedPrinters.includes(printerId)
      ? localSelectedPrinters.filter(id => id !== printerId)
      : [...localSelectedPrinters, printerId]

    setLocalSelectedPrinters(newSelectedPrinters)
  }

  const handleSave = async () => {
    // Get the category to work with (either newly created or current category in edit mode)
    const categoryToUpdate = newlyCreatedCategory || currentCategory

    // If no category to update, just update the local state
    if (!categoryToUpdate) {
      onPrintersSelected(localSelectedPrinters)
      onOpenChange(false)
      return
    }

    setIsUpdating(true)

    try {
      // Determine which printers were added and removed
      const previousSelection = new Set(selectedPrinters)
      const newSelection = new Set(localSelectedPrinters)

      const addedPrinters = localSelectedPrinters.filter(id => !previousSelection.has(id))
      const removedPrinters = selectedPrinters.filter(id => !newSelection.has(id))

      // Create API calls for all changes
      const apiCalls: Promise<any>[] = []

      // Add category to newly selected printers
      addedPrinters.forEach(printerId => {
        const printerPosition = printerPositions.find(p => p.printer_position_id === printerId)
        if (printerPosition) {
          apiCalls.push(
            updatePrinterPositionCategory.mutateAsync({
              printerPosition,
              categoryId: categoryToUpdate.item_type_id,
              action: 'add'
            })
          )
        }
      })

      // Remove category from deselected printers
      removedPrinters.forEach(printerId => {
        const printerPosition = printerPositions.find(p => p.printer_position_id === printerId)
        if (printerPosition) {
          apiCalls.push(
            updatePrinterPositionCategory.mutateAsync({
              printerPosition,
              categoryId: categoryToUpdate.item_type_id,
              action: 'remove'
            })
          )
        }
      })

      // Execute all API calls
      if (apiCalls.length > 0) {
        await Promise.all(apiCalls)
        toast.success(
          `Đã cập nhật ${apiCalls.length} vị trí máy in cho nhóm món "${categoryToUpdate.item_type_name}"`
        )
      }

      // Update local state and close modal
      onPrintersSelected(localSelectedPrinters)
      onOpenChange(false)
    } catch (error) {
      console.error('Error updating printer positions:', error)
      toast.error('Có lỗi xảy ra khi cập nhật vị trí máy in')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleCancel = () => {
    setLocalSelectedPrinters(selectedPrinters)
    onOpenChange(false)
  }

  // Reset local selection when modal opens
  React.useEffect(() => {
    if (open) {
      setLocalSelectedPrinters(selectedPrinters)
      setSearchQuery('')
    }
  }, [open, selectedPrinters])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-md'>
        <div className='space-y-4'>
          {/* Search Input */}
          <div>
            <Input
              placeholder='Tìm kiếm vị trí máy in'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className='w-full'
            />
          </div>

          {/* Selected Printers Collapse */}
          <Collapsible open={!selectedCollapsed} onOpenChange={setSelectedCollapsed}>
            <CollapsibleTrigger asChild>
              <Button
                variant='ghost'
                className='flex w-full items-center justify-between p-2 text-left'
              >
                <span>Đã chọn ({selectedPrintersData.length})</span>
                {selectedCollapsed ? (
                  <ChevronRight className='h-4 w-4' />
                ) : (
                  <ChevronDown className='h-4 w-4' />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className='space-y-2'>
              {selectedPrintersData.map(printer => (
                <div key={printer.printer_position_id} className='flex items-center space-x-2 p-2'>
                  <Checkbox
                    checked={true}
                    onCheckedChange={() => handlePrinterToggle(printer.printer_position_id)}
                  />
                  <span className='text-sm'>{printer.printer_position_name}</span>
                </div>
              ))}
              {selectedPrintersData.length === 0 && (
                <div className='p-2 text-sm text-gray-500'>Chưa chọn vị trí máy in nào</div>
              )}
            </CollapsibleContent>
          </Collapsible>

          {/* Remaining Printers Collapse */}
          <Collapsible open={!remainingCollapsed} onOpenChange={setRemainingCollapsed}>
            <CollapsibleTrigger asChild>
              <Button
                variant='ghost'
                className='flex w-full items-center justify-between p-2 text-left'
              >
                <span>Còn lại ({remainingPrinters.length})</span>
                {remainingCollapsed ? (
                  <ChevronRight className='h-4 w-4' />
                ) : (
                  <ChevronDown className='h-4 w-4' />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className='space-y-2'>
              {remainingPrinters.map(printer => (
                <div key={printer.printer_position_id} className='flex items-center space-x-2 p-2'>
                  <Checkbox
                    checked={false}
                    onCheckedChange={() => handlePrinterToggle(printer.printer_position_id)}
                  />
                  <span className='text-sm'>{printer.printer_position_name}</span>
                </div>
              ))}
              {remainingPrinters.length === 0 && (
                <div className='p-2 text-sm text-gray-500'>Không có vị trí máy in nào</div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel} disabled={isUpdating}>
            Hủy
          </Button>
          <Button onClick={handleSave} disabled={isUpdating}>
            {isUpdating ? 'Đang cập nhật...' : 'Lưu'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
