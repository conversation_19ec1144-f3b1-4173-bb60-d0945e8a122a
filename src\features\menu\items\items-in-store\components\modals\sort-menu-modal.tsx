import { useState, useEffect } from 'react'

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy
} from '@dnd-kit/sortable'

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'

import { useStoresData, useItemTypesData } from '@/hooks/api'
import { useItemsInStoreData, useBulkUpdateItemsInStore } from '../../hooks'
import { SortableMenuItem } from '../sortable-menu-item'
import type { ItemsInStore } from '../../data'

interface SortMenuModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}



export function SortMenuModal({ open, onOpenChange }: SortMenuModalProps) {
  const [selectedStoreUid, setSelectedStoreUid] = useState<string>('')
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('')

  // Fetch stores data
  const { data: stores = [] } = useStoresData({
    enabled: open
  })

  // Fetch item types for selected store
  const { data: itemTypes = [] } = useItemTypesData({
    ...(selectedStoreUid && selectedStoreUid !== 'all' ? { store_uid: selectedStoreUid } : {}),
    enabled: open && !!selectedStoreUid
  })

  // Fetch menu items for selected store and item type
  const { data: menuItems = [] } = useItemsInStoreData({
    params: {
      ...(selectedStoreUid && { store_uid: selectedStoreUid }),
      ...(selectedItemTypeUid && { item_type_uid: selectedItemTypeUid })
    },
    enabled: open && !!selectedStoreUid && !!selectedItemTypeUid
  })

  // Bulk update hook
  const bulkUpdateMutation = useBulkUpdateItemsInStore()

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Use state for sorted items to allow drag and drop
  const [sortedItems, setSortedItems] = useState<ItemsInStore[]>([])

  // Initialize sorted items when menuItems change
  useEffect(() => {
    if (menuItems.length > 0) {
      const sorted = [...menuItems].sort((a, b) => {
        if (a.sort !== b.sort) {
          return a.sort - b.sort
        }
        return a.id.localeCompare(b.id)
      })
      setSortedItems(sorted)
    }
  }, [menuItems.length]) // Only depend on length to avoid infinite loop

  // Reset selections when modal closes
  useEffect(() => {
    if (!open) {
      setSelectedStoreUid('')
      setSelectedItemTypeUid('')
      setSortedItems([])
    }
  }, [open])

  // Reset item type selection when store changes
  useEffect(() => {
    setSelectedItemTypeUid('')
    setSortedItems([])
  }, [selectedStoreUid])

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      setSortedItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  const handleSave = () => {
    if (sortedItems.length === 0) {
      onOpenChange(false)
      return
    }

    // Prepare bulk update data with new sort orders
    const updateData = sortedItems.map((item, index) => ({
      id: item.id,
      item_id: item.item_id,
      item_name: item.item_name,
      description: item.description,
      ots_price: item.ots_price,
      ots_tax: item.ots_tax,
      ta_price: item.ta_price,
      ta_tax: item.ta_tax,
      time_sale_hour_day: item.time_sale_hour_day,
      time_sale_date_week: item.time_sale_date_week,
      allow_take_away: item.allow_take_away,
      is_eat_with: item.is_eat_with,
      image_path: item.image_path,
      image_path_thumb: item.image_path_thumb,
      item_color: item.item_color,
      list_order: index + 1,
      is_service: item.is_service,
      is_material: item.is_material,
      active: item.active,
      user_id: item.user_id,
      is_foreign: item.is_foreign,
      quantity_default: item.quantity_default,
      price_change: item.price_change,
      currency_type_id: item.currency_type_id,
      point: item.point,
      is_gift: item.is_gift,
      is_fc: item.is_fc,
      show_on_web: item.show_on_web,
      show_price_on_web: item.show_price_on_web,
      cost_price: item.cost_price,
      is_print_label: item.is_print_label,
      quantity_limit: item.quantity_limit,
      is_kit: item.is_kit,
      time_cooking: item.time_cooking,
      item_id_barcode: item.item_id_barcode,
      process_index: item.process_index,
      is_allow_discount: item.is_allow_discount,
      quantity_per_day: item.quantity_per_day,
      item_id_eat_with: item.item_id_eat_with,
      is_parent: item.is_parent,
      is_sub: item.is_sub,
      item_id_mapping: item.item_id_mapping,
      effective_date: item.effective_date,
      expire_date: item.expire_date,
      sort: index + 1,
      extra_data: {
        ...item.extra_data,
        formula_qrcode: ''
      },
      revision: item.revision,
      unit_uid: item.unit_uid,
      unit_secondary_uid: item.unit_secondary_uid,
      item_type_uid: item.item_type_uid,
      item_class_uid: item.item_class_uid,
      source_uid: item.source_uid,
      brand_uid: item.brand_uid,
      company_uid: item.company_uid,
      customization_uid: item.customization_uid,
      is_fabi: item.is_fabi,
      deleted: item.deleted,
      created_by: item.created_by,
      updated_by: item.updated_by,
      deleted_by: item.deleted_by,
      created_at: item.created_at,
      updated_at: item.updated_at,
      deleted_at: item.deleted_at,
      apply_with_store: item.apply_with_store,
      store_uid: item.store_uid,
      city_uid: item.city_uid
    }))

    bulkUpdateMutation.mutate(updateData, {
      onSuccess: () => {
        onOpenChange(false)
      }
    })
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="h-[90vh] max-w-7xl sm:max-w-4xl flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-center text-lg font-medium">
            Sắp xếp thực đơn bán hàng
          </DialogTitle>
        </DialogHeader>

        {/* Warning message */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4 flex-shrink-0">
          <p className="text-sm text-yellow-800">
            Các món tại thành phố bị thay đổi vị trí sẽ chuyển thành món tại cửa hàng
          </p>
        </div>

        {/* Store selection */}
        <div className="flex items-center gap-4 mb-4 flex-shrink-0">
          <span className="text-sm font-medium whitespace-nowrap">
            Thứ tự hiển thị sẽ được áp dụng trên thực đơn của thiết bị bán hàng
          </span>
          <div className="flex-1" />
          <div className="w-64">
            <Select value={selectedStoreUid} onValueChange={setSelectedStoreUid}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn điểm áp dụng" />
              </SelectTrigger>
              <SelectContent>
                {stores.map((store) => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Content area */}
        <div className="flex-1 min-h-0 overflow-hidden">
          {!selectedStoreUid ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <p>Chưa chọn cửa hàng</p>
            </div>
          ) : (
            <div className="grid grid-cols-12 gap-4 h-full">
              {/* Left panel - Item Types */}
              <div className="col-span-3 h-full">
                <div className="border rounded-lg h-full flex flex-col">
                  <div className="p-3 border-b bg-gray-50 flex-shrink-0">
                    <h3 className="font-medium text-sm">Tên nhóm món</h3>
                  </div>
                  <div className="flex-1 min-h-0">
                    <ScrollArea className="h-[50vh] w-full">
                      <div className="p-2 space-y-1">
                        {itemTypes.map((itemType) => (
                          <button
                            key={itemType.id}
                            onClick={() => setSelectedItemTypeUid(itemType.id)}
                            className={`w-full text-left p-2 rounded text-sm transition-colors ${
                              selectedItemTypeUid === itemType.id
                                ? 'bg-blue-100 text-blue-900'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            <div className="flex items-center gap-2">
                              <span className="text-gray-600">📁</span>
                              <span>{itemType.item_type_name}</span>
                            </div>
                          </button>
                        ))}
                      </div>
                      <ScrollBar orientation="vertical" />
                    </ScrollArea>
                  </div>
                </div>
              </div>

              {/* Right panel - Menu Items */}
              <div className="col-span-9 h-full">
                <div className="border rounded-lg h-full flex flex-col">
                  <div className="p-3 border-b bg-gray-50 flex-shrink-0">
                    <h3 className="font-medium text-sm">
                      {selectedItemTypeUid
                        ? itemTypes.find(type => type.id === selectedItemTypeUid)?.item_type_name || 'Uncategory'
                        : 'Uncategory'
                      }
                    </h3>
                  </div>
                  <div className="flex-1 min-h-0">
                    <ScrollArea className="h-[50vh] w-full">
                      <div className="p-4">
                        {!selectedItemTypeUid ? (
                          <div className="text-center text-gray-500 py-8">
                            <p>Vui lòng chọn nhóm món từ danh sách bên trái</p>
                          </div>
                        ) : sortedItems.length === 0 ? (
                          <div className="text-center text-gray-500 py-8">
                            <p>Không có món ăn nào trong nhóm này</p>
                          </div>
                        ) : (
                          <DndContext
                            sensors={sensors}
                            collisionDetection={closestCenter}
                            onDragEnd={handleDragEnd}
                          >
                            <SortableContext
                              items={sortedItems.map(item => item.id)}
                              strategy={rectSortingStrategy}
                            >
                              <div className="grid grid-cols-4 gap-4">
                                {sortedItems.map((item) => (
                                  <SortableMenuItem key={item.id} item={item} />
                                ))}
                              </div>
                            </SortableContext>
                          </DndContext>
                        )}
                      </div>
                      <ScrollBar orientation="vertical" />
                      <ScrollBar orientation="horizontal" />
                    </ScrollArea>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer buttons */}
        <div className="flex justify-end gap-2 pt-4 border-t flex-shrink-0">
          <Button variant="outline" onClick={handleCancel} disabled={bulkUpdateMutation.isPending}>
            Hủy
          </Button>
          <Button
            onClick={handleSave}
            disabled={!selectedStoreUid || !selectedItemTypeUid || sortedItems.length === 0 || bulkUpdateMutation.isPending}
          >
            {bulkUpdateMutation.isPending ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
