import { useState, useMemo } from 'react'

import { LocalUser, CreateUserRequest, UpdateUserRequest, PERMISSION_CATEGORIES } from '../data'

// Mock data for demonstration
const MOCK_USERS: LocalUser[] = [
  {
    id: '1',
    username: 'tuti<PERSON>',
    email: '<EMAIL>',
    status: 'active',
    permissions: [
      { id: 'all', name: '<PERSON><PERSON><PERSON> cả', category: 'Báo cáo' },
      { id: 'customer_access', name: '<PERSON><PERSON><PERSON> cập', category: '<PERSON>h sách khách hàng' }
    ],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  }
]

export function useAccountManagement() {
  const [users, setUsers] = useState<LocalUser[]>(MOCK_USERS)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createUser = async (userData: CreateUserRequest) => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      const permissions = PERMISSION_CATEGORIES.flatMap(category => category.permissions).filter(permission =>
        userData.permissions.includes(permission.id)
      )

      const newUser: LocalUser = {
        id: Date.now().toString(),
        username: userData.username,
        email: userData.email,
        status: 'active',
        permissions,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      setUsers(prev => [...prev, newUser])
      return newUser
    } catch (err) {
      setError('Có lỗi xảy ra khi tạo tài khoản')
      throw err
    } finally {
      setIsLoading(false)
    }
  }

  const updateUser = async (userData: UpdateUserRequest) => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      setUsers(prev =>
        prev.map(user => {
          if (user.id === userData.id) {
            const permissions = userData.permissions
              ? PERMISSION_CATEGORIES.flatMap(category => category.permissions).filter(permission =>
                  userData.permissions!.includes(permission.id)
                )
              : user.permissions

            return {
              ...user,
              ...userData,
              permissions,
              updatedAt: new Date().toISOString()
            }
          }
          return user
        })
      )
    } catch (err) {
      setError('Có lỗi xảy ra khi cập nhật tài khoản')
      throw err
    } finally {
      setIsLoading(false)
    }
  }

  const deleteUser = async (userId: string) => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))

      setUsers(prev => prev.filter(user => user.id !== userId))
    } catch (err) {
      setError('Có lỗi xảy ra khi xóa tài khoản')
      throw err
    } finally {
      setIsLoading(false)
    }
  }

  const toggleUserStatus = async (userId: string) => {
    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500))

      setUsers(prev =>
        prev.map(user => {
          if (user.id === userId) {
            return {
              ...user,
              status: user.status === 'active' ? 'inactive' : 'active',
              updatedAt: new Date().toISOString()
            }
          }
          return user
        })
      )
    } catch (err) {
      setError('Có lỗi xảy ra khi thay đổi trạng thái tài khoản')
      throw err
    } finally {
      setIsLoading(false)
    }
  }

  const permissionCategories = useMemo(() => PERMISSION_CATEGORIES, [])

  return {
    users,
    isLoading,
    error,
    createUser,
    updateUser,
    deleteUser,
    toggleUserStatus,
    permissionCategories
  }
}
