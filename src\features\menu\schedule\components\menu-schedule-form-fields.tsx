import { format } from 'date-fns'

import { UseFormReturn } from 'react-hook-form'

import { vi } from 'date-fns/locale'
import { CalendarIcon } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

interface FormData {
  city_uid: string
  store_uid?: string
  start_date: Date
  end_date?: Date
}

interface MenuScheduleFormFieldsProps {
  form: UseFormReturn<FormData>
  citiesData: Array<{ id: string; name: string }>
  storesData: Array<{ id: string; name: string }>
  isUpdateMode: boolean
}

export function MenuScheduleFormFields({ form, citiesData, storesData, isUpdateMode }: MenuScheduleFormFieldsProps) {
  return (
    <div className='grid grid-cols-2 gap-4'>
      <FormField
        control={form.control}
        name='start_date'
        render={({ field }) => (
          <FormItem className='flex flex-col'>
            <FormLabel>Ngày bắt đầu</FormLabel>
            <Popover key={`start-date-${field.value?.getTime()}`}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant='outline'
                    className={cn('w-full pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}
                  >
                    {field.value ? format(field.value, 'dd/MM/yyyy', { locale: vi }) : <span>Chọn ngày</span>}
                    <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className='w-auto p-0' align='start'>
                <Calendar
                  mode='single'
                  selected={field.value}
                  onSelect={field.onChange}
                  disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))}
                  defaultMonth={field.value}
                />
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='end_date'
        render={({ field }) => (
          <FormItem className='flex flex-col'>
            <FormLabel>Ngày kết thúc (tùy chọn)</FormLabel>
            <Popover key={`end-date-${field.value?.getTime()}`}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant='outline'
                    className={cn('w-full pl-3 text-left font-normal', !field.value && 'text-muted-foreground')}
                  >
                    {field.value ? format(field.value, 'dd/MM/yyyy', { locale: vi }) : <span>Chọn ngày</span>}
                    <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className='w-auto p-0' align='start'>
                <Calendar
                  mode='single'
                  selected={field.value}
                  onSelect={field.onChange}
                  disabled={date => date < new Date(new Date().setHours(0, 0, 0, 0))}
                  defaultMonth={field.value}
                />
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='city_uid'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Thành phố</FormLabel>
            <Select
              key={`city-${field.value}`}
              onValueChange={field.onChange}
              value={field.value}
              defaultValue={field.value}
              disabled={isUpdateMode}
            >
              <FormControl>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Chọn thành phố' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {citiesData.map(city => (
                  <SelectItem key={city.id} value={city.id}>
                    {city.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name='store_uid'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Cửa hàng</FormLabel>
            <Select
              key={`store-${field.value}`}
              onValueChange={field.onChange}
              value={field.value}
              defaultValue={field.value}
              disabled={isUpdateMode}
            >
              <FormControl>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {storesData.map(store => (
                  <SelectItem key={store.id} value={store.id}>
                    {store.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
