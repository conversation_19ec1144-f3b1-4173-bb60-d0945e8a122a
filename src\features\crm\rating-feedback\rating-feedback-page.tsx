import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'
import { StarRating, Rating<PERSON><PERSON>, DateRangePicker } from './components'

// Interface for API response
interface RatingFeedbackData {
  rate_total_count: number
  rate_average: number
  number_of_rate_1_star: number
  number_of_rate_2_star: number
  number_of_rate_3_star: number
  number_of_rate_4_star: number
  number_of_rate_5_star: number
  report_by_pos: Array<{
    pos_name: string
    total_rating: number
    rating_count: number
    average_rating: number
    star_5: number
    star_4: number
    star_3: number
    star_2: number
    star_1: number
  }>
}

// Mock data based on API response structure
const mockData: RatingFeedbackData = {
  rate_total_count: 125,
  rate_average: 4.2,
  number_of_rate_1_star: 5,
  number_of_rate_2_star: 8,
  number_of_rate_3_star: 15,
  number_of_rate_4_star: 42,
  number_of_rate_5_star: 55,
  report_by_pos: [
    {
      pos_name: "Cửa hàng Quận 1",
      total_rating: 210,
      rating_count: 50,
      average_rating: 4.2,
      star_5: 20,
      star_4: 15,
      star_3: 8,
      star_2: 4,
      star_1: 3
    },
    {
      pos_name: "Cửa hàng Quận 3",
      total_rating: 185,
      rating_count: 45,
      average_rating: 4.1,
      star_5: 18,
      star_4: 12,
      star_3: 10,
      star_2: 3,
      star_1: 2
    },
    {
      pos_name: "Cửa hàng Quận 7",
      total_rating: 120,
      rating_count: 30,
      average_rating: 4.0,
      star_5: 12,
      star_4: 10,
      star_3: 5,
      star_2: 2,
      star_1: 1
    }
  ]
}

export function RatingFeedbackPage() {
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [dateRange, setDateRange] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activePreset, setActivePreset] = useState<string>('7days')
  const [data] = useState<RatingFeedbackData>(mockData)

  // Initialize with 7 days preset
  useEffect(() => {
    handlePresetDate('7days')
  }, [])

  // Format date range for display
  const formatDateRange = (start: Date, end: Date) => {
    return `${format(start, 'dd/MM/yyyy')} - ${format(end, 'dd/MM/yyyy')}`
  }

  // Handle preset date selections
  const handlePresetDate = (preset: string) => {
    const today = new Date()
    let start: Date
    let end: Date = endOfDay(today)

    switch (preset) {
      case 'today':
        start = startOfDay(today)
        break
      case 'yesterday':
        start = startOfDay(subDays(today, 1))
        end = endOfDay(subDays(today, 1))
        break
      case '7days':
        start = startOfDay(subDays(today, 6))
        break
      case '15days':
        start = startOfDay(subDays(today, 14))
        break
      case '30days':
        start = startOfDay(subDays(today, 29))
        break
      default:
        return
    }

    setStartDate(start)
    setEndDate(end)
    setDateRange(formatDateRange(start, end))
    setActivePreset(preset)
  }

  // Handle manual date input
  const handleDateRangeChange = (value: string) => {
    setDateRange(value)
    setActivePreset('')

    // Try to parse the date range format "dd/mm/yyyy - dd/mm/yyyy"
    const dateRangeRegex = /^(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})$/
    const match = value.match(dateRangeRegex)

    if (match) {
      try {
        const [, startStr, endStr] = match
        const [startDay, startMonth, startYear] = startStr.split('/').map(Number)
        const [endDay, endMonth, endYear] = endStr.split('/').map(Number)

        const start = new Date(startYear, startMonth - 1, startDay)
        const end = new Date(endYear, endMonth - 1, endDay)

        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          setStartDate(start)
          setEndDate(end)
        }
      } catch (error) {
        console.error('Invalid date format:', error)
      }
    }
  }

  // Prepare rating chart data
  const ratingChartData = [
    {
      stars: 5,
      count: data.number_of_rate_5_star,
      percentage: data.rate_total_count > 0 ? (data.number_of_rate_5_star / data.rate_total_count) * 100 : 0
    },
    {
      stars: 4,
      count: data.number_of_rate_4_star,
      percentage: data.rate_total_count > 0 ? (data.number_of_rate_4_star / data.rate_total_count) * 100 : 0
    },
    {
      stars: 3,
      count: data.number_of_rate_3_star,
      percentage: data.rate_total_count > 0 ? (data.number_of_rate_3_star / data.rate_total_count) * 100 : 0
    },
    {
      stars: 2,
      count: data.number_of_rate_2_star,
      percentage: data.rate_total_count > 0 ? (data.number_of_rate_2_star / data.rate_total_count) * 100 : 0
    },
    {
      stars: 1,
      count: data.number_of_rate_1_star,
      percentage: data.rate_total_count > 0 ? (data.number_of_rate_1_star / data.rate_total_count) * 100 : 0
    }
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Phản hồi khách hàng</h1>
        <p className="text-gray-600 mt-2">
          Theo dõi và phân tích đánh giá của khách hàng
        </p>
      </div>

      {/* Date Filter */}
      <div className="flex justify-end items-center gap-2 mb-6">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'today' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('today')}
          >
            Hôm nay
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'yesterday' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('yesterday')}
          >
            Hôm qua
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '7days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('7days')}
          >
            7 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '15days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('15days')}
          >
            15 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '30days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('30days')}
          >
            30 ngày trước
          </Button>
        </div>
        <DateRangePicker
          startDate={startDate}
          endDate={endDate}
          onDateChange={(start, end) => {
            setStartDate(start)
            setEndDate(end)
            if (start && end) {
              setDateRange(formatDateRange(start, end))
              setActivePreset('')
            }
          }}
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </div>

      {/* Main Content */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="grid grid-cols-10 gap-8">
            {/* Left side - Stats (3/10) */}
            <div className="col-span-3 space-y-8">
              {/* Total Reviews */}
              <div className="text-center">
                <div className="text-4xl font-bold text-gray-900 mb-2">{data.rate_total_count}</div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">LƯỢT ĐÁNH GIÁ</div>
              </div>

              {/* Average Rating */}
              <div className="text-center">
                <div className="text-4xl font-bold text-gray-900 mb-2">{data.rate_average.toFixed(2)}</div>
                <div className="text-sm text-gray-600 uppercase tracking-wide">ĐIỂM TRUNG BÌNH</div>
              </div>
            </div>

            {/* Right side - Rating breakdown chart (7/10) */}
            <div className="col-span-7">
              <RatingChart ratingData={ratingChartData} />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rating by Store Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="flex-1"></div>
            <CardTitle className="text-base font-medium text-gray-700 text-center flex-1">
              ĐÁNH GIÁ THEO CỬA HÀNG
            </CardTitle>
            <div className="flex-1 flex justify-end">
              <Input
                placeholder="Tìm kiếm nhanh"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-hidden">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-3 text-sm font-medium text-gray-600">Cửa hàng</th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">Tổng điểm</th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">Số lượt đánh giá</th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">Trung bình</th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">
                    <StarRating rating={5} size="sm" />
                  </th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">
                    <StarRating rating={4} size="sm" />
                  </th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">
                    <StarRating rating={3} size="sm" />
                  </th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">
                    <StarRating rating={2} size="sm" />
                  </th>
                  <th className="text-center p-3 text-sm font-medium text-gray-600">
                    <StarRating rating={1} size="sm" />
                  </th>
                </tr>
              </thead>
              <tbody>
                {data.report_by_pos.length === 0 ? (
                  <tr>
                    <td colSpan={9} className="text-center p-8 text-gray-500">
                      No data available in table
                    </td>
                  </tr>
                ) : (
                  data.report_by_pos
                    .filter(store =>
                      searchTerm === '' ||
                      store.pos_name.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map((store, index) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-3 text-sm text-gray-900">{store.pos_name}</td>
                        <td className="p-3 text-sm text-gray-900 text-center">{store.total_rating}</td>
                        <td className="p-3 text-sm text-gray-900 text-center">{store.rating_count}</td>
                        <td className="p-3 text-sm text-gray-900 text-center">
                          {store.average_rating.toFixed(2)}
                        </td>
                        <td className="p-3 text-sm text-gray-900 text-center">{store.star_5}</td>
                        <td className="p-3 text-sm text-gray-900 text-center">{store.star_4}</td>
                        <td className="p-3 text-sm text-gray-900 text-center">{store.star_3}</td>
                        <td className="p-3 text-sm text-gray-900 text-center">{store.star_2}</td>
                        <td className="p-3 text-sm text-gray-900 text-center">{store.star_1}</td>
                      </tr>
                    ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Customer Comments Section */}
      <Card className="mt-6">
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-gray-700 mb-4 text-center">
            KHÁCH HÀNG BÌNH LUẬN
          </h3>
          <div className="text-center text-gray-500 py-8">
            Chưa có bình luận
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
