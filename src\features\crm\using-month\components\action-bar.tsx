import { IconDownload, IconSearch } from '@tabler/icons-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import { VietnameseDatePicker } from './vietnamese-date-picker'

interface ActionBarProps {
  selectedMonth: string
  onMonthChange: (month: string) => void
  onSearch: () => void
  onExport: () => void
  isLoading: boolean
}

export function ActionBar({ selectedMonth, onMonthChange, onSearch, onExport, isLoading }: ActionBarProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Chọn tháng</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-2'>
            <VietnameseDatePicker selectedMonth={selectedMonth} onMonthChange={onMonthChange} />
          </div>

          <Button onClick={onSearch} disabled={isLoading}>
            <IconSearch className='mr-2 h-4 w-4' />
            {isLoading ? 'Đang tìm...' : 'Tìm kiếm'}
          </Button>

          <div className='ml-auto'>
            <Button variant='outline' onClick={onExport}>
              <IconDownload className='mr-2 h-4 w-4' />
              Xuất file
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
