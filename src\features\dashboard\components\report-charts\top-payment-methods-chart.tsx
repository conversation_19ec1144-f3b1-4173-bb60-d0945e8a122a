import { useMemo } from 'react'

import { useAuthStore } from '@/stores'

import { usePosStores } from '@/stores/posStore'

import { usePaymentMethodRevenue } from '@/hooks/api'

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui'

import { useDashboardContext } from '../../context'
import { PaymentMethodsPieChart } from '../pie-chart'

export function TopPaymentMethodsChart() {
  const { selectedStores, defaultDateRange } = useDashboardContext()
  const { company, brands } = useAuthStore(state => state.auth)
  const { currentBrandStores } = usePosStores()
  const selectedBrand = brands?.[0]

  const formatDateRange = useMemo(() => {
    const fromDate = defaultDateRange.from
    const toDate = defaultDateRange.to

    const formatDate = (date: Date) => {
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`
    }

    const fromFormatted = `${formatDate(fromDate)} 00:00`
    const toFormatted = `${formatDate(toDate)} 23:59`

    return `Báo cáo tính từ ${fromFormatted} - ${toFormatted}`
  }, [defaultDateRange])

  const startOfDay = useMemo(() => {
    const date = new Date(defaultDateRange.from)
    date.setHours(0, 0, 0, 0)
    return date.getTime()
  }, [defaultDateRange.from])

  const endOfDay = useMemo(() => {
    const date = new Date(defaultDateRange.to)
    date.setHours(23, 59, 59, 999)
    return date.getTime()
  }, [defaultDateRange.to])

  const selectedStoreIds = useMemo(() => {
    if (!currentBrandStores || currentBrandStores.length === 0) return []
    const actualSelectedStores = selectedStores.filter(store => store !== 'all-stores')
    if (actualSelectedStores.length === 0) return currentBrandStores.map(store => store.id)

    return actualSelectedStores
  }, [selectedStores, currentBrandStores])

  const { data, isLoading, isError } = usePaymentMethodRevenue({
    startDate: startOfDay,
    endDate: endOfDay,
    selectedStoreIds,
    companyUid: company?.id || '',
    brandUid: selectedBrand?.id || '',
    limit: 5,
    enabled: !!(company?.id && selectedBrand?.id && selectedStoreIds.length > 0)
  })

  return (
    <Card className='col-span-1 h-[500px]'>
      <CardHeader>
        <CardTitle>Top 5 phương thức thanh toán</CardTitle>
        <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        <CardAction>
          {/* <button className='cursor-pointer text-xs text-blue-600 hover:text-blue-800'>Chi tiết</button> */}
        </CardAction>
      </CardHeader>
      <CardContent>
        <PaymentMethodsPieChart data={data} isLoading={isLoading} isError={isError} />
      </CardContent>
    </Card>
  )
}
