import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, ReferenceLine } from 'recharts'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'
import { DateRangePicker } from './components'

// Mock data based on API response structure
const mockVoucherData = {
  amount_before_discount: 0,
  amount_discount: 0,
  report_by_pos: [],
  report_voucher_by_campaign: [],
  number_of_voucher_used: 0,
  number_of_voucher_published: 0
}

const revenueChartData = [
  { name: '1', '<PERSON>anh thu': 0, 'Giảm giá': 0 },
  { name: '2', '<PERSON>anh thu': 0, 'Giảm giá': 0 },
  { name: '3', '<PERSON><PERSON><PERSON> thu': 0, '<PERSON><PERSON><PERSON><PERSON> giá': 0 },
  { name: '4', '<PERSON><PERSON><PERSON> thu': 0, '<PERSON><PERSON><PERSON><PERSON> giá': 0 },
  { name: '5', '<PERSON><PERSON><PERSON> thu': 0, 'Gi<PERSON>m giá': 0 },
  { name: '6', 'Doanh thu': 0, 'Giảm giá': 0 },
  { name: '7', 'Doanh thu': 0, 'Giảm giá': 0 },
]

const voucherChartData = [
  { name: '1', 'Phát hành': 0, 'Sử dụng': 0 },
  { name: '2', 'Phát hành': 0, 'Sử dụng': 0 },
  { name: '3', 'Phát hành': 0, 'Sử dụng': 0 },
  { name: '4', 'Phát hành': 0, 'Sử dụng': 0 },
  { name: '5', 'Phát hành': 0, 'Sử dụng': 0 },
  { name: '6', 'Phát hành': 0, 'Sử dụng': 0 },
  { name: '7', 'Phát hành': 0, 'Sử dụng': 0 },
]

// Store voucher data will come from mockVoucherData.report_by_pos

export function VoucherReportPage() {
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [dateRange, setDateRange] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activePreset, setActivePreset] = useState<string>('7days')

  // Initialize with 7 days preset
  useEffect(() => {
    handlePresetDate('7days')
  }, [])

  // Format date range for display
  const formatDateRange = (start: Date, end: Date) => {
    return `${format(start, 'dd/MM/yyyy')} - ${format(end, 'dd/MM/yyyy')}`
  }

  // Handle preset date selection
  const handlePresetDate = (preset: string) => {
    const today = new Date()
    let start: Date
    let end: Date = endOfDay(today)

    switch (preset) {
      case 'today':
        start = startOfDay(today)
        break
      case 'yesterday':
        start = startOfDay(subDays(today, 1))
        end = endOfDay(subDays(today, 1))
        break
      case '7days':
        start = startOfDay(subDays(today, 6))
        break
      case '15days':
        start = startOfDay(subDays(today, 14))
        break
      case '30days':
        start = startOfDay(subDays(today, 29))
        break
      default:
        return
    }

    setStartDate(start)
    setEndDate(end)
    setDateRange(formatDateRange(start, end))
    setActivePreset(preset)
  }

  // Handle manual date range input
  const handleDateRangeChange = (value: string) => {
    setDateRange(value)
    setActivePreset('')
    
    // Try to parse the date range
    const dateRegex = /(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})/
    const match = value.match(dateRegex)
    
    if (match) {
      try {
        const [, startStr, endStr] = match
        const [startDay, startMonth, startYear] = startStr.split('/')
        const [endDay, endMonth, endYear] = endStr.split('/')
        
        const parsedStart = new Date(parseInt(startYear), parseInt(startMonth) - 1, parseInt(startDay))
        const parsedEnd = new Date(parseInt(endYear), parseInt(endMonth) - 1, parseInt(endDay))
        
        if (!isNaN(parsedStart.getTime()) && !isNaN(parsedEnd.getTime())) {
          setStartDate(startOfDay(parsedStart))
          setEndDate(endOfDay(parsedEnd))
        }
      } catch (error) {
        console.error('Error parsing date range:', error)
      }
    }
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">BÁO CÁO VOUCHER</h1>
      </div>

      {/* Date Filter Controls */}
      <div className="flex items-center justify-end gap-4">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'today' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('today')}
          >
            Hôm nay
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'yesterday' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('yesterday')}
          >
            Hôm qua
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '7days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('7days')}
          >
            7 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '15days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('15days')}
          >
            15 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '30days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('30days')}
          >
            30 ngày trước
          </Button>
        </div>
        <DateRangePicker
          startDate={startDate}
          endDate={endDate}
          onDateChange={(start: Date | null, end: Date | null) => {
            setStartDate(start)
            setEndDate(end)
            if (start && end) {
              setDateRange(formatDateRange(start, end))
              setActivePreset('')
            }
          }}
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{mockVoucherData.number_of_voucher_published}</div>
            <div className="text-sm text-gray-600 mt-1">VOUCHER PHÁT HÀNH</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{mockVoucherData.number_of_voucher_used}</div>
            <div className="text-sm text-gray-600 mt-1">VOUCHER SỬ DỤNG</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{mockVoucherData.amount_before_discount.toLocaleString()} VND</div>
            <div className="text-sm text-gray-600 mt-1">DOANH THU TRƯỚC GIẢM GIÁ</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-900">{mockVoucherData.amount_discount.toLocaleString()} VND</div>
            <div className="text-sm text-gray-600 mt-1">CHI PHÍ GIẢM GIÁ</div>
          </CardContent>
        </Card>
      </div>

      {/* Revenue Chart */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex justify-center gap-6 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-sm text-gray-600">Doanh thu</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              <span className="text-sm text-gray-600">Giảm giá</span>
            </div>
          </div>
          <div className="h-120">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={revenueChartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <XAxis dataKey="name" hide />
                <YAxis
                  domain={[0, 1]}
                  ticks={[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]}
                  tickFormatter={(value) => value === 0 ? '0' : value.toFixed(1)}
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                  width={40}
                />
                {/* Custom grid lines */}
                {[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0].map(value => (
                  <ReferenceLine
                    key={value}
                    y={value}
                    stroke="#e5e7eb"
                    strokeDasharray="none"
                  />
                ))}
                <Line
                  type="monotone"
                  dataKey="Doanh thu"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="Giảm giá"
                  stroke="#f97316"
                  strokeWidth={2}
                  dot={{ fill: '#f97316', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#f97316', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Voucher Usage Chart */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex justify-center gap-6 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-sm text-gray-600">Phát hành</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              <span className="text-sm text-gray-600">Sử dụng</span>
            </div>
          </div>
          <div className="h-120">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={voucherChartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <XAxis dataKey="name" hide />
                <YAxis
                  domain={[0, 1]}
                  ticks={[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]}
                  tickFormatter={(value) => value === 0 ? '0' : value.toFixed(1)}
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#6b7280' }}
                  width={40}
                />
                {/* Custom grid lines */}
                {[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0].map(value => (
                  <ReferenceLine
                    key={value}
                    y={value}
                    stroke="#e5e7eb"
                    strokeDasharray="none"
                  />
                ))}
                <Line
                  type="monotone"
                  dataKey="Phát hành"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#3b82f6', strokeWidth: 2 }}
                />
                <Line
                  type="monotone"
                  dataKey="Sử dụng"
                  stroke="#f97316"
                  strokeWidth={2}
                  dot={{ fill: '#f97316', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#f97316', strokeWidth: 2 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Store Voucher Usage Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base font-medium text-gray-700 text-center">
            SỬ DỤNG VOUCHER THEO CỬA HÀNG
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search Input */}
          <div className="mb-4 flex justify-end">
            <Input
              placeholder="Tìm kiếm nhanh"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 text-xs"
            />
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-3 font-medium text-gray-700">Cửa hàng</th>
                  <th className="text-center p-3 font-medium text-gray-700">Voucher sử dụng</th>
                  <th className="text-center p-3 font-medium text-gray-700">Doanh thu trước giảm giá</th>
                  <th className="text-center p-3 font-medium text-gray-700">Doanh thu sau giảm giá</th>
                  <th className="text-center p-3 font-medium text-gray-700">Chi phí giảm giá</th>
                </tr>
              </thead>
              <tbody>
                {mockVoucherData.report_by_pos.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="text-center p-8 text-gray-500">
                      No data available in table
                    </td>
                  </tr>
                ) : (
                  mockVoucherData.report_by_pos
                    .filter((store: any) =>
                      store.pos_name?.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map((store: any, index: number) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-3 text-gray-900">{store.pos_name || 'N/A'}</td>
                        <td className="p-3 text-center text-gray-900">{store.number_of_voucher_used || 0}</td>
                        <td className="p-3 text-center text-gray-900">
                          {(store.amount_before_discount || 0).toLocaleString()} VND
                        </td>
                        <td className="p-3 text-center text-gray-900">
                          {((store.amount_before_discount || 0) - (store.amount_discount || 0)).toLocaleString()} VND
                        </td>
                        <td className="p-3 text-center text-gray-900">
                          {(store.amount_discount || 0).toLocaleString()} VND
                        </td>
                      </tr>
                    ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
