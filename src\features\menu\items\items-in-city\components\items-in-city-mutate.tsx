import { useEffect, useState } from 'react'

import { z } from 'zod'

import { type SubmitHandler, useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { X } from 'lucide-react'

import { useAuthStore } from '@/stores/authStore'

import { useCitiesData, useItemClassesData, useItemTypesData, useUnitsData } from '@/hooks/api'
import { useImageUpload } from '@/hooks/api/use-images'

import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'

import { ItemsInCity } from '../data'
import {
  useCreateItemInCity,
  useItemByListId,
  useItemInCityDetail,
  useUpdateItemInCity,
  useUpdateItemInCityStatus
} from '../hooks'
import type { CreateItemInCityRequest, UpdateItemInCityRequest } from '../hooks/items-in-city-api'
import { ItemFormSections } from './item-form-sections'
import { PriceSourceDialog } from './price-source-dialog'

const formSchema = z.object({
  item_name: z.string().min(1, 'Tên món là bắt buộc'),
  ots_price: z.coerce.number().min(0, 'Giá phải lớn hơn hoặc bằng 0'),
  ta_price: z.coerce.number().min(0, 'Giá phải lớn hơn hoặc bằng 0'),
  description: z.string().optional(),
  item_id_barcode: z
    .string()
    .optional()
    .refine(
      val => !val || /^[a-zA-Z0-9]*$/.test(val),
      'Mã barcode chỉ được chứa chữ cái và số, không có dấu cách hoặc ký tự đặc biệt'
    ),
  item_id_mapping: z.string().optional(),
  is_eat_with: z.preprocess(val => Boolean(val), z.boolean()),
  is_featured: z.preprocess(val => Boolean(val), z.boolean()),
  item_class_uid: z.string().optional(),
  item_type_uid: z.string().min(1, 'Vui lòng chọn loại món'),
  city_uid: z.string().min(1, 'Vui lòng chọn thành phố'),
  sku: z.string().max(50, 'SKU không được vượt quá 50 ký tự').optional(),
  item_id: z.string().optional(),
  enable_custom_item_id: z.preprocess(val => Boolean(val), z.boolean()),
  unit_uid: z.string().min(1, 'Vui lòng chọn đơn vị tính'),
  unit_secondary_uid: z.string().optional(),
  ots_tax: z.coerce.number().min(0).max(100).default(0),
  ta_tax: z.coerce.number().min(0).max(100).default(0),
  time_cooking: z.coerce.number().min(0).default(0),
  enable_edit_price: z.coerce.number().min(0).default(0),
  is_print_label: z.preprocess(val => Boolean(val), z.boolean()),
  is_allow_discount: z.preprocess(val => Boolean(val), z.boolean()),
  is_virtual_item: z.coerce.number().min(0).max(1).default(0),
  is_item_service: z.coerce.number().min(0).max(1).default(0),
  is_buffet_item: z.coerce.number().min(0).max(1).default(0),
  no_update_quantity_toping: z.coerce.number().min(0).max(1).default(0),
  formula_qrcode: z.string().optional(),
  is_service: z.preprocess(val => Boolean(val), z.boolean()),
  price_by_source: z.array(z.any()).default([]),
  exclude_items_buffet: z.array(z.any()).default([]),
  up_size_buffet: z.array(z.any()).default([]),
  cross_price: z
    .array(
      z.object({
        quantity: z.number(),
        price: z.number()
      })
    )
    .default([]),
  time_sale_date_week: z.coerce.number().default(0),
  time_sale_hour_day: z.coerce.number().default(0),
  sort: z.coerce.number().min(0).default(0),
  sort_online: z.coerce.number().min(0).default(1000),
  store_uid: z.string().optional(),
  customization_uid: z.string().optional(),

  allow_take_away: z.coerce.number().min(0).max(1).default(1),
  list_order: z.coerce.number().min(0).default(0),
  is_material: z.coerce.number().min(0).max(1).default(0),
  quantity_default: z.coerce.number().min(0).default(0),
  price_change: z.coerce.number().min(0).default(0),
  point: z.coerce.number().min(0).default(0),
  is_gift: z.coerce.number().min(0).max(1).default(0),
  is_fc: z.coerce.number().min(0).max(1).default(0),
  show_on_web: z.coerce.number().min(0).max(1).default(0),
  show_price_on_web: z.coerce.number().min(0).max(1).default(0),
  cost_price: z.coerce.number().min(0).default(0),
  quantity_limit: z.coerce.number().min(0).default(0),
  is_kit: z.coerce.number().min(0).max(1).default(0),
  process_index: z.coerce.number().min(0).default(0),
  quantity_per_day: z.coerce.number().min(0).default(0),
  is_parent: z.coerce.number().min(0).max(1).default(0),
  is_sub: z.coerce.number().min(0).max(1).default(0),
  effective_date: z.coerce.number().min(0).default(0),
  expire_date: z.coerce.number().min(0).default(0)
})

export type FormValues = z.infer<typeof formSchema>

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: ItemsInCity
  isCopyMode?: boolean
}

export function ItemsInCityMutate({ open, onOpenChange, currentRow, isCopyMode = false }: Props) {
  const isUpdate = !!currentRow && !isCopyMode
  const isCopy = !!currentRow && isCopyMode
  const [openDialog, setOpenDialog] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null)
  const [priceSources, setPriceSources] = useState<
    Array<{
      source_id: string
      source: string
      amount: number
      price: number
    }>
  >([])

  const handlePriceSourceConfirm = (data: { source: string; amount: number; sourceName?: string }) => {
    const newSource = {
      source_id: data.source,
      source: data.sourceName || data.source,
      amount: data.amount,
      price: data.amount
    }
    const updatedSources = [...priceSources, newSource]
    setPriceSources(updatedSources)

    form.setValue('price_by_source', updatedSources)

    setOpenDialog(false)
  }

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { data: cities = [] } = useCitiesData()
  const { data: itemTypes = [] } = useItemTypesData()
  const { data: units = [] } = useUnitsData()
  const { data: itemClasses = [] } = useItemClassesData({ skip_limit: true })
  const { data: itemDetail } = useItemInCityDetail(currentRow?.id, isUpdate && open)

  const { data: itemDetailForCopy } = useItemByListId(
    {
      company_uid: company?.id || '',
      brand_uid: selectedBrand?.id || '',
      list_item_id: currentRow?.code || ''
    },
    isCopy && open
  )

  const activeItemDetail = isCopy ? itemDetailForCopy : itemDetail
  const createItemMutation = useCreateItemInCity()
  const updateItemMutation = useUpdateItemInCity()
  const updateStatusMutation = useUpdateItemInCityStatus()
  const { mutate: uploadImage } = useImageUpload()

  const itemData = activeItemDetail?.data as Record<string, unknown> | undefined
  const extraData = (itemData?.extra_data as Record<string, unknown>) || {}

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      item_name: (itemData?.item_name as string) || '',
      ots_price: Number(itemData?.ots_price) || 0,
      description: (itemData?.description as string) || '',
      item_id_barcode: (itemData?.item_id_barcode as string) || '',
      is_eat_with: Boolean(itemData?.is_eat_with),
      is_featured: Boolean(itemData?.is_featured),
      item_class_uid: (itemData?.item_class_uid as string) || '',
      item_type_uid: (itemData?.item_type_uid as string) || '',
      city_uid: (itemData?.city_uid as string) || '',
      sku: (itemData?.sku as string) || '',
      item_id: (itemData?.item_id as string) || '',
      enable_custom_item_id: Boolean(itemData?.item_id && (itemData?.item_id as string).trim() !== ''),
      unit_uid: (itemData?.unit_uid as string) || '',
      unit_secondary_uid: (itemData?.unit_secondary_uid as string) || '',
      ots_tax: Number(itemData?.ots_tax) || 0,
      time_cooking: Number(itemData?.time_cooking) || 0,
      ta_price: Number(itemData?.ta_price) || 0,
      ta_tax: Number(itemData?.ta_tax) || 0,
      item_id_mapping: (itemData?.item_id_mapping as string) || '',
      enable_edit_price: Number(extraData?.enable_edit_price) || 0,
      is_print_label: Boolean(itemData?.is_print_label) || false,
      is_allow_discount: Boolean(itemData?.is_allow_discount) || false,
      is_virtual_item: Number(extraData?.is_virtual_item) || 0,
      is_item_service: Number(extraData?.is_item_service) || 0,
      is_buffet_item: Number(extraData?.is_buffet_item) || (currentRow?.buffetConfig === 'Áp dụng buffet' ? 1 : 0),
      no_update_quantity_toping: Number(extraData?.no_update_quantity_toping) || 0,
      formula_qrcode: (extraData?.formula_qrcode as string) || '',
      is_service: Boolean(extraData?.is_item_service) || false,
      price_by_source: [],
      exclude_items_buffet: [],
      up_size_buffet: [],
      cross_price: [],
      time_sale_date_week: Number(itemData?.time_sale_date_week) || 0,
      time_sale_hour_day: Number(itemData?.time_sale_hour_day) || 0,
      sort: Number(itemData?.sort) || 0,
      store_uid: '',
      customization_uid: 'none'
    }
  })

  useEffect(() => {
    if (!open) {
      setImagePreview(null)
      setSelectedImageFile(null)
      setPriceSources([])
    }
  }, [open])

  useEffect(() => {
    if ((isUpdate || isCopy) && activeItemDetail?.data && open) {
      const resetItemData = activeItemDetail.data as Record<string, unknown>
      const resetExtraData = (resetItemData?.extra_data as Record<string, unknown>) || {}
      if (resetItemData.image_path && typeof resetItemData.image_path === 'string') {
        setImagePreview(resetItemData.image_path)
      }

      const existingPriceSources = (resetExtraData?.price_by_source as Array<any>) || []
      setPriceSources(existingPriceSources)

      form.reset({
        item_name: (resetItemData?.item_name as string) || '',
        ots_price: resetItemData?.ots_price as number,
        description: (resetItemData?.description as string) || '',
        item_id_barcode: (resetItemData?.item_id_barcode as string) || '',
        is_eat_with: Boolean(resetItemData?.is_eat_with),
        is_featured: Boolean(resetItemData?.is_featured),
        item_class_uid: (resetItemData?.item_class_uid as string) || '',
        item_type_uid: (resetItemData?.item_type_uid as string) || '',
        city_uid: (resetItemData?.city_uid as string) || '',
        sku: (resetItemData?.sku as string) || '',
        item_id: isCopy ? '' : (resetItemData?.item_id as string) || '', // Để trống mã khi copy
        unit_uid: (resetItemData?.unit_uid as string) || '',
        unit_secondary_uid: (resetItemData?.unit_secondary_uid as string) || '',
        ots_tax: resetItemData?.ots_tax as number,
        time_cooking: resetItemData?.time_cooking as number,
        ta_price: resetItemData?.ta_price as number,
        ta_tax: resetItemData?.ta_tax as number,
        item_id_mapping: (resetItemData?.item_id_mapping as string) || '',
        enable_edit_price: Number(resetExtraData?.enable_edit_price) || 0,
        is_print_label: Boolean(resetItemData?.is_print_label) || false,
        is_allow_discount: Boolean(resetItemData?.is_allow_discount) || false,
        is_virtual_item: Number(resetExtraData?.is_virtual_item) || 0,
        is_item_service: Number(resetExtraData?.is_item_service) || 0,
        is_buffet_item:
          Number(resetExtraData?.is_buffet_item) || (currentRow?.buffetConfig === 'Áp dụng buffet' ? 1 : 0),
        no_update_quantity_toping: Number(resetExtraData?.no_update_quantity_toping) || 0,
        formula_qrcode: (resetExtraData?.formula_qrcode as string) || '',
        is_service: Boolean(resetExtraData?.is_item_service) || false,
        price_by_source: existingPriceSources,
        exclude_items_buffet: [],
        up_size_buffet: [],
        cross_price: (resetExtraData?.cross_price as Array<{ quantity: number; price: number }>) || [],
        time_sale_date_week: (resetItemData?.time_sale_date_week as number) || 0,
        time_sale_hour_day: (resetItemData?.time_sale_hour_day as number) || 0,
        sort: resetItemData?.sort as number,
        sort_online: (resetItemData?.sort_online as number) || 1000,
        customization_uid: (resetItemData?.customization_uid as string) || 'none',
        // Additional fields from curl data
        allow_take_away: (resetItemData?.allow_take_away as number) || 1,
        list_order: (resetItemData?.list_order as number) || 0,
        is_material: (resetItemData?.is_material as number) || 0,
        quantity_default: (resetItemData?.quantity_default as number) || 0,
        price_change: (resetItemData?.price_change as number) || 0,
        point: (resetItemData?.point as number) || 0,
        is_gift: (resetItemData?.is_gift as number) || 0,
        is_fc: (resetItemData?.is_fc as number) || 0,
        show_on_web: (resetItemData?.show_on_web as number) || 0,
        show_price_on_web: (resetItemData?.show_price_on_web as number) || 0,
        cost_price: (resetItemData?.cost_price as number) || 0,
        quantity_limit: (resetItemData?.quantity_limit as number) || 0,
        is_kit: (resetItemData?.is_kit as number) || 0,
        process_index: (resetItemData?.process_index as number) || 0,
        quantity_per_day: (resetItemData?.quantity_per_day as number) || 0,
        is_parent: (resetItemData?.is_parent as number) || 0,
        is_sub: (resetItemData?.is_sub as number) || 0,
        effective_date: (resetItemData?.effective_date as number) || 0,
        expire_date: (resetItemData?.expire_date as number) || 0
      })
    }
  }, [isUpdate, isCopy, activeItemDetail, open, form, currentRow?.buffetConfig])

  const onSubmit: SubmitHandler<FormValues> = async data => {
    try {
      if (!company?.id || !selectedBrand?.id) {
        return
      }

      let imageUrl = ''
      if (selectedImageFile) {
        try {
          const uploadResponse = await new Promise<{ data: { image_url: string } }>((resolve, reject) => {
            uploadImage(selectedImageFile, {
              onSuccess: data => resolve(data),
              onError: error => reject(error)
            })
          })
          imageUrl = uploadResponse.data.image_url
        } catch (error) {}
      }

      const requestData = isUpdate
        ? {
            id: currentRow?.id || '',
            item_id:
              data.enable_custom_item_id && data.item_id
                ? data.item_id
                : (itemData?.item_id as string) || currentRow?.originalData?.item_id || '',
            item_name: data.item_name,
            description: data.description || '',
            ots_price: data.ots_price,
            ots_tax: data.ots_tax,
            ta_price: data.ta_price,
            ta_tax: data.ta_tax,
            time_sale_hour_day: data.time_sale_hour_day,
            time_sale_date_week: data.time_sale_date_week,
            allow_take_away: data.allow_take_away,
            is_eat_with: data.is_eat_with ? 1 : 0,
            image_path: imageUrl || (itemData?.image_path as string) || '',
            image_path_thumb: imageUrl || (itemData?.image_path_thumb as string) || '',
            item_color: (itemData?.item_color as string) || '',
            list_order: data.list_order,
            is_service: data.is_service ? 1 : 0,
            is_material: data.is_material,
            active: 1,
            user_id: (itemData?.user_id as string) || '',
            is_foreign: 0,
            quantity_default: data.quantity_default,
            price_change: data.price_change,
            currency_type_id: (itemData?.currency_type_id as string) || '',
            point: data.point,
            is_gift: data.is_gift,
            is_fc: data.is_fc,
            show_on_web: data.show_on_web,
            show_price_on_web: data.show_price_on_web,
            cost_price: data.cost_price,
            is_print_label: data.is_print_label ? 1 : 0,
            quantity_limit: data.quantity_limit,
            is_kit: data.is_kit,
            time_cooking: data.time_cooking || 0,
            item_id_barcode: data.item_id_barcode || '',
            sku: data.item_id_mapping || (itemData?.sku as string) || '',
            process_index: data.process_index,
            is_allow_discount: data.is_allow_discount ? 1 : 0,
            quantity_per_day: data.quantity_per_day,
            item_id_eat_with: (itemData?.item_id_eat_with as string) || '',
            is_parent: data.is_parent,
            is_sub: data.is_sub,
            item_id_mapping: data.item_id_mapping || '',
            effective_date: data.effective_date,
            expire_date: data.expire_date,
            sort: data.sort || 0,
            sort_online: data.sort_online,
            extra_data: {
              ...(extraData || {}),
              price_by_source: data.price_by_source,
              is_virtual_item: data.is_virtual_item,
              is_item_service: data.is_item_service,
              no_update_quantity_toping: data.no_update_quantity_toping,
              enable_edit_price: data.enable_edit_price,
              is_buffet_item: data.is_buffet_item,
              exclude_items_buffet: data.exclude_items_buffet,
              up_size_buffet: data.up_size_buffet,
              cross_price: data.cross_price,
              formula_qrcode: data.formula_qrcode || ''
            },
            revision: (itemData?.revision as number) || 0,
            unit_uid: data.unit_uid,
            unit_secondary_uid: data.unit_secondary_uid || null,
            item_type_uid: data.item_type_uid,
            item_class_uid: data.item_class_uid || null,
            source_uid: null,
            brand_uid: selectedBrand.id,
            city_uid: data.city_uid,
            company_uid: company.id,
            customization_uid: data.customization_uid === 'none' ? null : data.customization_uid || null,
            is_fabi: 1,
            deleted: false,
            created_by: (itemData?.created_by as string) || '',
            updated_by: (itemData?.updated_by as string) || '',
            deleted_by: null,
            created_at: (itemData?.created_at as number) || 0,
            updated_at: Math.floor(Date.now() / 1000),
            deleted_at: null,
            cities: (itemData?.cities as unknown[]) || [],
            item_old: {}
          }
        : {
            unit_uid: data.unit_uid,
            ta_price: data.ta_price,
            ots_tax: data.ots_tax,
            ta_tax: data.ta_tax,
            description: data.description,
            item_id_barcode: data.item_id_barcode,
            sku: data.sku || '',
            is_eat_with: data.is_eat_with ? 1 : 0,
            time_cooking: data.time_cooking,
            is_print_label: data.is_print_label ? 1 : 0,
            is_allow_discount: data.is_allow_discount ? 1 : 0,
            is_service: data.is_service ? 1 : 0,
            unit_secondary_uid: data.unit_secondary_uid || null,
            item_class_uid: data.item_class_uid,
            extra_data: {
              price_by_source: data.price_by_source,
              is_virtual_item: data.is_virtual_item,
              is_item_service: data.is_item_service,
              no_update_quantity_toping: data.no_update_quantity_toping,
              enable_edit_price: data.enable_edit_price,
              is_buffet_item: data.is_buffet_item,
              exclude_items_buffet: data.exclude_items_buffet,
              up_size_buffet: data.up_size_buffet,
              cross_price: data.cross_price,
              formula_qrcode: data.formula_qrcode || ''
            },
            item_type_uid: data.item_type_uid,
            item_name: data.item_name,
            city_uid: data.city_uid,
            company_uid: company.id,
            brand_uid: selectedBrand.id,
            item_id:
              data.enable_custom_item_id && data.item_id
                ? data.item_id
                : `ITEM-${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
            ots_price: data.ots_price,
            sort: data.sort || 1000,
            customization_uid: data.customization_uid === 'none' ? null : data.customization_uid || null,
            time_sale_hour_day: data.time_sale_hour_day,
            time_sale_date_week: data.time_sale_date_week,
            allow_take_away: data.allow_take_away,
            list_order: data.list_order,
            is_material: data.is_material,
            quantity_default: data.quantity_default,
            price_change: data.price_change,
            point: data.point,
            is_gift: data.is_gift,
            is_fc: data.is_fc,
            show_on_web: data.show_on_web,
            show_price_on_web: data.show_price_on_web,
            cost_price: data.cost_price,
            quantity_limit: data.quantity_limit,
            is_kit: data.is_kit,
            process_index: data.process_index,
            quantity_per_day: data.quantity_per_day,
            is_parent: data.is_parent,
            is_sub: data.is_sub,
            item_id_mapping: data.item_id_mapping,
            effective_date: data.effective_date,
            expire_date: data.expire_date,
            sort_online: data.sort_online
          }

      if (isUpdate && currentRow?.id) {
        updateItemMutation.mutate(requestData as unknown as UpdateItemInCityRequest, {
          onSuccess: () => {
            onOpenChange(false)
            form.reset()
            setPriceSources([])
          }
        })
      } else {
        createItemMutation.mutate(requestData as unknown as CreateItemInCityRequest, {
          onSuccess: () => {
            onOpenChange(false)
            form.reset()
            setPriceSources([])
          }
        })
      }
    } catch (error) {}
  }

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = e => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)

      setSelectedImageFile(file)
    }
  }

  const handleClose = () => {
    onOpenChange(false)
    form.reset()
    setPriceSources([])
  }

  const handleDeactive = async () => {
    if (!currentRow?.id || !company?.id || !selectedBrand?.id) return

    try {
      await updateStatusMutation.mutateAsync({
        id: currentRow.id,
        active: 0
      })
      onOpenChange(false)
    } catch (error) {}
  }

  const handleActive = async () => {
    if (!currentRow?.id || !company?.id || !selectedBrand?.id) return

    try {
      await updateStatusMutation.mutateAsync({
        id: currentRow.id,
        active: 1
      })
      onOpenChange(false)
    } catch (error) {}
  }

  if (!open) return null

  return (
    <>
      <div className='container mx-auto px-4 py-8'>
        <div className='mb-8'>
          <div className='mb-4 flex items-center justify-between'>
            <div>
              <Button variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
                <X className='h-4 w-4' />
              </Button>
            </div>
            <div className='flex-1 text-center'>
              <h1 className='mb-2 text-3xl font-bold'>
                {isUpdate ? 'Chi tiết món' : isCopy ? 'Sao chép món' : 'Tạo món'}
              </h1>
            </div>
            <div className='flex gap-2'>
              {isUpdate && currentRow?.isActive && (
                <Button
                  type='button'
                  variant='outline'
                  className='border-red-500 text-red-500 hover:bg-red-50'
                  disabled={updateStatusMutation.isPending}
                  onClick={handleDeactive}
                >
                  {updateStatusMutation.isPending ? 'Đang deactive...' : 'Deactive'}
                </Button>
              )}
              {isUpdate && !currentRow?.isActive && (
                <Button
                  type='button'
                  variant='outline'
                  className='border-green-500 text-green-500 hover:bg-green-50'
                  disabled={updateStatusMutation.isPending}
                  onClick={handleActive}
                >
                  {updateStatusMutation.isPending ? 'Đang active...' : 'Active'}
                </Button>
              )}

              <Button
                type='button'
                disabled={createItemMutation.isPending || updateItemMutation.isPending}
                onClick={async () => {
                  const isValid = await form.trigger()

                  if (isValid) {
                    form.handleSubmit(onSubmit)()
                  }
                }}
              >
                {createItemMutation.isPending || updateItemMutation.isPending
                  ? 'Đang lưu và đồng bộ...'
                  : 'Lưu và đồng bộ'}
              </Button>
              <Button
                type='button'
                disabled={createItemMutation.isPending || updateItemMutation.isPending}
                onClick={async () => {
                  const isValid = await form.trigger()

                  if (isValid) {
                    form.handleSubmit(onSubmit)()
                  }
                }}
              >
                {createItemMutation.isPending || updateItemMutation.isPending ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </div>
          </div>
        </div>

        <div className='mx-auto max-w-4xl'>
          <div className='rounded-lg border bg-white p-6 shadow-sm'>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
                <ItemFormSections
                  form={form}
                  itemTypes={itemTypes}
                  itemClasses={itemClasses}
                  units={units}
                  cities={cities}
                  imageFile={selectedImageFile}
                  onImageChange={handleImageChange}
                  imagePreview={imagePreview}
                  onImageRemove={() => {
                    setImagePreview(null)
                    setSelectedImageFile(null)
                  }}
                  currentRow={currentRow}
                />
              </form>
            </Form>
          </div>
        </div>
      </div>

      {/* PriceSourceDialog rendered at root level to prevent layout issues */}
      <PriceSourceDialog
        open={openDialog}
        onOpenChange={setOpenDialog}
        cityUid={form.watch('city_uid') || ''}
        onConfirm={handlePriceSourceConfirm}
      />
    </>
  )
}
