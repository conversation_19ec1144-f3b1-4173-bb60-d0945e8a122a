import { format } from 'date-fns'

import { ColumnDef } from '@tanstack/react-table'

import { IconCopy } from '@tabler/icons-react'

import { Device } from '@/types/device'
import { vi } from 'date-fns/locale'

import { Button } from '@/components/ui/button'

import { StatusBadge } from '@/components/pos'

const getDeviceTypeLabel = (type: string) => {
  const typeLabels: Record<string, string> = {
    POS: 'POS',
    POS_MINI: 'POS_MINI',
    PDA: 'PDA',
    KDS: 'KDS',
    KDS_ORDER_CONTROL: 'KDS ORDER CONTROL',
    KDS_MAKER: 'KDS MAKER',
    SELF_ORDER: 'SELF ORDER'
  }
  return typeLabels[type] || type
}

const getDeviceTypeLocalLabel = (deviceTypeLocal: number) => {
  const typeLocalLabels: Record<number, string> = {
    2: '<PERSON><PERSON>y trạm',
    1: '<PERSON><PERSON><PERSON> chủ',
    0: 'None'
  }
  return typeLocalLabels[deviceTypeLocal] || 'None'
}

export const deviceColumns: ColumnDef<Device>[] = [
  {
    accessorKey: 'id',
    header: '#',
    cell: ({ row }) => {
      const index = row.index + 1
      return <div className='w-[50px] font-medium'>{index}</div>
    },
    enableSorting: false
  },
  {
    accessorKey: 'name',
    header: 'Tên thiết bị',
    cell: ({ row }) => {
      const device = row.original
      return <span className='font-medium'>{device.name}</span>
    }
  },
  {
    accessorKey: 'type',
    header: 'Loại thiết bị',
    cell: ({ row }) => {
      const type = row.getValue('type') as string
      const device = row.original
      return (
        <span className='font-medium'>
          {getDeviceTypeLabel(type)} -{' '}
          <span className='text-gray-500'>{getDeviceTypeLocalLabel(device.device_type_local || 0)}</span>
        </span>
      )
    }
  },
  {
    accessorKey: 'version',
    header: 'Phiên bản',
    cell: ({ row }) => {
      return <span className='text-sm font-medium'>{row.getValue('version')}</span>
    }
  },
  {
    accessorKey: 'storeName',
    header: 'Cửa hàng',
    cell: ({ row }) => {
      const device = row.original
      return <span className='font-medium'>{device.storeName}</span>
    }
  },

  {
    accessorKey: 'lastUpdate',
    header: 'Thời gian cập nhật',
    cell: ({ row }) => {
      const date = row.getValue('lastUpdate') as Date
      return <span className='text-sm font-medium'>{format(date, 'dd/MM/yyyy HH:mm', { locale: vi })}</span>
    }
  },
  {
    id: 'copy',
    header: 'Sao chép thiết bị',
    cell: ({ row, table }) => {
      const device = row.original
      const meta = table.options.meta as {
        onCopyDevice?: (device: Device) => void
      }

      return (
        <Button
          variant='ghost'
          size='sm'
          onClick={e => {
            e.stopPropagation()
            meta?.onCopyDevice?.(device)
          }}
          className='h-8 w-8 p-0'
        >
          <IconCopy className='h-4 w-4' />
          <span className='sr-only'>Sao chép thiết bị {device.name}</span>
        </Button>
      )
    }
  },
  {
    accessorKey: 'isActive',
    header: '',
    cell: ({ row }) => {
      const isActive = row.getValue('isActive') as boolean
      return <StatusBadge isActive={isActive} activeText='Active' inactiveText='Deactive' />
    }
  }
]
