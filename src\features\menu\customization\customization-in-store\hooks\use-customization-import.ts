import { useState, useRef } from 'react'

import { ParsedCustomizationData } from '@/types/customizations'
import { toast } from 'sonner'

import { useBulkImportCustomizations } from '@/hooks/api'

import { useExcelParser } from './use-excel-parser'

export function useCustomizationImport(storeUid?: string) {
  const [importSelectedFile, setImportSelectedFile] = useState<File | null>(null)
  const [importParsedData, setImportParsedData] = useState<ParsedCustomizationData[]>([])
  const [showImportParsedData, setShowImportParsedData] = useState(false)
  const importFileInputRef = useRef<HTMLInputElement>(null)

  const { parseExcelFile } = useExcelParser()
  const bulkImportCustomizationsMutation = useBulkImportCustomizations()

  const resetImportState = () => {
    setShowImportParsedData(false)
    setImportParsedData([])
    setImportSelectedFile(null)
  }

  const handleDownloadTemplate = () => {
    const link = document.createElement('a')
    link.href = '/files/customization/customization-in-store/create-customize-store.xlsx'
    link.download = 'create-customize-store.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success('Đã tải file mẫu thành công!')
  }

  const handleImportFileUpload = () => {
    importFileInputRef.current?.click()
  }

  const handleImportFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportSelectedFile(file)

    try {
      const parsedCustomizations = await parseExcelFile(file, [], true)
      setImportParsedData(parsedCustomizations)
      setShowImportParsedData(true)
      toast.success(`Đã phân tích ${parsedCustomizations.length} customization từ file!`)
    } catch {
      // Error handling is done in parseExcelFile
    }
  }

  const handleSaveImportedCustomizations = async () => {
    if (importParsedData.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return false
    }

    try {
      await bulkImportCustomizationsMutation.mutateAsync({
        parsedData: importParsedData,
        storeUid
      })
      toast.success(`Đã tạo thành công ${importParsedData.length} customization!`)
      resetImportState()
      return true
    } catch {
      toast.error('Lỗi khi tạo customization. Vui lòng thử lại.')
      return false
    }
  }

  return {
    importSelectedFile,
    importParsedData,
    showImportParsedData,
    importFileInputRef,
    resetImportState,
    handleDownloadTemplate,
    handleImportFileUpload,
    handleImportFileChange,
    handleSaveImportedCustomizations,
    isLoading: bulkImportCustomizationsMutation.isPending
  }
}
