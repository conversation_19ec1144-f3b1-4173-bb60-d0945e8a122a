import { useCallback } from 'react'

import { UseFormReturn } from 'react-hook-form'

import {
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input
} from '@/components/ui'

import { CrmSettingsFormValues } from '../../data'
import { useSettingsApi } from '../../hooks/use-settings-api'
import { EmailTagsInput } from '../email-tags-input'
import { ImageUploadSection } from './image-upload-section'

interface BrandConfigSectionProps {
  form: UseFormReturn<CrmSettingsFormValues>
  isLoading?: boolean
}

const SaveButton = ({ onSave, isLoading, isSaving }: { onSave: () => void; isLoading: boolean; isSaving: boolean }) => (
  <div className='flex justify-start'>
    <Button type='button' disabled={isLoading || isSaving} onClick={onSave}>
      {isSaving ? 'Đang lưu...' : 'Lưu'}
    </Button>
  </div>
)

export function BrandConfigSection({ form, isLoading = false }: BrandConfigSectionProps) {
  const { saveBrandConfig, isLoading: isSaving } = useSettingsApi()

  const handleSave = useCallback(async () => {
    const values = form.getValues()
    await saveBrandConfig({
      brandName: values.brandName,
      bannerImage: values.bannerImage,
      logo: values.logo,
      hotline: values.hotline,
      email: values.email
    })
  }, [form, saveBrandConfig])

  const handleBannerImageChange = useCallback(
    (url: string) => {
      form.setValue('bannerImage', url)
    },
    [form]
  )

  const handleLogoChange = useCallback(
    (url: string) => {
      form.setValue('logo', url)
    },
    [form]
  )

  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-lg font-semibold text-gray-900'>
          CẤU HÌNH THƯƠNG HIỆU
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-6 p-6'>
        {/* Tên thương hiệu */}
        <FormField
          control={form.control}
          name='brandName'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm font-medium'>Tên thương hiệu</FormLabel>
              <FormControl>
                <Input
                  placeholder='Nhập tên thương hiệu'
                  value={(field.value as string) || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Banner thương hiệu và Logo */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
          <div>
            <FormField
              control={form.control}
              name='bannerImage'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium'>Banner thương hiệu</FormLabel>
                  <ImageUploadSection
                    label='Tải ảnh banner lên'
                    value={field.value}
                    onChange={handleBannerImageChange}
                    disabled={isLoading}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div>
            <FormField
              control={form.control}
              name='logo'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium'>Logo</FormLabel>
                  <ImageUploadSection
                    label='Tải ảnh logo lên'
                    value={field.value}
                    onChange={handleLogoChange}
                    disabled={isLoading}
                    className='rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 text-center'
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Hotline */}
        <FormField
          control={form.control}
          name='hotline'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm font-medium'>Hotline</FormLabel>
              <FormControl>
                <Input
                  placeholder='Nhập số hotline'
                  value={(field.value as string) || ''}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                  name={field.name}
                  ref={field.ref}
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Email nhận thông báo */}
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel className='text-sm font-medium'>Email nhận thông báo</FormLabel>
              <FormControl>
                <EmailTagsInput
                  value={field.value || []}
                  onChange={field.onChange}
                  placeholder='Nhập email và ấn Enter'
                  disabled={isLoading}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <SaveButton onSave={handleSave} isLoading={isLoading} isSaving={isSaving} />
      </CardContent>
    </Card>
  )
}
