import { DataTable, DataTableColumn } from '@/components/ui/data-table'

import { SystemLog } from '../data'

interface SystemLogTableProps {
  logs: SystemLog[]
  isLoading: boolean
  formatTimestamp: (timestamp: string) => string
}

export function SystemLogTable({ logs, isLoading, formatTimestamp }: SystemLogTableProps) {
  const columns: DataTableColumn<SystemLog>[] = [
    {
      key: 'request_path',
      header: 'Thao tác',
      width: '50%',
      render: (value: string) => {
        // Map request path to Vietnamese description
        const actionMap: Record<string, string> = {
          '/crm/setting/crm_config/update': 'SYSTEM_LOG:crm:settingupdate',
          '/crm/setting/update_pos_parent': 'Cập nhật thông tin thương hiệu',
          '/crm/setting/update-setting-config': 'SYSTEM_LOG:crm:settingupdate',
          '/crm/setting/update-setting-loyalty': 'Cậ<PERSON> nhật chương tr<PERSON>nh thành viên',
          '/crm/setting/update-cheat-config': 'Cập nhật cảnh báo giao dịch'
        }
        return actionMap[value] || value
      }
    },
    {
      key: 'user_name',
      header: 'User',
      width: '25%'
    },
    {
      key: 'request_at',
      header: 'Thời gian',
      width: '25%',
      render: (value: string) => formatTimestamp(value)
    }
  ]

  if (isLoading) {
    return (
      <div className='py-8 text-center'>
        <p>Đang tải dữ liệu nhật ký...</p>
      </div>
    )
  }

  return (
    <DataTable
      data={logs}
      columns={columns}
      isLoading={isLoading}
      pageSize={20}
      emptyMessage='Không có dữ liệu nhật ký'
      loadingMessage='Đang tải...'
    />
  )
}
