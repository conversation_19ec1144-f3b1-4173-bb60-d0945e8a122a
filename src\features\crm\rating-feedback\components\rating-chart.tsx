import { StarRating } from './star-rating'

interface RatingData {
  stars: number
  count: number
  percentage: number
}

interface RatingChartProps {
  ratingData: RatingData[]
}

export function RatingChart({ ratingData }: RatingChartProps) {
  return (
    <div className="space-y-3">
      {ratingData.map((data) => (
        <div key={data.stars} className="flex items-center gap-3">
          {/* Star rating */}
          <div className="flex items-center gap-1 w-20">
            <StarRating rating={data.stars} size="sm" />
          </div>
          
          {/* Progress bar */}
          <div className="flex-1 bg-gray-200 rounded-full h-2 relative overflow-hidden">
            <div 
              className="bg-yellow-400 h-full rounded-full transition-all duration-300"
              style={{ width: `${data.percentage}%` }}
            />
          </div>
          
          {/* Percentage */}
          <div className="text-sm text-blue-600 w-12 text-right">
            {data.percentage.toFixed(1)}%
          </div>
        </div>
      ))}
    </div>
  )
}
