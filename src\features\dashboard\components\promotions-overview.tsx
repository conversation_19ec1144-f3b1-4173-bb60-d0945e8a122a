import { useMemo } from 'react'

import { <PERSON>, Bar<PERSON>hart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

import { useDashboardContext } from '../context/dashboard-context'

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('vi-VN').format(amount)
    }

    return (
      <div className='min-w-48 rounded-lg border bg-white p-3 shadow-lg'>
        <p className='mb-2 font-semibold text-gray-900'>{label}</p>
        <div className='space-y-1 text-xs text-gray-600'>
          <p>• Tổng doanh thu: {formatCurrency(data.revenue_net)} đ</p>
          <p>• Tổng giảm giá: {formatCurrency(data.discount_amount)} đ</p>
          <p>• Tổng hóa đơn: {data.total_bill}</p>
        </div>
      </div>
    )
  }

  return null
}

export function PromotionsOverview() {
  const { promotionsData, isPromotionsLoading, promotionsError } = useDashboardContext()

  const chartData = useMemo(() => {
    return promotionsData.map(promotion => ({
      name: promotion.promotion_name,
      total: promotion.revenue_net,
      secondary: promotion.discount_amount,
      revenue_gross: promotion.revenue_gross,
      revenue_net: promotion.revenue_net,
      discount_amount: promotion.discount_amount,
      total_bill: promotion.total_bill,
      commission_amount: promotion.commission_amount,
      deduct_tax_amount: promotion.deduct_tax_amount
    }))
  }, [promotionsData])

  if (isPromotionsLoading) {
    return (
      <div className='flex h-[350px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Đang tải dữ liệu...</div>
      </div>
    )
  }

  if (promotionsError) {
    return (
      <div className='flex h-[350px] items-center justify-center'>
        <div className='text-sm text-red-500'>Lỗi: {promotionsError}</div>
      </div>
    )
  }

  if (!chartData.length) {
    return (
      <div className='flex h-[350px] items-center justify-center'>
        <div className='text-muted-foreground text-sm'>Chưa có thông tin</div>
      </div>
    )
  }

  return (
    <ResponsiveContainer width='100%' height={350}>
      <BarChart data={chartData}>
        <XAxis
          dataKey='name'
          stroke='#888888'
          fontSize={9}
          tickLine={false}
          axisLine={false}
          angle={-45}
          textAnchor='end'
          height={80}
          interval={0}
          tick={{ fontSize: 9, textAnchor: 'end' }}
          tickFormatter={value => {
            // Truncate long promotion names for diagonal display
            return value.length > 15 ? value.substring(0, 15) + '...' : value
          }}
        />
        <YAxis
          stroke='#888888'
          fontSize={12}
          tickLine={false}
          axisLine={false}
          tickFormatter={value => `${(value / 1000000).toFixed(0)}M`}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey='total' fill='currentColor' radius={[4, 4, 0, 0]} className='fill-primary' maxBarSize={40} />
        <Bar dataKey='secondary' fill='#fb923c' radius={[4, 4, 0, 0]} maxBarSize={25} />
      </BarChart>
    </ResponsiveContainer>
  )
}
