import { useState, useEffect } from 'react'
import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { formatNumberDisplay, handleNumberInputChange } from '@/lib/utils'
import {
  formatDate,
  formatSelectedDays,
  formatSelectedHours,
  convertDaysToBitFlags,
  convertHoursToBitFlags,
  convertDateToTimestamp
} from '@/utils/date-utils'

import { useSourcesData } from '@/hooks/api/use-sources'

interface TimeFrameConfig {
  id: string
  amount: string
  startDate: string
  endDate: string
  selectedDays: boolean[]
  selectedHours: Record<string, boolean>
}

import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'

const priceSourceConfigSchema = z.object({
  orderSource: z.string().min(1, 'Vui lòng chọn nguồn đơn'),
  amount: z.string().min(1, 'Vui lòng nhập số tiền'),
  comboCode: z.string().max(50, 'Mã combo tối đa 50 ký tự').optional(),
  autoGenerateCode: z.boolean()
})

type PriceSourceConfigData = z.infer<typeof priceSourceConfigSchema>

interface PriceSourceConfigDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (data: PriceSourceConfigData) => void
  onCancel: () => void
  selectedStoreIds?: string[]
  onOpenTimeFrameConfig?: (sourceName: string, onSave: (data: any) => void, editingData?: any) => void
  editingData?: {
    orderSource: string
    amount: string
    comboCode?: string
    autoGenerateCode: boolean
    timeFrameConfigs?: Array<{
      id: string
      amount: string
      startDate: string
      endDate: string
      selectedDays: boolean[]
      selectedHours: Record<string, boolean>
    }>
    price_times?: Array<{
      price: number
      from_date: number
      to_date: number
      time_sale_date_week: number
      time_sale_hour_day: number
    }>
  } | null
}

export function PriceSourceConfigDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  selectedStoreIds = [],
  onOpenTimeFrameConfig,
  editingData
}: PriceSourceConfigDialogProps) {
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const { data: sourcesData = [] } = useSourcesData({
    company_uid: company?.id,
    brand_uid: selectedBrand?.id,
    skip_limit: true,
    enabled: open && !!company?.id && !!selectedBrand?.id && selectedStoreIds.length > 0,
    list_store_uid: selectedStoreIds.length > 0 ? selectedStoreIds : undefined
  })

  const [timeFrameConfigs, setTimeFrameConfigs] = useState<TimeFrameConfig[]>([])
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
  const [deletingTimeFrameId, setDeletingTimeFrameId] = useState<string | null>(null)

  const convertSelectedDaysToNumbers = (selectedDays: boolean[]): number[] => {
    return selectedDays
      .map((selected, index) => selected ? index : -1)
      .filter(index => index !== -1)
  }

  const convertSelectedHoursToNumbers = (selectedHours: Record<string, boolean>): number[] => {
    return Object.entries(selectedHours)
      .filter(([_, selected]) => selected)
      .map(([hour, _]) => parseInt(hour))
      .sort((a, b) => a - b)
  }

  const convertTimeFrameConfigToAPI = (config: TimeFrameConfig) => {
    const selectedDayNumbers = convertSelectedDaysToNumbers(config.selectedDays)
    const selectedHourNumbers = convertSelectedHoursToNumbers(config.selectedHours)

    return {
      price: parseInt(config.amount) || 0,
      from_date: convertDateToTimestamp(config.startDate, false),
      to_date: convertDateToTimestamp(config.endDate, true),
      time_sale_date_week: convertDaysToBitFlags(selectedDayNumbers),
      time_sale_hour_day: convertHoursToBitFlags(selectedHourNumbers)
    }
  }
  const form = useForm<PriceSourceConfigData>({
    resolver: zodResolver(priceSourceConfigSchema),
    defaultValues: {
      orderSource: '',
      amount: '0',
      comboCode: '',
      autoGenerateCode: true
    }
  })

  useEffect(() => {
    if (editingData && open) {
      form.reset({
        orderSource: editingData.orderSource,
        amount: editingData.amount,
        comboCode: editingData.comboCode || '',
        autoGenerateCode: editingData.autoGenerateCode
      })
      if (editingData.timeFrameConfigs) {
        setTimeFrameConfigs(editingData.timeFrameConfigs)
      }
    } else if (open && !editingData) {
      form.reset({
        orderSource: '',
        amount: '0',
        comboCode: '',
        autoGenerateCode: true
      })
      setTimeFrameConfigs([])
    }
  }, [editingData, open, form])

  const handleConfirm = (data: PriceSourceConfigData) => {
    const price_times = timeFrameConfigs.map(config => convertTimeFrameConfigToAPI(config))

    const dataWithTimeFrames = {
      ...data,
      timeFrameConfigs: timeFrameConfigs,
      price_times: price_times
    }
    onConfirm(dataWithTimeFrames)
    form.reset()
    setTimeFrameConfigs([])
  }

  const handleCancel = () => {
    form.reset()
    onCancel()
  }

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      handleCancel()
    }
    onOpenChange(newOpen)
  }

  const handleDeleteTimeFrame = (id: string) => {
    setDeletingTimeFrameId(id)
    setDeleteConfirmOpen(true)
  }

  const confirmDeleteTimeFrame = () => {
    if (deletingTimeFrameId) {
      setTimeFrameConfigs(prev => prev.filter(config => config.id !== deletingTimeFrameId))
      setDeletingTimeFrameId(null)
    }
    setDeleteConfirmOpen(false)
  }

  const handleEditTimeFrame = (config: TimeFrameConfig) => {
    const sourceName = sourcesData.find(s => s.id === form.watch('orderSource'))?.sourceName || form.watch('orderSource')
    const editingData = {
      amount: config.amount,
      startDate: config.startDate,
      endDate: config.endDate,
      selectedDays: config.selectedDays,
      selectedHours: config.selectedHours
    }
    onOpenTimeFrameConfig?.(sourceName, (data: any) => handleTimeFrameConfigSaved(data, config.id), editingData)
  }

  const handleTimeFrameConfigSaved = (data: any, editingId?: string) => {
    if (editingId) {
      setTimeFrameConfigs(prev => prev.map(config =>
        config.id === editingId
          ? {
              ...config,
              amount: data.amount,
              startDate: data.startDate,
              endDate: data.endDate,
              selectedDays: data.selectedDays,
              selectedHours: data.selectedHours
            }
          : config
      ))
    } else {
      const newConfig: TimeFrameConfig = {
        id: Date.now().toString(),
        amount: data.amount,
        startDate: data.startDate,
        endDate: data.endDate,
        selectedDays: data.selectedDays,
        selectedHours: data.selectedHours
      }
      setTimeFrameConfigs(prev => [...prev, newConfig])
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle className='text-center'>
            {editingData ? 'Chỉnh sửa' : 'Cấu hình'} giá theo nguồn
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleConfirm)} className='space-y-4'>
            {/* Nguồn đơn */}
            <FormField
              control={form.control}
              name='orderSource'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium'>Nguồn đơn</FormLabel>
                  <FormControl>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger className='w-full' onClick={e => e.preventDefault()}>
                        <SelectValue placeholder='Chọn nguồn đơn' />
                      </SelectTrigger>
                      <SelectContent>
                        {sourcesData.map(source => (
                          <SelectItem key={source.id} value={source.id}>
                            {source.sourceName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Số tiền */}
            <FormField
              control={form.control}
              name='amount'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-sm font-medium'>Số tiền</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='0'
                      value={formatNumberDisplay(field.value || '')}
                      onChange={e => handleNumberInputChange(e.target.value, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {form.watch('orderSource') && form.watch('amount') && (
              <div className='rounded-lg border border-blue-200 bg-blue-50 p-3'>
                <div className='flex items-center justify-between'>
                  <div>
                    <div className='text-gray-600'>Cấu hình giá theo khung thời gian</div>
                    <div className='text-sm text-gray-800'>
                      Giá theo nguồn{' '}
                      <span className='font-bold'>
                        {sourcesData.find(s => s.id === form.watch('orderSource'))?.sourceName ||
                          form.watch('orderSource')}
                      </span>{' '}
                      sẽ được áp dụng theo số tiền <span className='font-bold'>{formatNumberDisplay(form.watch('amount') || '')} ₫</span>. Khi cấu
                      hình giá theo khung thời gian số tiền sẽ hiển thị theo các khung thời gian cấu hình được dưới đây
                    </div>
                  </div>
                  <Button
                    type='button'
                    variant='default'
                    size='sm'
                    onClick={() => {
                      const sourceName =
                        sourcesData.find(s => s.id === form.watch('orderSource'))?.sourceName ||
                        form.watch('orderSource')
                      onOpenTimeFrameConfig?.(sourceName, handleTimeFrameConfigSaved)
                    }}
                  >
                    Thêm cấu hình
                  </Button>
                </div>
              </div>
            )}

            {timeFrameConfigs.length > 0 && (
              <div className='space-y-3'>
                {timeFrameConfigs.map((config) => (
                  <div
                    key={config.id}
                    className='rounded-lg border border-gray-200 bg-gray-50 p-3 cursor-pointer hover:bg-gray-100'
                    onClick={() => {
                      handleEditTimeFrame(config)
                    }}
                  >
                    <div className='flex items-center justify-between'>
                      <div className='flex-1'>
                        <div className='text-sm text-gray-600'>
                          ● Từ ngày {formatDate(config.startDate)} đến ngày {formatDate(config.endDate)} Giá: {formatNumberDisplay(config.amount)} ₫
                        </div>
                        <div className='text-xs text-gray-500 mt-1'>
                          Khung giờ {formatSelectedHours(convertSelectedHoursToNumbers(config.selectedHours))} {formatSelectedDays(convertSelectedDaysToNumbers(config.selectedDays))}
                        </div>
                      </div>
                      <Button
                        type='button'
                        variant='ghost'
                        size='sm'
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteTimeFrame(config.id)
                        }}
                        className='text-gray-400 hover:text-red-500'
                      >
                        ⊗
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <DialogFooter>
              <Button type='submit' className='w-full'>
                Xác nhận
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có muốn bỏ cấu hình này không? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteTimeFrame} className='bg-red-600 hover:bg-red-700'>
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Dialog>
  )
}
