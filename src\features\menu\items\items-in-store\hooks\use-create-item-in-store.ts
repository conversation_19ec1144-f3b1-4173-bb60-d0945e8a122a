import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { api } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface CreateItemInStoreRequest {
  city_uid?: string
  ta_price: number
  ots_tax: number
  ta_tax: number
  store_uid: string
  unit_uid: string
  extra_data: {
    price_by_source: unknown[]
    is_virtual_item: number
    is_item_service: number
    no_update_quantity_toping: number
    enable_edit_price: number
    is_buffet_item: number
    exclude_items_buffet: unknown[]
    up_size_buffet: unknown[]
  }
  item_type_uid: string
  item_name: string
  company_uid: string
  brand_uid: string
  ots_price: number
  sort: number
  item_id: string
  apply_with_store: number
}

interface BulkCreateItemInStoreRequest {
  store_uid: string
  apply_with_store: number
  company_uid: string
  brand_uid: string
  city_uid?: string
  item_id: string
  unit_uid: string
  ots_price: number
  ta_price: number
  ots_tax: number
  ta_tax: number
  item_name: string
  item_id_barcode?: string
  is_eat_with: number
  item_type_uid: string
  item_class_uid: string
  description?: string
  item_id_mapping?: string
  time_cooking: number
  time_sale_date_week: number
  time_sale_hour_day: number
  sort: number
  image_path_thumb?: string
  image_path?: string
  extra_data: {
    no_update_quantity_toping: number
    enable_edit_price: number
    is_virtual_item: number
    is_item_service: number
    is_buffet_item: number
  }
}

export const useCreateItemInStore = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: CreateItemInStoreRequest) => {
      // Use the exact payload structure from the component
      const payload = data

      const response = await api.post('/mdata/v1/item', payload)

      return response.data.data || response.data
    },
    onSuccess: () => {
      // Just invalidate without refetching to preserve pagination
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST],
        refetchType: 'none'
      })

      // Manually refetch in background to update data
      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
        })
      }, 100)

      toast.success('Tạo món ăn thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo món ăn: ${error.message}`)
    }
  })

  return { createItemInStore: mutate, isCreating: isPending }
}

export const useBulkCreateItemsInStore = () => {
  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: BulkCreateItemInStoreRequest[]) => {
      const response = await api.post('/mdata/v1/items', data)
      return response.data.data || response.data
    },
    onSuccess: () => {
      // Just invalidate without refetching to preserve pagination
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST],
        refetchType: 'none'
      })

      // Manually refetch in background to update data
      setTimeout(() => {
        queryClient.refetchQueries({
          queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
        })
      }, 100)

      toast.success('Tạo món ăn thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo món ăn: ${error.message}`)
    }
  })

  return { bulkCreateItemsInStore: mutate, isBulkCreating: isPending }
}

export type { CreateItemInStoreRequest, BulkCreateItemInStoreRequest }
