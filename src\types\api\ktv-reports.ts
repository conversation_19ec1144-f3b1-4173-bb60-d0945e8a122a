// KTV Reports Types
export interface MaterialCostDetail {
  item_id: string
  item_name: string
  cost_item: number
  quantity: number
  price_cost: number
  price_cost_declaration: number
  price: number
  ivt: boolean
  sale_fabi: number
}

export interface PurchaseDetail {
  // Add fields as needed when we have sample data
}

export interface ToolCostDetail {
  // Add fields as needed when we have sample data
}

export interface CostInexDetail {
  // Add fields as needed when we have sample data
}

export interface IncomeDetail {
  // Add fields as needed when we have sample data
}

export interface KtvMonthReportData {
  ts_time: number
  store_uid: string
  revenue_net: number
  material_cost: number
  material_cost_detail: MaterialCostDetail[]
  purchase_detail: PurchaseDetail[]
  count_item_not_price: number
  count_item_price: number
  count_item: number
  tool_cost: number
  tool_cost_detail: ToolCostDetail[]
  count_tool: number
  cost_amount_inex: number
  cost_inex_detail: CostInexDetail[]
  count_cost_out: number
  count_cost_out_all: number
  income_amount: number
  income_detail: IncomeDetail[]
  profit: number
  date_time_filter: string | null
  require_close_report: number
  ivt_price: number
  status_report: string
  percentage_material: number
  percentage_tool: number
  percentage_inex: number
  percentage_profit: number
}

export interface KtvMonthReportParams {
  store_uid: string
  date_time: number
}

export interface KtvMonthReportResponse {
  data: KtvMonthReportData
  track_id: string
}
