import { X } from 'lucide-react'
import { useParams, useSearch } from '@tanstack/react-router'

import {
  Label,
  Button,
  Input,
  Checkbox,
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent
} from '@/components/ui'

import { CmsTabContent, PosPdaTabContent, ManagerTabContent } from './components'
import { useRoleForm, usePermissions, useTabManagement, useRoleSave } from './hooks'

interface EmployeeRoleDetailPageProps {
  roleId?: string
}

export default function EmployeeRoleDetailPage({ roleId: propRoleId }: EmployeeRoleDetailPageProps = {}) {
  let roleId = propRoleId
  let copyFromRoleId: string | undefined

  try {
    const params = useParams({ strict: false })
    if (!propRoleId && params && 'roleId' in params) {
      roleId = params.roleId as string
    }
  } catch {
    try {
      const currentPath = window.location.pathname
      if (currentPath.includes('/employee/role/detail/') && !propRoleId) {
        const pathParts = currentPath.split('/')
        const roleIdIndex = pathParts.findIndex(part => part === 'detail') + 1
        if (roleIdIndex > 0 && pathParts[roleIdIndex]) {
          roleId = pathParts[roleIdIndex]
        }
      }
    } catch {
    }
  }

  // Get search params for copy functionality
  try {
    const search = useSearch({ strict: false })
    if (search && 'copyFromRoleId' in search) {
      copyFromRoleId = search.copyFromRoleId as string
    }
  } catch {
    // Ignore search params errors
  }

  const isEditMode = !!roleId
  const isCopyMode = !!copyFromRoleId

  const {
    roleName,
    setRoleName,
    description,
    setDescription,
    isFormValid,
    roleData,
    copyFromRoleData,
    isLoading: isLoadingRole
  } = useRoleForm({ roleId, copyFromRoleId })

  const {
    permissions,
    handlePermissionChange,
    handleParentPermissionChange,
    handleChildPermissionChange
  } = usePermissions({
    roleData: isEditMode ? roleData : (isCopyMode ? copyFromRoleData : undefined),
    isEditMode: isEditMode || isCopyMode,
    copyFromRoleId
  })

  const {
    activeTab,
    setActiveTab,
    enabledTabs,
    hasAnyEnabledTabs,
    handlePermissionChangeWithTabSwitch
  } = useTabManagement({ permissions, onPermissionChange: handlePermissionChange })

  const { handleBack, handleSave, isLoading: isSaving } = useRoleSave({
    roleName,
    description,
    permissions,
    isFormValid,
    isEditMode,
    roleData
  })

  const permissionOptions = [
    { value: 'POS_CMS', label: 'POS_CMS' },
    { value: 'POS_CLIENT', label: 'POS_CLIENT' },
    { value: 'POS_MANAGER', label: 'POS_MANAGER' }
  ]

  if (isEditMode && isLoadingRole) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='py-8 text-center'>
          <p>Đang tải thông tin chức vụ...</p>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='button'
            disabled={isSaving || !isFormValid}
            className='min-w-[100px]'
            onClick={handleSave}
          >
            {isSaving ? (isEditMode ? 'Đang cập nhật...' : 'Đang tạo...') : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>
            {isEditMode ? 'Chỉnh sửa chức vụ' : 'Tạo chức vụ mới'}
          </h1>
        </div>
      </div>

      <div className='mx-auto max-w-4xl'>
        <div className='p-6'>
          <div className='space-y-6'>
            <div className='flex items-center gap-4'>
              <Label htmlFor='role-name' className='min-w-[200px] text-sm font-medium'>
                Tên chức vụ <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='role-name'
                value={roleName}
                onChange={e => setRoleName(e.target.value)}
                placeholder='Thêm chức vụ'
                className='flex-1'
              />
            </div>

            <div className='flex items-center gap-4'>
              <Label htmlFor='description' className='min-w-[200px] text-sm font-medium'>
                Mô tả <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='description'
                value={description}
                onChange={e => setDescription(e.target.value)}
                placeholder='Thêm mô tả'
                className='flex-1'
              />
            </div>

            <div className='flex items-center gap-4'>
              <Label className='min-w-[200px] text-sm font-medium'>Quyền truy cập</Label>
              <div className='flex flex-1 items-center gap-6'>
                {permissionOptions.map(option => (
                  <div key={option.value} className='flex items-center space-x-2'>
                    <Checkbox
                      id={option.value}
                      checked={permissions.includes(option.value)}
                      onCheckedChange={checked =>
                        handlePermissionChangeWithTabSwitch(option.value, checked as boolean)
                      }
                    />
                    <Label htmlFor={option.value} className='cursor-pointer text-sm font-normal'>
                      {option.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className='p-6'>
          <h2 className='mb-6 text-xl font-semibold'>Cấp quyền</h2>

          {hasAnyEnabledTabs && (
            <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
              <TabsList className='grid w-full grid-cols-3'>
                <TabsTrigger
                  value='cms'
                  disabled={!enabledTabs.includes('cms')}
                  className={!enabledTabs.includes('cms') ? 'cursor-not-allowed opacity-50' : ''}
                >
                  CMS
                </TabsTrigger>
                <TabsTrigger
                  value='pos-pda'
                  disabled={!enabledTabs.includes('pos-pda')}
                  className={
                    !enabledTabs.includes('pos-pda') ? 'cursor-not-allowed opacity-50' : ''
                  }
                >
                  POS/PDA
                </TabsTrigger>
                <TabsTrigger
                  value='manager'
                  disabled={!enabledTabs.includes('manager')}
                  className={
                    !enabledTabs.includes('manager') ? 'cursor-not-allowed opacity-50' : ''
                  }
                >
                  MANAGER
                </TabsTrigger>
              </TabsList>

              <TabsContent value='cms' className='mt-6'>
                <CmsTabContent
                  permissions={permissions}
                  onParentPermissionChange={handleParentPermissionChange}
                  onChildPermissionChange={handleChildPermissionChange}
                />
              </TabsContent>

              <TabsContent value='pos-pda' className='mt-6'>
                <PosPdaTabContent
                  permissions={permissions}
                  onParentPermissionChange={handleParentPermissionChange}
                  onChildPermissionChange={handleChildPermissionChange}
                />
              </TabsContent>

              <TabsContent value='manager' className='mt-6'>
                <ManagerTabContent
                  permissions={permissions}
                  onParentPermissionChange={handleParentPermissionChange}
                  onChildPermissionChange={handleChildPermissionChange}
                />
              </TabsContent>
            </Tabs>
          )}
        </div>
      </div>
    </div>
  )
}
