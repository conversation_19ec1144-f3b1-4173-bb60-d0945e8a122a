import { useState, useMemo, useCallback, useEffect } from 'react'

import { format } from 'date-fns'

import { CRM_PARAMS } from '@/constants'

import { systemLogApi, formatStartDateForApi, formatEndDateForApi } from '@/lib/api/crm/system-log-api'

import { createDateFilter, createTextFilter } from '@/utils/date-filters'

import { DEFAULT_DATE_RANGE } from '@/constants/crm/time'

import type { SystemLogFilters, SystemLog } from '../data'
import { ALL_MOCK_LOGS } from '../data'

export function useSystemLog() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [apiLogs, setApiLogs] = useState<SystemLog[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [filters, setFilters] = useState<SystemLogFilters>({
    dateRange: {
      from: DEFAULT_DATE_RANGE.FROM,
      to: DEFAULT_DATE_RANGE.TO
    },
    request_path: 'all',
    user_name: ''
  })

  // Filter logs based on current filters
  const filteredLogs = useMemo(() => {
    const sourceData = apiLogs.length > 0 ? apiLogs : ALL_MOCK_LOGS
    let filtered = [...sourceData]

    // Apply date range filter
    if (filters.dateRange.from && filters.dateRange.to) {
      const dateFilter = createDateFilter<SystemLog>(filters.dateRange.from, filters.dateRange.to)
      filtered = filtered.filter(dateFilter)
    }

    // Apply request path filter
    const pathFilter = createTextFilter<SystemLog>('request_path')
    filtered = filtered.filter(pathFilter(filters.request_path))

    // Apply user name filter
    const userFilter = createTextFilter<SystemLog>('user_name')
    filtered = filtered.filter(userFilter(filters.user_name))

    // Sort by request_at descending (newest first)
    return filtered.sort((a, b) => new Date(b.request_at).getTime() - new Date(a.request_at).getTime())
  }, [filters, apiLogs])

  const updateFilter = (key: keyof SystemLogFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const updateDateRange = (from: Date | null, to: Date | null) => {
    setFilters(prev => ({
      ...prev,
      dateRange: { from, to }
    }))
  }

  const fetchSystemLogs = useCallback(async () => {
    if (!filters.dateRange.from || !filters.dateRange.to) {
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await systemLogApi.getSystemLogs({
        start_time: formatStartDateForApi(filters.dateRange.from),
        end_time: formatEndDateForApi(filters.dateRange.to),
        pos_parent: CRM_PARAMS.POS_PARENT,
        limit: 1000
      })

      if (response.data && response.data.data) {
        setApiLogs(response.data.data)
        setTotalCount(response.data.count || response.data.data.length)
      } else {
        setApiLogs([])
        setTotalCount(0)
      }
    } catch (err) {
      console.error('Error fetching system logs:', err)
      setError('Có lỗi xảy ra khi tải nhật ký hệ thống')
      // Fallback to mock data
      setApiLogs(ALL_MOCK_LOGS)
      setTotalCount(ALL_MOCK_LOGS.length)
    } finally {
      setIsLoading(false)
    }
  }, [filters.dateRange.from, filters.dateRange.to])

  const searchLogs = useCallback(async () => {
    await fetchSystemLogs()
  }, [fetchSystemLogs])

  // Fetch data on component mount and when date range changes
  useEffect(() => {
    fetchSystemLogs()
  }, [fetchSystemLogs])

  // Get unique request paths for filter dropdown
  const availableRequestPaths = useMemo(() => {
    const dataSource = apiLogs.length > 0 ? apiLogs : ALL_MOCK_LOGS
    const paths = Array.from(new Set(dataSource.map(log => log.request_path)))
    return paths.map(path => ({ value: path, label: path }))
  }, [apiLogs])

  // Get unique users for filter dropdown
  const availableUsers = useMemo(() => {
    const dataSource = apiLogs.length > 0 ? apiLogs : ALL_MOCK_LOGS
    const users = Array.from(new Set(dataSource.map(log => log.user_name)))
    return users.map(user => ({ value: user, label: user }))
  }, [apiLogs])

  const formatTimestamp = useCallback((timestamp: string) => {
    try {
      const date = new Date(timestamp)
      return format(date, 'dd/MM/yyyy HH:mm:ss')
    } catch {
      return timestamp
    }
  }, [])

  return {
    logs: filteredLogs,
    isLoading,
    error,
    filters,
    updateFilter,
    updateDateRange,
    searchLogs,
    availableRequestPaths,
    availableUsers,
    formatTimestamp,
    totalCount: totalCount || filteredLogs.length
  }
}
