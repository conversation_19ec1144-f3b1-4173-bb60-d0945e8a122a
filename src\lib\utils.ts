/* eslint-disable @typescript-eslint/no-explicit-any */
import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

/**
 * Get user email from localStorage
 * @returns User email or fallback value
 */
export const getUserEmail = (): string => {
  try {
    const userData = localStorage.getItem('pos_user_data')
    if (userData) {
      const user = JSON.parse(userData)
      return user.email || '<EMAIL>'
    }
  } catch (_error) {
    // Error parsing user data, fallback to default
  }
  return '<EMAIL>'
}

/**
 * Get display text for selected items
 * @param selectedItems Array of selected items
 * @returns Display text showing count or placeholder
 */
export const getSelectedItemsDisplay = (selectedItems: any[]) => {
  if (selectedItems.length === 0) {
    return 'Chọn món áp dụng'
  }

  return `${selectedItems.length} món`
}

/**
 * Get display text for selected printers
 * @param selectedPrinters Array of selected printers
 * @returns Display text showing count or placeholder
 */
export const getSelectedPrintersDisplay = (selectedPrinters: any[]) => {
  if (selectedPrinters.length === 0) {
    return 'Chọn vị trí máy in'
  }

  return `${selectedPrinters.length} vị trí máy in`
}

/**
 * Format number for display with thousand separators
 * @param value - String or number to format
 * @returns Formatted string with dots as thousand separators
 */
export function formatNumberDisplay(value: string | number): string {
  if (!value || value === '0') return ''
  const stringValue = typeof value === 'number' ? value.toString() : value

  // Validate input contains only digits
  if (!/^\d+$/.test(stringValue)) return stringValue

  return stringValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * Parse input to extract only digits
 * @param input - Raw input string
 * @returns Clean string with only digits
 */
export function parseNumberInput(input: string): string {
  return input.replace(/[^\d]/g, '')
}

/**
 * Handle number input change for forms
 * @param input - Raw input value
 * @param onChange - Form field onChange function
 */
export function handleNumberInputChange(
  input: string,
  onChange: (value: string) => void
): void {
  const cleanValue = parseNumberInput(input)
  onChange(cleanValue)
}