import type { User } from '@/types'

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button
} from '@/components/ui'

import { useCreateEmployeeForm } from '../../hooks'
import { TableSelector, StoreSelector, BrandCityStoreSelector } from '../selectors'
import { BrandAccessSection } from './brand-access-section'
import { PasswordSection } from './password-section'
import { TableSelectionSection } from './table-selection-section'

type CreateEmployeeSubmitData = {
  email: string
  full_name: string
  phone?: string
  role_uid: string
  password?: string
  brand_access?: string[]
}

interface CreateEmployeeFormProps {
  onSubmit: (data: CreateEmployeeSubmitData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
  showSaveButton?: boolean
  initialData?: User
  isEditMode?: boolean
}

export function CreateEmployeeForm({
  onSubmit,
  onCancel: _onCancel,
  isLoading = false,
  showSaveButton = true,
  initialData,
  isEditMode = false
}: CreateEmployeeFormProps) {
  const {
    // Form
    form,
    handleSubmit,

    // Data
    roles,
    rolesLoading,

    // Computed values
    selectedRole,
    isOwner,
    needsStore,
    needsTables,

    // Selection states
    selectedBrandAccess,
    selectedStore,
    selectedTables,

    // Modal states
    showBrandSelector,
    setShowBrandSelector,
    showStoreSelector,
    setShowStoreSelector,
    showTableSelector,
    setShowTableSelector,

    // Handlers
    handleBrandAccessSave,
    handleStoreSave,
    handleTablesSave,

    // Utilities
    getBrandIdFromStore
  } = useCreateEmployeeForm({
    initialData,
    isEditMode,
    onSubmit
  })

  return (
    <>
      <div className='mx-auto max-w-2xl'>
        {showSaveButton && (
          <div className='mb-4 flex justify-end'>
            <Button type='submit' form='employee-form' disabled={isLoading}>
              {isLoading ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        )}
        <Form {...form}>
          <form id='employee-form' onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
            <div className='space-y-6'>
              <div>
                <h3 className='mb-1 text-base font-medium'>Tài khoản</h3>
                <p className='text-muted-foreground text-sm'>
                  Hãy sử dụng email khả dụng đối với tài khoản cho Owner, Quản lý chuỗi, IT Support, Marketing, Kế toán
                </p>
              </div>

              <FormField
                control={form.control}
                name='email'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-4 items-center gap-4'>
                    <FormLabel className='text-right text-sm'>Email *</FormLabel>
                    <div className='col-span-3'>
                      <FormControl>
                        <Input placeholder='Nhập email nhân viên' disabled={isEditMode} {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <div className='space-y-2'>
              <h3 className='text-base font-medium'>Chi tiết</h3>

              <FormField
                control={form.control}
                name='full_name'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-4 items-center gap-4'>
                    <FormLabel className='text-right text-sm'>Tên nhân viên *</FormLabel>
                    <div className='col-span-3'>
                      <FormControl>
                        <Input placeholder='Nhập tên nhân viên' {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='phone'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-4 items-center gap-4'>
                    <FormLabel className='text-right text-sm'>Số điện thoại</FormLabel>
                    <div className='col-span-3'>
                      <FormControl>
                        <Input placeholder='Nhập số điện thoại' {...field} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <div className='space-y-6'>
              <h3 className='text-base font-medium'>Vị trí - Chức vụ</h3>

              <FormField
                control={form.control}
                name='role_uid'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-4 items-center gap-4'>
                    <FormLabel className='text-right text-sm'>Chức vụ *</FormLabel>
                    <div className='col-span-3'>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder='Chọn chức vụ' />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {rolesLoading ? (
                            <SelectItem disabled value='loading'>
                              Đang tải...
                            </SelectItem>
                          ) : (
                            roles.map(role => (
                              <SelectItem key={role.id} value={role.id}>
                                {role.role_name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {!isOwner && selectedRole && (
              <BrandAccessSection
                needsStoreSelection={needsStore}
                selectedStore={selectedStore}
                selectedBrandAccess={selectedBrandAccess}
                onShowStoreSelector={() => setShowStoreSelector(true)}
                onShowBrandSelector={() => setShowBrandSelector(true)}
                brands={initialData?.brands}
                cities={initialData?.cities}
                stores={initialData?.storeDetails}
              />
            )}

            {needsTables && selectedStore && (
              <TableSelectionSection
                selectedTables={selectedTables}
                selectedStore={selectedStore}
                brandId={getBrandIdFromStore(selectedStore)}
                onShowTableSelector={() => setShowTableSelector(true)}
              />
            )}

            <PasswordSection control={form.control} isEditMode={isEditMode} userId={initialData?.id} />
          </form>
        </Form>
      </div>

      <BrandCityStoreSelector
        open={showBrandSelector}
        onOpenChange={setShowBrandSelector}
        selectedItems={selectedBrandAccess}
        onSave={handleBrandAccessSave}
      />

      <StoreSelector
        open={showStoreSelector}
        onOpenChange={setShowStoreSelector}
        selectedStore={selectedStore}
        onSave={handleStoreSave}
      />

      {needsTables && selectedStore && (
        <TableSelector
          open={showTableSelector}
          onOpenChange={setShowTableSelector}
          selectedTables={selectedTables}
          onSave={handleTablesSave}
          storeId={selectedStore.replace('store:', '')}
          brandId={getBrandIdFromStore(selectedStore)}
        />
      )}
    </>
  )
}
