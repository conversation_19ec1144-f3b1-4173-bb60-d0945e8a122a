// Use global User type from @/types
export type { User } from '@/types'

export interface LocalUser {
  id: string
  username: string
  email: string
  status: 'active' | 'inactive'
  permissions: UserPermission[]
  createdAt: string
  updatedAt: string
}

export interface UserPermission {
  id: string
  name: string
  category: string
  description?: string
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  permissions: string[]
}

export interface UpdateUserRequest {
  id: string
  username?: string
  email?: string
  password?: string
  permissions?: string[]
  status?: 'active' | 'inactive'
}

export interface PermissionCategory {
  name: string
  permissions: UserPermission[]
}

// Mock data for permissions
export const PERMISSION_CATEGORIES: PermissionCategory[] = [
  {
    name: 'Báo cáo',
    permissions: [
      { id: 'all', name: 'Tất cả', category: 'Báo cáo' },
      { id: 'revenue_member', name: '<PERSON>anh thu thành viên', category: 'Báo cáo' },
      { id: 'customer_report', name: '<PERSON><PERSON><PERSON> cáo khách hàng', category: 'Báo cáo' },
      { id: 'customer_group_report', name: '<PERSON><PERSON> sánh nhóm khách', category: 'Báo cáo' },
      { id: 'customer_behavior_report', name: 'Biến động khách hàng', category: 'Báo cáo' },
      { id: 'customer_feedback_report', name: 'Phản hồi khách hàng', category: 'Báo cáo' },
      { id: 'voucher_report', name: 'Báo cáo voucher', category: 'Báo cáo' },
      { id: 'invoice_report', name: 'Bảng kê hóa đơn', category: 'Báo cáo' }
    ]
  },
  {
    name: 'Danh sách khách hàng',
    permissions: [
      { id: 'customer_access', name: 'Truy cập', category: 'Danh sách khách hàng' },
      { id: 'customer_filter', name: 'Lọc khách hàng', category: 'Danh sách khách hàng' },
      { id: 'customer_archive', name: 'Lưu bộ lọc', category: 'Danh sách khách hàng' },
      { id: 'customer_tag', name: 'Thêm tag', category: 'Danh sách khách hàng' },
      { id: 'customer_export', name: 'Xuất danh sách', category: 'Danh sách khách hàng' },
      { id: 'customer_import', name: 'Nhập dữ liệu khách hàng', category: 'Danh sách khách hàng' },
      { id: 'customer_attach_filter', name: 'Gắn tag theo bộ lọc', category: 'Danh sách khách hàng' },
      { id: 'customer_add_new', name: 'Thêm khách hàng mới', category: 'Danh sách khách hàng' }
    ]
  },
  {
    name: 'Chi tiết khách hàng',
    permissions: [
      { id: 'customer_detail_access', name: 'Truy cập', category: 'Chi tiết khách hàng' },
      {
        id: 'customer_info_edit',
        name: 'Sửa thông tin khách hàng (Tên, ngày sinh,...)',
        category: 'Chi tiết khách hàng'
      },
      { id: 'customer_pos_sync', name: 'Đồng bộ điểm từ POS', category: 'Chi tiết khách hàng' },
      { id: 'customer_edit', name: 'Sửa hạng khách hàng', category: 'Chi tiết khách hàng' }
    ]
  },
  {
    name: 'Lịch sử hạng thành viên',
    permissions: [{ id: 'member_history_access', name: 'Truy cập', category: 'Lịch sử hạng thành viên' }]
  },
  {
    name: 'Công cụ phân tích',
    permissions: [
      { id: 'analytics_access', name: 'Truy cập', category: 'Công cụ phân tích' },
      { id: 'analytics_tag', name: 'Thêm tag', category: 'Công cụ phân tích' }
    ]
  },
  {
    name: 'Quản lý phản hồi khách hàng',
    permissions: [
      { id: 'feedback_access', name: 'Truy cập', category: 'Quản lý phản hồi khách hàng' },
      { id: 'feedback_process', name: 'Xử lý', category: 'Quản lý phản hồi khách hàng' },
      { id: 'feedback_export', name: 'Xuất danh sách', category: 'Quản lý phản hồi khách hàng' }
    ]
  },
  {
    name: 'Campaign trên Momo',
    permissions: [{ id: 'momo_campaign_access', name: 'Truy cập', category: 'Campaign trên Momo' }]
  },
  {
    name: 'Quản lý e-voucher',
    permissions: [
      { id: 'evoucher_access', name: 'Truy cập', category: 'Quản lý e-voucher' },
      { id: 'evoucher_search', name: 'Tìm kiếm mã voucher', category: 'Quản lý e-voucher' },
      { id: 'evoucher_create', name: 'Tạo chương trình', category: 'Quản lý e-voucher' },
      { id: 'evoucher_view', name: 'Xem kết quả', category: 'Quản lý e-voucher' },
      { id: 'evoucher_list', name: 'Xem danh sách mã voucher', category: 'Quản lý e-voucher' },
      { id: 'evoucher_edit', name: 'Chỉnh sửa chương trình', category: 'Quản lý e-voucher' },
      { id: 'evoucher_copy', name: 'Sao chép chương trình', category: 'Quản lý e-voucher' },
      { id: 'evoucher_deactivate', name: 'Hủy, kích hoạt, gia hạn chương trình', category: 'Quản lý e-voucher' },
      { id: 'evoucher_export', name: 'Xuất mã voucher', category: 'Quản lý e-voucher' },
      { id: 'evoucher_send', name: 'Gửi mã voucher', category: 'Quản lý e-voucher' },
      { id: 'evoucher_priority', name: 'Ưu đãi VinID', category: 'Quản lý e-voucher' },
      { id: 'evoucher_cancel', name: 'Hủy voucher', category: 'Quản lý e-voucher' }
    ]
  },
  {
    name: 'Quản lý broadcast',
    permissions: [
      { id: 'broadcast_access', name: 'Truy cập', category: 'Quản lý broadcast' },
      { id: 'broadcast_create', name: 'Tạo broadcast', category: 'Quản lý broadcast' }
    ]
  },
  {
    name: 'Trang đăng ký thành viên',
    permissions: [
      { id: 'member_registration_access', name: 'Trang đăng ký thành viên', category: 'Trang đăng ký thành viên' }
    ]
  },
  {
    name: 'Loyalty',
    permissions: [{ id: 'loyalty_member', name: 'Hạng thành viên', category: 'Loyalty' }]
  }
]
