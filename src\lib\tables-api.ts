import { apiClient } from './api/pos/pos-api'

/**
 * Generate a random table ID in the format AREA-XXXX
 */
const generateTableId = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = 'AREA-'
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export interface Table {
  id: string
  table_id: string
  table_name: string
  description?: string
  extra_data?: {
    order_list?: any[]
    color?: string
    font_size?: string
  }
  active: number
  revision?: number
  sort: number
  is_fabi: number
  source_id?: string
  area_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
  area_id: string
  store_id?: string
  brand_id?: string
  company_id?: string
  created_by: string
  updated_by?: string
  created_at: string
  updated_at: string
  area: {
    id: string
    area_id: string
    area_name: string
    active: number
    sort: number
    extra_data?: any
  }
}

export interface TablesListParams {
  company_uid: string
  brand_uid: string
  store_uid: string
  page?: number
  limit?: number
  skip_limit?: boolean
}

export interface TablesListResponse {
  data: Table[]
  track_id: string
}

export interface Area {
  id: string
  area_id: string
  area_name: string
  description?: string
  extra_data?: any
  active: number
  revision: number
  sort: number
  store_uid: string
  brand_uid: string
  company_uid: string
  store_id?: string
  brand_id?: string
  company_id?: string
  is_fabi: number
  created_by: string
  updated_by?: string
  created_at: string
  updated_at: string
  list_table_id: string[]
}

export interface AreasListParams {
  company_uid: string
  brand_uid: string
  store_uid: string
  skip_limit?: boolean
  page?: number
  results_per_page?: number
}

export interface AreasListResponse {
  data: Area[]
  track_id: string
}

export interface DeleteTableRequest {
  company_uid: string
  brand_uid: string
  store_uid: string
  list_id: string[]
}

export interface UpdateTableStatusRequest {
  id: string
  table_id: string
  table_name: string
  description: string | null
  extra_data: {
    color: string
    font_size: string
    order_list: Array<{
      item_id: string
      quantity: number
    }>
  }
  active: number
  revision: string | null
  sort: number
  is_fabi: number
  source_id: string | null
  area_uid: string
  store_uid: string
  brand_uid: string
  company_uid: string
  area_id: string
  store_id: string | null
  brand_id: string | null
  company_id: string | null
  created_by: string
  updated_by: string
  created_at: string
  updated_at: string
  area: {
    id: string
    area_id: string
    area_name: string
    active: number
    sort: number
    extra_data: {
      background: string
    }
  }
}

export interface CreateTableRequest {
  store_uid: string
  area_uid: string
  extra_data: {
    order_list: Array<{
      item_id: string
      quantity: number
    }>
    color: string
    font_size: string
  }
  table_name: string
  source_id: string
  description?: string
  sort: number
  company_uid: string
  brand_uid: string
  table_id: string
  area_id: string
}

export interface UpdateTableRequest extends CreateTableRequest {
  id: string
}

export interface DeleteTablesRequest {
  company_uid: string
  brand_uid: string
  list_id: string[]
  store_uid: string
  area_uid?: string
}

export interface BulkImportTableItem {
  company_uid: string
  brand_uid: string
  area_name: string
  area_id: string
  description?: string
  store_uid: string
  sort: number
}

export type BulkImportTablesRequest = BulkImportTableItem[]

export interface UpdateTablePositionItem {
  company_uid: string
  brand_uid: string
  store_uid: string
  id: string
  sort: number
}

export type UpdateTablePositionsRequest = UpdateTablePositionItem[]

export interface BulkCreateTableItem {
  table_name: string
  area_uid: string
  source_id?: string
  description?: string
  extra_data?: {
    order_list?: Array<{
      item_id: string
      quantity: number
    }>
    color?: string
    font_size?: string
  }
}

export interface BulkCreateTablesRequest {
  store_uid: string
  company_uid: string
  brand_uid: string
  tables: BulkCreateTableItem[]
}

/**
 * Tables API client
 */
export const tablesApi = {
  /**
   * Get tables list
   */
  getTablesList: async (params: TablesListParams): Promise<TablesListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      store_uid: params.store_uid,
      ...(params.skip_limit ? { skip_limit: 'true' } : { page: (params.page || 1).toString() }),
      ...(params.limit && !params.skip_limit && { limit: params.limit.toString() })
    })

    const url = `/pos/v1/table?${queryParams.toString()}`

    const response = await apiClient.get(url)
    return response.data
  },

  /**
   * Update an existing table
   */
  updateTable: async (data: Table): Promise<Table> => {
    const updateData = {
      id: data.id,
      table_name: data.table_name,
      store_uid: data.store_uid,
      company_uid: data.company_uid,
      brand_uid: data.brand_uid,
      source_id: data.source_id,
      area_uid: data.area_uid,
      sort: data.sort,
      extra_data: {
        order_list: data.extra_data?.order_list || [],
        color: data.extra_data?.color || '',
        font_size: data.extra_data?.font_size || '15'
      },
      description: data.description
    }

    const response = await apiClient.post('/pos/v1/table', [updateData], {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      timeout: 30000
    })

    return response.data.data || response.data
  },

  /**
   * Get table by ID
   */
  getTableById: async (id: string, companyUid: string, brandUid: string, storeUid: string): Promise<Table> => {
    const queryParams = new URLSearchParams({
      company_uid: companyUid,
      brand_uid: brandUid,
      store_uid: storeUid,
      id: id
    })

    const response = await apiClient.get(`/pos/v1/table?${queryParams.toString()}`)
    return response.data.data || response.data
  },

  /**
   * Delete multiple tables
   */
  deleteTables: async (params: DeleteTablesRequest): Promise<void> => {
    const response = await apiClient.delete('/pos/v1/table', {
      data: params
    })
    return response.data
  },

  /**
   * Create a new table
   */
  createTable: async (data: CreateTableRequest): Promise<Table> => {
    const response = await apiClient.post('/pos/v1/table', data)
    return response.data.data || response.data
  },

  /**
   * Bulk import tables
   */
  bulkImportTables: async (tables: BulkImportTablesRequest): Promise<Table[]> => {
    const response = await apiClient.post('/pos/v1/area', tables)
    return response.data.data || response.data
  },

  /**
   * Bulk create tables
   */
  bulkCreateTables: async (data: BulkCreateTablesRequest): Promise<Table[]> => {
    const results: Table[] = []

    // Create tables one by one since there's no bulk endpoint
    for (const tableData of data.tables) {
      const tableId = generateTableId()
      const areaId = generateTableId()

      const createTableRequest: CreateTableRequest = {
        store_uid: data.store_uid,
        area_uid: tableData.area_uid,
        extra_data: {
          order_list: tableData.extra_data?.order_list || [],
          color: tableData.extra_data?.color || '',
          font_size: tableData.extra_data?.font_size || '15'
        },
        table_name: tableData.table_name,
        source_id: tableData.source_id || '',
        description: tableData.description || '',
        sort: results.length + 1,
        company_uid: data.company_uid,
        brand_uid: data.brand_uid,
        table_id: tableId,
        area_id: areaId
      }

      const response = await apiClient.post('/pos/v1/table', createTableRequest)
      const result = response.data.data || response.data
      results.push(result)
    }

    return results
  },

  /**
   * Bulk update tables - send all tables in one request
   */
  bulkUpdateTables: async (tables: Table[]): Promise<Table[]> => {
    const updateData = tables.map(table => ({
      id: table.id,
      table_name: table.table_name,
      store_uid: table.store_uid,
      company_uid: table.company_uid,
      brand_uid: table.brand_uid,
      source_id: table.source_id || '',
      area_uid: table.area_uid,
      sort: table.sort,
      extra_data: {
        order_list: table.extra_data?.order_list || [],
        color: table.extra_data?.color || '',
        font_size: table.extra_data?.font_size || ''
      },
      description: table.description || ''
    }))

    const response = await apiClient.post('/pos/v1/table', updateData, {
      headers: {
        Accept: 'application/json, text/plain, */*',
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      },
      timeout: 30000
    })

    return response.data.data || response.data
  },

  /**
   * Get areas list for filtering
   */
  getAreasList: async (params: AreasListParams): Promise<AreasListResponse> => {
    const queryParams = new URLSearchParams({
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      store_uid: params.store_uid,
      skip_limit: 'true',
      page: (params.page || 1).toString(),
      results_per_page: (params.results_per_page || 15000).toString()
    })

    const response = await apiClient.get(`/pos/v1/area?${queryParams.toString()}`)
    return response.data
  },

  /**
   * Delete table
   */
  deleteTable: async (params: DeleteTableRequest): Promise<void> => {
    const response = await apiClient.delete('/pos/v1/table', {
      data: params
    })
    return response.data
  },

  /**
   * Update table status (activate/deactivate)
   */
  updateTableStatus: async (tableData: UpdateTableStatusRequest): Promise<Table> => {
    // Use the same endpoint as the cURL - POST to /pos/v1/table with full table data
    const response = await apiClient.post('/pos/v1/table', tableData)
    return response.data.data || response.data
  },

  /**
   * Update table positions (sort order) after drag and drop
   */
  updateTablePositions: async (positions: UpdateTablePositionsRequest): Promise<void> => {
    const response = await apiClient.post('/pos/v1/table', positions)
    return response.data
  }
}

export default tablesApi
