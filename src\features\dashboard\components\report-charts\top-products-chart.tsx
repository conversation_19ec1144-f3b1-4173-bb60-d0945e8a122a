import { useMemo } from 'react'

import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui'

import { useDashboardContext } from '../../context/dashboard-context'
import { RecentSales } from '../recent-sales'
import { SortDropdown } from '../sort-dropdown'

export function TopProductsChart() {
  const { itemsSortBy, setItemsSortBy, defaultDateRange } = useDashboardContext()

  const formatDateRange = useMemo(() => {
    const formatDate = (date: Date) => {
      return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`
    }

    const fromFormatted = `${formatDate(defaultDateRange.from)} 00:00`
    const toFormatted = `${formatDate(defaultDateRange.to)} 23:59`

    return `<PERSON><PERSON><PERSON> cáo tính từ ${fromFormatted} - ${toFormatted}`
  }, [defaultDateRange])

  return (
    <Card className='col-span-1'>
      <CardHeader>
        <CardTitle>Top 5 mặt hàng bán chạy</CardTitle>
        <CardDescription className='text-muted-foreground text-xs'>{formatDateRange}</CardDescription>
        <CardDescription className='text-muted-foreground text-xs'>
          Sắp xếp theo: <SortDropdown value={itemsSortBy} onChange={setItemsSortBy} />
        </CardDescription>
        <CardAction>
          {/* <button className='cursor-pointer text-xs text-blue-600 hover:text-blue-800'>Chi tiết</button> */}
        </CardAction>
      </CardHeader>
      <CardContent>
        <RecentSales />
      </CardContent>
    </Card>
  )
}
