import React, { createContext, useContext, useMemo, useState } from 'react'

import { format } from 'date-fns'

import { usePosStores } from '@/stores'
import { vi } from 'date-fns/locale'

import { sourcesApi, type SourceData } from '@/lib/sale-sources-api'

import { usePosData } from '@/hooks/use-pos-data'

type SourcePoint = {
  date_label: string
  total_amount: number
  total_bill: number
  discount_amount: number
}

interface SourceContextType {
  dateRange: { from: Date; to: Date }
  setDateRange: React.Dispatch<React.SetStateAction<{ from: Date; to: Date }>>

  selectedCities: string[]
  selectedStores: string[]
  setSelectedCities: React.Dispatch<React.SetStateAction<string[]>>
  setSelectedStores: React.Dispatch<React.SetStateAction<string[]>>

  compareCities: string[]
  compareStores: string[]
  setCompareCities: React.Dispatch<React.SetStateAction<string[]>>
  setCompareStores: React.Dispatch<React.SetStateAction<string[]>>

  selectedStoreIds: string[]
  compareStoreIds: string[]

  data: SourcePoint[]
  compareData: SourcePoint[]
  rawData: SourceData[]
  compareRawData: SourceData[]
  isLoading: boolean
  isCompareLoading: boolean
  tableRows: Array<{
    source_id: string
    source_name: string
    total_bill: number
    revenue_net: number
    revenue_gross: number
    commission_amount: number
    discount_amount: number
    partner_marketing_amount: number
    peo_count: number
  }>

  handleUpdateDateRange: () => void
  handleExport: (mode: 'selected' | 'all') => void
}

const SourceContext = createContext<SourceContextType | undefined>(undefined)

export const SourceProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>(() => {
    const today = new Date()
    return { from: today, to: today }
  })

  const [apiTrigger, setApiTrigger] = useState(0)
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const [selectedStores, setSelectedStores] = useState<string[]>([])

  const [compareCities, setCompareCities] = useState<string[]>([])
  const [compareStores, setCompareStores] = useState<string[]>([])

  const { company, activeBrands } = usePosData()
  const companyUid = company?.id || ''
  const brandUid = activeBrands[0]?.id || ''

  const { currentBrandStores } = usePosStores()
  const allBrandStoreIds = currentBrandStores.map(store => store.id || store.store_id)

  const getSelectedStoreIds = () => {
    const storeIds: string[] = []
    selectedCities.forEach(cityId => {
      const cityStores = currentBrandStores.filter(store => store.city_uid === cityId)
      cityStores.forEach(store => {
        const storeId = store.id || store.store_id
        if (storeId && !storeIds.includes(storeId)) storeIds.push(storeId)
      })
    })
    selectedStores.forEach(storeId => {
      const store = currentBrandStores.find(s => (s.id || s.store_id) === storeId)
      if (store && !storeIds.includes(storeId)) storeIds.push(storeId)
    })
    return storeIds
  }

  const selectedStoreIds = getSelectedStoreIds()
  const finalStoreIds = selectedStoreIds.length > 0 ? selectedStoreIds : allBrandStoreIds

  const getCompareStoreIds = () => {
    const storeIds: string[] = []
    compareCities.forEach(cityId => {
      const cityStores = currentBrandStores.filter(store => store.city_uid === cityId)
      cityStores.forEach(store => {
        const storeId = store.id || store.store_id
        if (storeId && !storeIds.includes(storeId)) storeIds.push(storeId)
      })
    })
    compareStores.forEach(storeId => {
      const store = currentBrandStores.find(s => (s.id || s.store_id) === storeId)
      if (store && !storeIds.includes(storeId)) storeIds.push(storeId)
    })
    return storeIds
  }

  const compareStoreIds = getCompareStoreIds()

  const { startDate, endDate } = useMemo(() => {
    const start = new Date(dateRange.from)
    start.setHours(0, 0, 0, 0)
    const end = new Date(dateRange.to)
    end.setHours(23, 59, 59, 999)
    return { startDate: start.getTime(), endDate: end.getTime() }
  }, [dateRange.from, dateRange.to, apiTrigger])

  const handleUpdateDateRange = () => setApiTrigger(prev => prev + 1)

  const handleSetSelectedCities = (cities: string[] | ((prev: string[]) => string[])) => {
    const newCities = typeof cities === 'function' ? cities(selectedCities) : cities
    const newStoreIds = [...selectedStores]

    const wouldBeEmpty = newCities.length === 0 && newStoreIds.length === 0

    if (wouldBeEmpty) {
      setSelectedCities([])
      setSelectedStores([])
      setCompareCities([])
      setCompareStores([])
      setApiTrigger(prev => prev + 1)
    } else {
      setSelectedCities(newCities)
    }
  }

  const handleSetSelectedStores = (stores: string[] | ((prev: string[]) => string[])) => {
    const newStores = typeof stores === 'function' ? stores(selectedStores) : stores
    const newCities = [...selectedCities]

    const wouldBeEmpty = newCities.length === 0 && newStores.length === 0

    if (wouldBeEmpty) {
      setSelectedCities([])
      setSelectedStores([])
      setCompareCities([])
      setCompareStores([])
      setApiTrigger(prev => prev + 1)
    } else {
      setSelectedStores(newStores)
    }
  }

  const [rawData, setRawData] = useState<SourceData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [compareRawData, setCompareRawData] = useState<SourceData[]>([])
  const [isCompareLoading, setIsCompareLoading] = useState(false)

  React.useEffect(() => {
    let mounted = true
    const fetchData = async () => {
      if (!companyUid || !brandUid || finalStoreIds.length === 0) {
        setRawData([])
        return
      }
      setIsLoading(true)
      try {
        const res = await sourcesApi.getSourcesSummary({
          companyUid,
          brandUid,
          startDate,
          endDate,
          storeUids: finalStoreIds,
          byDays: 1,
          limit: 100
        })
        if (mounted) setRawData(res.data || [])
      } finally {
        if (mounted) setIsLoading(false)
      }
    }
    fetchData()
    return () => {
      mounted = false
    }
  }, [companyUid, brandUid, startDate, endDate, finalStoreIds.join(','), apiTrigger])

  React.useEffect(() => {
    let mounted = true
    const fetchCompare = async () => {
      if (!companyUid || !brandUid || compareStoreIds.length === 0) {
        setCompareRawData([])
        return
      }
      setIsCompareLoading(true)
      try {
        const res = await sourcesApi.getSourcesSummary({
          companyUid,
          brandUid,
          startDate,
          endDate,
          storeUids: compareStoreIds,
          byDays: 1,
          limit: 100
        })
        if (mounted) setCompareRawData(res.data || [])
      } finally {
        if (mounted) setIsCompareLoading(false)
      }
    }
    fetchCompare()
    return () => {
      mounted = false
    }
  }, [companyUid, brandUid, startDate, endDate, compareStoreIds.join(','), apiTrigger])

  const safeFormatDateLabel = (input: string): string => {
    try {
      const d = new Date(input)
      if (!isNaN(d.getTime())) {
        return format(d, 'dd/MM', { locale: vi })
      }
      if (/^\d{2}\/\d{2}$/.test(input)) return input
      const m = input.match(/^(\d{4})-(\d{2})-(\d{2})$/)
      if (m) return `${m[3]}/${m[2]}`
      return input
    } catch {
      return input
    }
  }

  const data: SourcePoint[] = React.useMemo(() => {
    const dateMap = new Map<string, { total_amount: number; total_bill: number; discount_amount: number }>()
    ;(rawData || []).forEach(p => {
      ;(p.list_data || []).forEach(d => {
        const prev = dateMap.get(d.date) || { total_amount: 0, total_bill: 0, discount_amount: 0 }
        dateMap.set(d.date, {
          total_amount: prev.total_amount + (d.revenue_net ?? 0),
          total_bill: prev.total_bill + (d.total_bill ?? 0),
          discount_amount: prev.discount_amount + (d.discount_amount ?? 0)
        })
      })
    })

    return Array.from(dateMap.entries())
      .sort(([a], [b]) => (a < b ? -1 : a > b ? 1 : 0))
      .map(([date, v]) => ({
        date_label: safeFormatDateLabel(date),
        total_amount: v.total_amount,
        total_bill: v.total_bill,
        discount_amount: v.discount_amount
      }))
  }, [rawData])

  const compareData: SourcePoint[] = React.useMemo(() => {
    const dateMap = new Map<string, { total_amount: number; total_bill: number; discount_amount: number }>()
    ;(compareRawData || []).forEach(p => {
      ;(p.list_data || []).forEach(d => {
        const prev = dateMap.get(d.date) || { total_amount: 0, total_bill: 0, discount_amount: 0 }
        dateMap.set(d.date, {
          total_amount: prev.total_amount + (d.revenue_net ?? 0),
          total_bill: prev.total_bill + (d.total_bill ?? 0),
          discount_amount: prev.discount_amount + (d.discount_amount ?? 0)
        })
      })
    })

    return Array.from(dateMap.entries())
      .sort(([a], [b]) => (a < b ? -1 : a > b ? 1 : 0))
      .map(([date, v]) => ({
        date_label: safeFormatDateLabel(date),
        total_amount: v.total_amount,
        total_bill: v.total_bill,
        discount_amount: v.discount_amount
      }))
  }, [compareRawData])

  const tableRows = React.useMemo(() => {
    return (rawData || []).map(item => ({
      source_id: (item as any).source_id || '',
      source_name: (item as any).source_name || '',
      total_bill: (item as any).total_bill || 0,
      revenue_net: (item as any).revenue_net || 0,
      revenue_gross: (item as any).revenue_gross || 0,
      commission_amount: (item as any).commission_amount || 0,
      discount_amount: (item as any).discount_amount || 0,
      partner_marketing_amount: (item as any).partner_marketing_amount || 0,
      peo_count: (item as any).peo_count || 0
    }))
  }, [rawData])

  const handleExport = async (mode: 'selected' | 'all') => {
    try {
      const exportStoreIds = mode === 'selected' ? finalStoreIds : allBrandStoreIds

      const res = await sourcesApi.getSourcesSummary({
        companyUid,
        brandUid,
        startDate,
        endDate,
        storeUids: exportStoreIds,
        byDays: 1,
        limit: 100
      })

      const exportData = res.data || []
      if (exportData.length === 0) return

      const storeNames = exportStoreIds
        .map(id => currentBrandStores.find(s => (s.id || s.store_id) === id)?.store_name)
        .filter((name): name is string => Boolean(name))
        .slice(0, 3)

      const { exportSourceRevenueToExcel } = await import('../utils/excel-export')
      await exportSourceRevenueToExcel(exportData as any, {
        dateRange,
        selectedStoreNames: storeNames,
        filename: 'sale-sources.xlsx'
      })
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const value: SourceContextType = {
    dateRange,
    setDateRange,
    selectedCities,
    selectedStores,
    setSelectedCities: handleSetSelectedCities,
    setSelectedStores: handleSetSelectedStores,
    compareCities,
    compareStores,
    setCompareCities,
    setCompareStores,
    selectedStoreIds,
    compareStoreIds,
    data,
    compareData,
    rawData,
    compareRawData,
    isLoading,
    isCompareLoading,
    tableRows,
    handleUpdateDateRange,
    handleExport
  }

  return <SourceContext.Provider value={value}>{children}</SourceContext.Provider>
}

export const useSourceContext = () => {
  const ctx = useContext(SourceContext)
  if (!ctx) throw new Error('useSourceContext must be used within SourceProvider')
  return ctx
}
