import { Main } from '@/components/layout/main'
import { <PERSON><PERSON>, Card, CardContent, CardHeader } from '@/components/ui'

import { ExtraPointTable, ExtraPointModalForm } from './components'
import { ExtraPointProvider, useExtraPointContext } from './context'

function ExtraPointContent() {
  const { setCreateDialogOpen, createDialogOpen, editDialogOpen, setEditDialogOpen, selectedExtraPoint } =
    useExtraPointContext()

  const handleCreateNew = () => {
    setCreateDialogOpen(true)
  }

  return (
    <Main>
      <div className='container mx-auto space-y-6 py-6'>
        <div className='flex items-center justify-between'>
          <h1 className='text-3xl font-bold tracking-tight'>Hệ số tích điểm</h1>
        </div>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
            <Button onClick={handleCreateNew}>Tạo mới</Button>
          </CardHeader>
          <CardContent>
            <ExtraPointTable />
          </CardContent>
        </Card>

        <ExtraPointModalForm open={createDialogOpen} onOpenChange={setCreateDialogOpen} />
        <ExtraPointModalForm
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          extraPoint={selectedExtraPoint || undefined}
        />
      </div>
    </Main>
  )
}

export default function ExtraPointPage() {
  return (
    <ExtraPointProvider>
      <ExtraPointContent />
    </ExtraPointProvider>
  )
}
