import { ConfirmDialog } from '@/components/confirm-dialog'

import { useItemsInCity } from '../context'
import { useDeleteItemInCity } from '../hooks'
import { ItemsInCityMutate } from './items-in-city-mutate'

export function ItemsInCityDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useItemsInCity()
  const deleteItemInCity = useDeleteItemInCity()

  return (
    <>
      <ItemsInCityMutate
        key='items-in-city-create'
        open={open === 'create'}
        onOpenChange={isOpen => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
      />

      {currentRow && (
        <>
          <ItemsInCityMutate
            key={`items-in-city-copy-${currentRow.id}`}
            open={open === 'copy'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
            isCopyMode={true}
          />

          <ItemsInCityMutate
            key={`items-in-city-update-${currentRow.id}`}
            open={open === 'update'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={open === 'delete'}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={async () => {
              setOpen(null)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              deleteItemInCity.mutate(currentRow.id)
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}
    </>
  )
}
