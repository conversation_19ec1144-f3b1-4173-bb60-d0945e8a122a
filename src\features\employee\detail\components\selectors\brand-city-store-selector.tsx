import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, Input, Button } from '@/components/ui'

import { useBrandCityStoreSelector } from '../../hooks'
import { HierarchicalList } from '../hierarchical-list'

interface BrandCityStoreSelectorProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedItems: string[]
  onSave: (selectedItems: string[]) => void
}

export function BrandCityStoreSelector({ open, onOpenChange, selectedItems, onSave }: BrandCityStoreSelectorProps) {
  const {
    searchTerm,
    setSearchTerm,
    expandedBrands,
    expandedCities,
    localSelectedItems,
    filteredData,
    toggleBrandExpansion,
    toggleCityExpansion,
    handleItemToggle,
    isItemSelected,
    isItemIndeterminate,
    getSelectedCount
  } = useBrandCityStoreSelector(selectedItems)

  const handleSave = () => {
    onSave(Array.from(localSelectedItems))
    onOpenChange(false)
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-4xl'>
        <DialogHeader>
          <DialogTitle className='text-center'>Chọn thương hiệu và thành phố, nhà hàng</DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          <div>
            <Input
              placeholder='Tìm kiếm thương hiệu, thành phố, cửa hàng...'
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />
          </div>

          <div className='max-h-96 overflow-y-auto'>
            <HierarchicalList
              filteredData={filteredData}
              expandedBrands={expandedBrands}
              expandedCities={expandedCities}
              toggleBrandExpansion={toggleBrandExpansion}
              toggleCityExpansion={toggleCityExpansion}
              handleItemToggle={handleItemToggle}
              isItemSelected={isItemSelected}
              isItemIndeterminate={isItemIndeterminate}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={handleCancel}>
            Hủy
          </Button>
          <Button onClick={handleSave}>Lưu ({getSelectedCount()} mục)</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
