import type { UseFormReturn } from 'react-hook-form'

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

import type { FormData } from '../utils/item-data-mapper'

interface MenuItemBasicFieldsProps {
  form: UseFormReturn<FormData>
}

export function MenuItemBasicFields({ form }: MenuItemBasicFieldsProps) {
  return (
    <div className='space-y-4'>
      {/* Tên món */}
      <FormField
        control={form.control}
        name='item_name'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Tên <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Input placeholder='Nhập tên món' {...field} className='flex-1' />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Giá */}
      <FormField
        control={form.control}
        name='ots_price'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-center gap-4'>
              <FormLabel className='w-40 text-sm font-medium text-gray-700'>
                Giá <span className='text-red-500'>*</span>
              </FormLabel>
              <FormControl>
                <Input type='number' placeholder='0' {...field} className='flex-1' />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Mã món */}
      <FormField
        control={form.control}
        name='item_id'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-start gap-4'>
              <FormLabel className='mt-2 w-40 text-sm font-medium text-gray-700'>Mã món</FormLabel>
              <div className='flex-1'>
                <FormControl>
                  <Input
                    placeholder='Nếu để trống, hệ thống sẽ tự động tạo mới mã món'
                    {...field}
                    className='flex-1'
                  />
                </FormControl>
              </div>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Mã barcode */}
      <FormField
        control={form.control}
        name='item_id_barcode'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-start gap-4'>
              <FormLabel className='mt-2 w-40 text-sm font-medium text-gray-700'>
                Mã barcode
              </FormLabel>
              <div className='flex-1'>
                <FormControl>
                  <Input
                    placeholder='Nếu bạn sử dụng tính năng scan QR tại POS hãy tạo mã barcode cho món'
                    {...field}
                    className='flex-1'
                  />
                </FormControl>
              </div>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Mô tả */}
      <FormField
        control={form.control}
        name='description'
        render={({ field }) => (
          <FormItem>
            <div className='flex items-start gap-4'>
              <FormLabel className='mt-2 w-40 text-sm font-medium text-gray-700'>Mô tả</FormLabel>
              <FormControl>
                <Textarea placeholder='Mô tả' {...field} className='min-h-[80px] flex-1' />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  )
}
