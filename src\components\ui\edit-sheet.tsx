import React from 'react'

import { <PERSON><PERSON>, Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTitle } from '@/components/ui'

interface EditSheetProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  children: React.ReactNode
  isLoading?: boolean
  onSave?: () => void
  onCancel?: () => void
  saveText?: string
  cancelText?: string
  showActions?: boolean
}

export function EditSheet({
  open,
  onOpenChange,
  title,
  children,
  isLoading = false,
  onSave,
  onCancel,
  saveText = 'Lưu thay đổi',
  cancelText = 'Hủy',
  showActions = true
}: EditSheetProps) {
  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      onOpenChange(false)
    }
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className={`w-[55vw] max-w-none overflow-y-auto`} side='right' hideCloseButton={true}>
        <SheetHeader className='border-b pb-4'>
          <SheetTitle className='flex items-center justify-between gap-2'>
            <span className='text-lg font-semibold'>{title}</span>
            <Button variant='default' size='sm' onClick={onSave} disabled={isLoading}>
              {isLoading ? 'Đang cập nhật...' : 'Cập nhật'}
            </Button>
          </SheetTitle>
        </SheetHeader>

        <div>{children}</div>

        {showActions && (
          <div className='mt-6 flex justify-end gap-3 border-t pt-4'>
            <Button variant='outline' onClick={handleCancel} disabled={isLoading}>
              {cancelText}
            </Button>
            <Button onClick={onSave} disabled={isLoading} className='bg-blue-600 hover:bg-blue-700'>
              {isLoading ? 'Đang lưu...' : saveText}
            </Button>
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}
