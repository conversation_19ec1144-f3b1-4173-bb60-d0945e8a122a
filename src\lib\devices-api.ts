import { Device, DeviceType } from '@/types'

import { useAuthStore } from '@/stores/authStore'

import { apiClient } from './api/pos/pos-api'

// API response device structure
export interface ApiDevice {
  id: string
  device_name: string
  device_code: string
  device_id: string | null
  active: number
  type: string
  address: string | null
  version_app: string | null
  updated_at: string
  store_uid: string
  device_type_local: number
}

export interface DevicesApiResponse {
  data: ApiDevice[]
  track_id: string
}

// Parameters for devices API
export interface DevicesApiParams {
  company_uid: string
  brand_uid: string
  page?: number
  list_store_uid?: string
  search?: string
  type?: string
  skip_limit?: boolean
}

export interface CopyDeviceParams {
  company_uid: string
  brand_uid: string
  id: string
  newDeviceName: string
}

export interface CreateDeviceParams {
  name: string
  store_id: string
  device_type: string
  brand_uid: string
}

// Device extra_data structure from API response
export interface DeviceExtraData {
  ip_server: string | null
  value_vat: number
  is_payment: number
  is_pos_mini: number
  items_order: unknown[]
  column_table: number
  state_operate: number
  enable_tab_kds: number
  item_types_order: unknown[]
  link_home_images: string
  allow_print_label: number
  device_type_local: number
  link_baner_images: string
  order_token_state: number
  dual_screen_enable: number
  enable_cash_drawer: number
  so_item_black_list: unknown[]
  area_manager_enable: number
  is_print_food_check: number
  use_ip_server_manual: number
  sale_change_vat_enable: number
  type_allow_connect_pos: number
  ip_from_server_register: string | null
  new_restaurant_interface: number
  so_payment_method_black_list: unknown[]
  pda_payment_method_black_list: unknown[]
}

// Full device data structure from GET API response
export interface GetDeviceApiData {
  id: string
  created_at: string
  created_by: string
  updated_at: string
  updated_by: string
  deleted: boolean
  deleted_by: string | null
  deleted_at: string | null
  device_id: string | null
  device_code: string
  device_name: string
  module_name: string | null
  address: string | null
  push_id: string | null
  hardware_info: string | null
  software_info: string | null
  description: string | null
  is_test: number
  extra_data: DeviceExtraData
  active: number
  revision: string
  store_uid: string
  brand_uid: string
  company_uid: string
  city_uid: string
  store_id: string
  brand_id: string
  compunknown_id: string
  type: string
  version_app: string | null
  my_ip_local: string | null
  sort: number
  ip_local_server: string | null
  time_display: string | null
  time_zone: string | null
  index_device_in_store: number
  extra_info: string | null
  md_store_id: string
  is_ios: number
  partner: string | null
}

// GET device API response wrapper
export interface GetDeviceApiResponse {
  data: GetDeviceApiData
  track_id: string
}

// POST create device API response
export interface CreateDeviceApiResponse {
  data: GetDeviceApiData
  track_id?: string
}

// Partner module add-on API interfaces
export interface PartnerModuleAddOnParams {
  company_uid: string
  list_brand_uid: string
}

export interface PartnerModuleAddOnData {
  id: string
  name: string
  description: string | null
  status: string
  created_at: string
  updated_at: string
  // Add more fields based on actual API response
}

export interface PartnerModuleAddOnApiResponse {
  data: PartnerModuleAddOnData[]
  track_id?: string
}

/**
 * Fetch devices from POS API
 */
export const fetchDevices = async (params: DevicesApiParams): Promise<DevicesApiResponse> => {
  const queryParams = new URLSearchParams({
    company_uid: params.company_uid,
    brand_uid: params.brand_uid,
    page: (params.page || 1).toString()
  })

  if (params.list_store_uid) {
    queryParams.append('list_store_uid', params.list_store_uid)
  }

  if (params.search) {
    queryParams.append('search', params.search)
  }

  if (params.type) {
    queryParams.append('type', params.type)
  }

  if (params.skip_limit) {
    queryParams.append('skip_limit', 'true')
  }

  const response = await apiClient.get<DevicesApiResponse>(`/v1/pos-devices?${queryParams.toString()}`)

  return response.data
}

/**
 * Convert API device to our internal Device format
 */
export const convertApiDeviceToDevice = (apiDevice: ApiDevice, storeName?: string) => {
  return {
    id: apiDevice.id,
    name: apiDevice.device_name,
    type: getDeviceTypeFromApi(apiDevice.type) as 'POS' | 'POS_MINI' | 'PDA',
    version: apiDevice.version_app || '',
    storeId: apiDevice.store_uid,
    storeName: storeName || 'Unknown Store',
    status: (apiDevice.active === 1 ? 'active' : 'inactive') as 'active' | 'inactive' | 'maintenance' | 'error',
    lastUpdate: new Date(apiDevice.updated_at),
    serialNumber: apiDevice.device_code,
    manufacturer: 'None',
    model: apiDevice.type,
    ipAddress: apiDevice.address || undefined,
    macAddress: undefined,
    createdAt: new Date(apiDevice.updated_at),
    updatedAt: new Date(apiDevice.updated_at),
    isActive: apiDevice.active === 1,
    device_type_local: apiDevice.device_type_local
  }
}

/**
 * Map API device type to our internal device type
 */
const getDeviceTypeFromApi = (apiType: string): string => {
  const typeMap: Record<string, string> = {
    POS: 'POS',
    'POS MINI': 'POS_MINI',
    POS_MINI: 'POS_MINI',
    PDA: 'PDA',
    KDS: 'KDS',
    KDS_ORDER_CONTROL: 'KDS_ORDER_CONTROL',
    KDS_MAKER: 'KDS_MAKER',
    SELF_ORDER: 'SELF_ORDER'
  }

  return typeMap[apiType] || typeMap[apiType.toUpperCase()] || 'POS'
}

/**
 * Copy a device with a new name
 * Two-step process: GET device details → POST create copy
 */
export const copyDevice = async (params: CopyDeviceParams): Promise<Device> => {
  const { company_uid, brand_uid, id, newDeviceName } = params

  // Step 1: Get the device details
  const getDeviceParams = new URLSearchParams({
    company_uid,
    brand_uid,
    id
  })

  const deviceResponse = await apiClient.get<GetDeviceApiResponse>(`/v1/pos-devices/id?${getDeviceParams.toString()}`)

  if (!deviceResponse.data?.data) {
    throw new Error('Device not found')
  }

  const originalDevice: GetDeviceApiData = deviceResponse.data.data

  // Step 2: Create a copy with new name using v3 API
  const copyPayload = {
    device_name: newDeviceName,
    type: originalDevice.type,
    company_uid: originalDevice.company_uid,
    brand_uid: originalDevice.brand_uid,
    store_uid: originalDevice.store_uid,
    extra_data: originalDevice.extra_data
  }

  const copyResponse = await apiClient.post<CreateDeviceApiResponse>(`/v3/pos-cms/pos-device`, copyPayload)

  if (copyResponse.data?.data) {
    // Convert the response to our Device format
    const copiedDevice: GetDeviceApiData = copyResponse.data.data
    return {
      id: copiedDevice.id,
      name: copiedDevice.device_name,
      type: copiedDevice.type as DeviceType,
      status: copiedDevice.active === 1 ? 'active' : 'inactive',
      storeName: '', // Will be populated by the list API
      lastUpdate: new Date(copiedDevice.updated_at),
      isActive: copiedDevice.active === 1,
      version: copiedDevice.version_app || '',
      storeId: copiedDevice.store_uid,
      serialNumber: copiedDevice.device_code || '',
      manufacturer: 'Unknown',
      model: 'Unknown',
      createdAt: new Date(copiedDevice.created_at),
      updatedAt: new Date(copiedDevice.updated_at),
      ipAddress: copiedDevice.my_ip_local || undefined
    }
  }

  throw new Error('Failed to copy device')
}

/**
 * Create a new device
 */
export const createDevice = async (params: CreateDeviceParams): Promise<Device> => {
  const authState = useAuthStore.getState().auth
  let companyUid = authState.company?.id || ''

  if (!companyUid) {
    try {
      const posUserData = localStorage.getItem('pos_user_data')
      const posCompanyData = localStorage.getItem('pos_company_data')

      if (posUserData) {
        const userData = JSON.parse(posUserData)
        companyUid = companyUid || userData.company_uid || ''
      }

      if (posCompanyData) {
        const companyData = JSON.parse(posCompanyData)
        companyUid = companyUid || companyData.id || ''
      }
    } catch (error) {}
  }

  if (!companyUid) {
    throw new Error(`Company UID not found. Company: ${companyUid}`)
  }

  const getDeviceTypeLocal = (deviceType: string): number => {
    switch (deviceType) {
      case 'POS':
        return 1
      case 'POS_MINI':
        return 2
      case 'PDA':
        return 3
      case 'KDS':
        return 4
      case 'KDS_ORDER_CONTROL':
        return 5
      case 'KDS_MAKER':
        return 6
      case 'SELF_ORDER':
        return 7
      default:
        return 1
    }
  }

  const payload = {
    device_name: params.name,
    type: params.device_type,
    company_uid: companyUid,
    brand_uid: params.brand_uid,
    store_uid: params.store_id,
    extra_data: {
      ip_server: null,
      value_vat: 0,
      is_pos_mini: params.device_type === 'POS_MINI' ? 1 : 0,
      device_type_local: getDeviceTypeLocal(params.device_type),
      dual_screen_enable: 0,
      area_manager_enable: 1,
      use_ip_server_manual: 0,
      sale_change_vat_enable: 0,
      ip_from_server_register: null
    }
  }

  const response = await apiClient.post<CreateDeviceApiResponse>(`/v3/pos-cms/pos-device`, payload)

  if (response.data?.data) {
    const newDevice: GetDeviceApiData = response.data.data
    return {
      id: newDevice.id,
      name: newDevice.device_name,
      type: newDevice.type as DeviceType,
      status: newDevice.active === 1 ? 'active' : 'inactive',
      storeName: '', // Will be populated by the list API
      lastUpdate: new Date(newDevice.updated_at),
      isActive: newDevice.active === 1,
      version: newDevice.version_app || '',
      storeId: newDevice.store_uid,
      serialNumber: newDevice.device_code || '',
      manufacturer: 'Unknown',
      model: 'Unknown',
      createdAt: new Date(newDevice.created_at),
      updatedAt: new Date(newDevice.updated_at),
      ipAddress: newDevice.my_ip_local || undefined,
      device_code: newDevice.device_code // Add device_code to response
    }
  }

  throw new Error('Failed to create device')
}

/**
 * Get device detail by ID
 */
export const getDeviceDetail = async (deviceId: string, companyUid: string, brandUid: string): Promise<Device> => {
  console.log('🔍 getDeviceDetail called with:', { deviceId, companyUid, brandUid })

  // Validate required parameters
  if (!deviceId || !companyUid || !brandUid) {
    throw new Error(`Missing required parameters: deviceId=${deviceId}, companyUid=${companyUid}, brandUid=${brandUid}`)
  }

  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id: deviceId
  })

  console.log('🚀 Making API call to:', `/v1/pos-devices/id?${queryParams.toString()}`)
  const response = await apiClient.get<GetDeviceApiResponse>(`/v1/pos-devices/id?${queryParams.toString()}`)

  if (response.data?.data) {
    const deviceData: GetDeviceApiData = response.data.data
    return {
      id: deviceData.id,
      name: deviceData.device_name,
      type: deviceData.type as DeviceType,
      status: deviceData.active === 1 ? 'active' : 'inactive',
      storeName: '', // Will be populated by the hook
      lastUpdate: new Date(deviceData.updated_at),
      isActive: deviceData.active === 1,
      version: deviceData.version_app || '',
      storeId: deviceData.store_uid,
      serialNumber: deviceData.device_code || '',
      manufacturer: 'Unknown',
      model: deviceData.type,
      createdAt: new Date(deviceData.created_at),
      updatedAt: new Date(deviceData.updated_at),
      ipAddress: deviceData.my_ip_local || deviceData.address || undefined,
      macAddress: undefined,
      device_code: deviceData.device_code,
      // Add missing fields for UI display
      device_id: deviceData.device_id,
      time_display: deviceData.time_display,
      extra_data: deviceData.extra_data,
      address: deviceData.address,
      hardware_info: deviceData.hardware_info,
      software_info: deviceData.software_info,
      time_zone: deviceData.time_zone,
      device_name: deviceData.device_name,
      my_ip_local: deviceData.my_ip_local,
      version_app: deviceData.version_app
    } as Device & {
      device_id: string | null
      time_display: string | null
      extra_data: DeviceExtraData
      address: string | null
      hardware_info: string | null
      software_info: string | null
      time_zone: string | null
      device_name: string
      my_ip_local: string | null
      version_app: string | null
    }
  }

  throw new Error('Device not found')
}

/**
 * Get device detail raw data (for update operations)
 */
export const getDeviceDetailRaw = async (
  deviceId: string,
  companyUid: string,
  brandUid: string
): Promise<GetDeviceApiData> => {
  console.log('🔍 getDeviceDetailRaw called with:', { deviceId, companyUid, brandUid })

  // Validate required parameters
  if (!deviceId || !companyUid || !brandUid) {
    throw new Error(`Missing required parameters: deviceId=${deviceId}, companyUid=${companyUid}, brandUid=${brandUid}`)
  }

  const queryParams = new URLSearchParams({
    company_uid: companyUid,
    brand_uid: brandUid,
    id: deviceId
  })

  console.log('🚀 Making API call to:', `/v1/pos-devices/id?${queryParams.toString()}`)
  const response = await apiClient.get<GetDeviceApiResponse>(`/v1/pos-devices/id?${queryParams.toString()}`)

  console.log('📦 Raw API response:', response.data)

  if (!response.data?.data) {
    throw new Error('Device not found')
  }

  return response.data.data
}

/**
 * Update device
 */
export const updateDevice = async (
  deviceId: string,
  updateData: Partial<Device> & { extra_data?: any }
): Promise<Device> => {
  const authState = useAuthStore.getState().auth
  let companyUid = authState.company?.id || ''
  let brandUid = authState.brands?.[0]?.id || ''

  if (!companyUid || !brandUid) {
    try {
      const posUserData = localStorage.getItem('pos_user_data')
      const posCompanyData = localStorage.getItem('pos_company_data')
      const posBrandsData = localStorage.getItem('pos_brands_data')

      if (posUserData) {
        const userData = JSON.parse(posUserData)
        companyUid = companyUid || userData.company_uid || ''
      }

      if (posCompanyData) {
        const companyData = JSON.parse(posCompanyData)
        companyUid = companyUid || companyData.id || ''
      }

      if (posBrandsData) {
        const brandsData = JSON.parse(posBrandsData)
        if (Array.isArray(brandsData) && brandsData.length > 0) {
          brandUid = brandUid || brandsData[0].id || ''
        }
      }
    } catch (error) {}
  }

  if (!companyUid || !brandUid) {
    throw new Error(`Company UID or Brand UID not found. Company: ${companyUid}, Brand: ${brandUid}`)
  }

  const currentDeviceData = await getDeviceDetailRaw(deviceId, companyUid, brandUid)

  // Prepare the full payload by merging current data with updates
  const payload = {
    ...currentDeviceData,
    id: deviceId,
    device_name: updateData.name !== undefined ? updateData.name : currentDeviceData.device_name,
    type: updateData.type !== undefined ? updateData.type : currentDeviceData.type,
    active: updateData.status ? (updateData.status === 'active' ? 1 : 0) : currentDeviceData.active,
    address: updateData.ipAddress !== undefined ? updateData.ipAddress : currentDeviceData.address,
    extra_data: updateData.extra_data
      ? {
          ...currentDeviceData.extra_data,
          ...updateData.extra_data
        }
      : currentDeviceData.extra_data
  }

  const response = await apiClient.post<CreateDeviceApiResponse>(`/v3/pos-cms/pos-device`, payload)

  if (response.data?.data) {
    const deviceData: GetDeviceApiData = response.data.data
    return {
      id: deviceData.id,
      name: deviceData.device_name,
      type: deviceData.type as DeviceType,
      status: deviceData.active === 1 ? 'active' : 'inactive',
      storeName: '', // Will be populated by the hook
      lastUpdate: new Date(deviceData.updated_at),
      isActive: deviceData.active === 1,
      version: deviceData.version_app || '',
      storeId: deviceData.store_uid,
      serialNumber: deviceData.device_code || '',
      manufacturer: 'Unknown',
      model: deviceData.type,
      createdAt: new Date(deviceData.created_at),
      updatedAt: new Date(deviceData.updated_at),
      ipAddress: deviceData.my_ip_local || deviceData.address || undefined,
      macAddress: undefined,
      device_code: deviceData.device_code
    }
  }

  throw new Error('Failed to update device')
}

/**
 * Delete device
 */
export const deleteDevice = async (deviceId: string): Promise<void> => {
  await apiClient.delete(`/v3/pos-cms/pos-device/${deviceId}`)
}

export const devicesApi = {
  fetchDevices,
  copyDevice,
  createDevice,
  getDeviceDetail,
  updateDevice,
  deleteDevice
}
