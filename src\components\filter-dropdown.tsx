import { useState } from 'react'

import { Check, ChevronsUpDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

interface FilterOption {
  value: string
  label: string
}

interface FilterDropdownProps {
  value: string
  onValueChange: (value: string) => void
  options: FilterOption[]
  isLoading?: boolean
  placeholder?: string
  className?: string
  allOptionLabel?: string
  loadingText?: string
  emptyText?: string
  showAllOption?: boolean
  searchPlaceholder?: string
}

export function FilterDropdown({
  value,
  onValueChange,
  options,
  isLoading = false,
  placeholder = 'Chọn...',
  className = 'w-48',
  allOptionLabel = 'Tất cả',
  loadingText = 'Đang tải...',
  emptyText = 'Không có dữ liệu',
  showAllOption = true,
  searchPlaceholder = 'Tìm kiếm...'
}: FilterDropdownProps) {
  const [open, setOpen] = useState(false)

  const allOptions = showAllOption ? [{ value: 'all', label: allOptionLabel }, ...options] : options

  const selectedOption = allOptions.find(option => option.value === value)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className={cn('justify-between', className, !value && 'text-muted-foreground')}
        >
          {selectedOption ? selectedOption.label : placeholder}
          <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-full p-0' style={{ width: 'var(--radix-popover-trigger-width)' }}>
        <Command>
          <CommandInput placeholder={searchPlaceholder} />
          <CommandList>
            <CommandEmpty>{emptyText}</CommandEmpty>
            <CommandGroup>
              {isLoading ? (
                <CommandItem disabled>{loadingText}</CommandItem>
              ) : (
                <>
                  {allOptions.map(option => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={currentValue => {
                        onValueChange(currentValue)
                        setOpen(false)
                      }}
                    >
                      <Check className={cn('mr-2 h-4 w-4', value === option.value ? 'opacity-100' : 'opacity-0')} />
                      {option.label}
                    </CommandItem>
                  ))}

                  {!isLoading && options.length === 0 && !showAllOption && (
                    <CommandItem disabled>{emptyText}</CommandItem>
                  )}
                </>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
