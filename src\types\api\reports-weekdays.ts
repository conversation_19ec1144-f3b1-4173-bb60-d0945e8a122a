// Reports Weekdays API Types

export interface ReportsWeekdaysData {
  day_of_week: 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY' | 'SATURDAY' | 'SUNDAY'
  revenue: number
  transaction_quantity: number
  peo_count: number
  deduct_tax_amount: number
}

export interface ReportsWeekdaysResponse {
  data: ReportsWeekdaysData[]
  message: string
  track_id: string
}

export interface GetReportsWeekdaysParams {
  brand_uid: string
  company_uid: string
  list_store_uid: string
  start_date: number
  end_date: number
  store_open_at?: number
  limit?: number
}
