/**
 * Utility functions for parsing Excel data in menu items
 */

/**
 * Parse active status from Excel data
 * Handles multiple formats: text, numbers, strings, booleans
 */
export const parseActiveStatus = (value: any): number => {
  if (value === 'Hoạt động' || value === 1 || value === '1' || value === true) {
    return 1
  }
  return 0
}

/**
 * Parse boolean status from Excel data (Có/Không format)
 * Handles multiple formats: text, numbers, strings, booleans
 */
export const parseBooleanStatus = (value: any): number => {
  if (value === 'Có' || value === 1 || value === '1' || value === true) {
    return 1
  }
  return 0
}
