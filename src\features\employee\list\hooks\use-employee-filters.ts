import { useState, useMemo } from 'react'

import type { User } from '@/types/user'

interface UseEmployeeFiltersProps {
  employees?: User[]
}

export function useEmployeeFilters({ employees }: UseEmployeeFiltersProps = {}) {
  const [searchTerm, setSearchTerm] = useState<string>('')
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [selectedRole, setSelectedRole] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedBrand, setSelectedBrand] = useState<string>('all')
  const [selectedCity, setSelectedCity] = useState<string>('all')
  const [selectedStore, setSelectedStore] = useState<string>('all')

  const filteredEmployees = useMemo(() => {
    if (!employees) return []

    let filtered = employees

    if (selectedRole !== 'all') {
      filtered = filtered.filter(employee => employee.role_id === selectedRole)
    }

    if (selectedStatus !== 'all') {
      const isActive = selectedStatus === 'active'
      filtered = filtered.filter(employee => employee.active === (isActive ? 1 : 0))
    }

    return filtered
  }, [employees, selectedRole, selectedStatus])

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
  }

  const handleSearchSubmit = () => {
    setSearchTerm(searchQuery)
  }

  return {
    // Search state
    searchTerm,
    searchQuery,
    handleSearchChange,
    handleSearchSubmit,

    // Filter state
    selectedRole,
    setSelectedRole,
    selectedStatus,
    setSelectedStatus,
    selectedBrand,
    setSelectedBrand,
    selectedCity,
    setSelectedCity,
    selectedStore,
    setSelectedStore,

    // Filtered data
    filteredEmployees
  }
}
