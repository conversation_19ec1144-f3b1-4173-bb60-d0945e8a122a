import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from '@/components/ui'

interface DeviceDetailModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  device: {
    id: string
    name: string
    description: string
    images: string[]
    detailInformation?: string[]
  } | null
}

export function DeviceDetailModal({
  open,
  onOpenChange,
  device,
}: DeviceDetailModalProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [api, setApi] = useState<CarouselApi>()

  useEffect(() => {
    if (!api) return

    const onSelect = () => {
      setCurrentImageIndex(api.selectedScrollSnap())
    }

    api.on('select', onSelect)
    onSelect() // Set initial index

    return () => {
      api.off('select', onSelect)
    }
  }, [api])

  if (!device) return null

  // Extract device type from name (remove "Thiết bị" prefix)
  const deviceType = device.name.replace('Thiết bị ', '')

  const handleDotClick = (index: number) => {
    if (api) {
      api.scrollTo(index)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='!h-[600px] !max-h-[600px] !w-[1400px] !max-w-[1400px] overflow-hidden'>
        <DialogHeader>
          <DialogTitle className='text-center text-2xl font-bold'>
            {deviceType}
          </DialogTitle>
        </DialogHeader>

        <div className='mt-6 grid h-[500px] grid-cols-2 gap-8'>
          <div className='flex flex-col items-center space-y-4'>
            <Carousel setApi={setApi} className='!w-[700px]'>
              <CarouselContent>
                {device.images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className='h-[400px] w-[700px] overflow-hidden rounded-lg border bg-gray-50'>
                      <img
                        src={image}
                        alt={`${deviceType} - Image ${index + 1}`}
                        className='h-full w-full object-contain p-4'
                        onError={(e) => {
                          const target = e.target as HTMLImageElement
                          target.src = '/placeholder-image.jpg'
                        }}
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              {device.images.length > 1 && (
                <>
                  <CarouselPrevious className='top-1/2 left-2 -translate-y-1/2' />
                  <CarouselNext className='top-1/2 right-2 -translate-y-1/2' />
                </>
              )}
            </Carousel>

            {/* Dot Navigation */}
            {device.images.length > 1 && (
              <div className='flex space-x-2'>
                {device.images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => handleDotClick(index)}
                    className={`h-3 w-3 rounded-full transition-all duration-200 ${
                      currentImageIndex === index
                        ? 'scale-110 bg-blue-500'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                    aria-label={`Go to image ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>

          <div className='h-full overflow-y-auto pr-4'>
            <ul className='space-y-3 text-lg leading-relaxed text-gray-700'>
              {(device.detailInformation || [device.description]).map(
                (item, index) => (
                  <li key={index} className='flex items-start'>
                    <span className='mt-1 mr-2 text-blue-500'>•</span>
                    <span>{item}</span>
                  </li>
                )
              )}
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
