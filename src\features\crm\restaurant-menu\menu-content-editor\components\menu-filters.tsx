import { useCrmItemTypeOptions } from '@/hooks/api/use-crm-item-types'

import { Combobox } from '@/components/pos/combobox'
import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

interface MenuFiltersProps {
  filters: {
    itemName: string
    itemGroup: string
    itemType: string
    allowTakeAway: string
    allowSelfOrder: string
    activeTab: string
  }
  itemGroups: string[]
  onFilterChange: (key: string, value: string) => void
  onClearFilters: () => void
}

export function MenuFilters({ filters, onFilterChange }: Omit<MenuFiltersProps, 'onClearFilters' | 'itemGroups'>) {
  const { options: itemTypeOptions, isLoading: isLoadingItemTypes } = useCrmItemTypeOptions()

  return (
    <div className='mb-4 space-y-4'>
      {/* Filter Tabs */}
      <div className='flex gap-2'>
        <Button
          variant={filters.activeTab === 'menu' ? 'default' : 'outline'}
          size='sm'
          onClick={() => onFilterChange('activeTab', 'menu')}
        >
          Danh sách món
        </Button>
        <Button
          variant={filters.activeTab === 'combo' ? 'default' : 'outline'}
          size='sm'
          onClick={() => onFilterChange('activeTab', 'combo')}
        >
          Danh sách combo
        </Button>
        <Button
          variant={filters.activeTab === 'special-combo' ? 'default' : 'outline'}
          size='sm'
          onClick={() => onFilterChange('activeTab', 'special-combo')}
        >
          Danh sách combo tùy chỉnh
        </Button>
      </div>

      <div className='grid grid-cols-1 gap-4 md:grid-cols-6'>
        <div>
          <label className='mb-1 block text-sm font-medium text-gray-700'>TÊN/MÃ MÓN</label>
          <Input
            placeholder='Tìm kiếm tên món hoặc mã món (VD: Cà phê hoặc ITEM-123)'
            value={filters.itemName}
            onChange={e => onFilterChange('itemName', e.target.value)}
          />
        </div>

        <div>
          <label className='mb-1 block text-sm font-medium text-gray-700'>NHÓM MÓN</label>
          <Combobox
            options={[
              { value: 'all', label: 'Tất cả' },
              ...itemTypeOptions.map(option => ({
                value: option.value,
                label: option.label
              }))
            ]}
            value={filters.itemGroup}
            onValueChange={value => onFilterChange('itemGroup', value || 'all')}
            placeholder={isLoadingItemTypes ? 'Đang tải...' : 'Chọn nhóm món'}
            searchPlaceholder='Tìm kiếm nhóm món...'
            emptyText='Không tìm thấy nhóm món'
            disabled={isLoadingItemTypes}
            className='w-full'
          />
        </div>

        <div>
          <label className='mb-1 block text-sm font-medium text-gray-700'>LOẠI MÓN</label>
          <Select value={filters.itemType} onValueChange={value => onFilterChange('itemType', value)}>
            <SelectTrigger className='w-full'>
              <SelectValue placeholder='Chọn loại món' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả</SelectItem>
              <SelectItem value='parent'>Món cha</SelectItem>
              <SelectItem value='child'>Món con</SelectItem>
              <SelectItem value='normal'>Món thường</SelectItem>
              <SelectItem value='eat_with'>Món ăn kèm</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className='mb-1 block text-sm font-medium text-gray-700'>BÁN TẠI CHỖ</label>
          <Select value={filters.allowTakeAway} onValueChange={value => onFilterChange('allowTakeAway', value)}>
            <SelectTrigger className='w-full'>
              <SelectValue placeholder='Chọn' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả</SelectItem>
              <SelectItem value='yes'>Có</SelectItem>
              <SelectItem value='no'>Không</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className='mb-1 block text-sm font-medium text-gray-700'>BÁN MANG ĐI</label>
          <Select value={filters.allowSelfOrder} onValueChange={value => onFilterChange('allowSelfOrder', value)}>
            <SelectTrigger className='w-full'>
              <SelectValue placeholder='Chọn' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>Tất cả</SelectItem>
              <SelectItem value='yes'>Có</SelectItem>
              <SelectItem value='no'>Không</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  )
}
