import React, { useMemo } from 'react'

import { format } from 'date-fns'

import { vi } from 'date-fns/locale'
import { CartesianGrid, Line, LineChart, XAxis, YAxis } from 'recharts'

import {
  ChartContainer,
  ChartTooltip,
  ChartLegend,
  ChartLegendContent,
  ChartTooltipContent
} from '@/components/ui/chart'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'

import { usePromotionContext } from '../context'

const formatLargeNumber = (num: number): string => {
  if (num >= 1000000) return Math.round(num / 1000000) + 'M'
  if (num >= 1000) return Math.round(num / 1000) + 'K'
  return new Intl.NumberFormat('vi-VN').format(num)
}

export const PromotionChart: React.FC = () => {
  const { rawData, compareRawData, isLoading, isCompareLoading, dateRange, compareCities, compareStores } =
    usePromotionContext() as any

  const hasCompareData = compareCities?.length > 0 || compareStores?.length > 0

  // Early return for loading state to maintain hook order
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Doanh thu bao gồm: Phí ship, VAT</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex h-64 items-center justify-center'>
            <div className='text-muted-foreground'>Đang tải dữ liệu...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const isSameDay = (a: Date, b: Date) =>
    a.getFullYear() === b.getFullYear() && a.getMonth() === b.getMonth() && a.getDate() === b.getDate()

  const isSingleDayRange = useMemo(() => {
    const from = new Date(dateRange.from)
    const to = new Date(dateRange.to)
    from.setHours(0, 0, 0, 0)
    to.setHours(0, 0, 0, 0)
    return isSameDay(from, to)
  }, [dateRange.from, dateRange.to])

  const isTodayRange = useMemo(() => {
    if (!isSingleDayRange) return false
    const from = new Date(dateRange.from)
    const today = new Date()
    from.setHours(0, 0, 0, 0)
    today.setHours(0, 0, 0, 0)
    return isSameDay(from, today)
  }, [dateRange.from, isSingleDayRange])

  const chartSeries = useMemo(() => {
    if (!dateRange) return { data: [], series: [] as Array<{ key: string; name: string; color: string }> }

    const promotions = (rawData || []).map((p: { promotion_id: string; promotion_name?: string }) => ({
      id: String(p.promotion_id),
      name: String(p.promotion_name || p.promotion_id)
    }))

    const labelFromDate = (input: string) => {
      const d = new Date(input)
      if (!Number.isNaN(d.getTime())) return format(d, 'dd/MM', { locale: vi })
      const m = String(input).match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/)
      if (m) return `${m[3]}/${m[2]}`
      return String(input)
    }

    const mapPerPromotion = new Map<string, Map<string, number>>()
    ;(rawData || []).forEach((p: { promotion_id: string; list_data?: Array<any> }) => {
      const id = String(p.promotion_id)
      const dateMap = new Map<string, number>()
      ;(p.list_data || []).forEach((d: { date: string; revenue_net?: number; total_amount?: number }) => {
        const label = labelFromDate(String(d.date))
        const value = Number(d.revenue_net ?? d.total_amount ?? 0)
        dateMap.set(label, (dateMap.get(label) || 0) + value)
      })
      mapPerPromotion.set(id, dateMap)
    })

    const start = new Date(dateRange.from)
    const end = new Date(dateRange.to)
    start.setHours(0, 0, 0, 0)
    end.setHours(0, 0, 0, 0)

    const rows: Array<Record<string, any>> = []
    for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
      const name = format(dt, 'dd/MM', { locale: vi })
      const row: Record<string, any> = { name }
      promotions.forEach((p: { id: string }, idx: number) => {
        const key = `p_${p.id}_${idx}`
        const val = mapPerPromotion.get(p.id)?.get(name) || 0
        row[key] = val
      })
      rows.push(row)
    }

    if (rows.length === 1 && !isSingleDayRange) {
      const prevDay = new Date(dateRange.from)
      prevDay.setDate(prevDay.getDate() - 1)
      rows.unshift({ ...rows[0], name: format(prevDay, 'dd/MM', { locale: vi }) })
    }

    const palette = (idx: number) => `var(--chart-${(idx % 12) + 1})`
    const series = promotions.map((p: { id: string; name: string }, idx: number) => ({
      key: `p_${p.id}_${idx}`,
      name: p.name,
      color: palette(idx)
    }))

    return { data: rows, series }
  }, [rawData, dateRange, isSingleDayRange])

  const compareChartSeries = useMemo(() => {
    if (!dateRange || !compareRawData)
      return { data: [], series: [] as Array<{ key: string; name: string; color: string }> }

    const promotions = (compareRawData || []).map((p: { promotion_id: string; promotion_name?: string }) => ({
      id: String(p.promotion_id),
      name: String(p.promotion_name || p.promotion_id)
    }))

    const labelFromDate = (input: string) => {
      const d = new Date(input)
      if (!Number.isNaN(d.getTime())) return format(d, 'dd/MM', { locale: vi })
      const m = String(input).match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/)
      if (m) return `${m[3]}/${m[2]}`
      return String(input)
    }

    const mapPerPromotion = new Map<string, Map<string, number>>()
    ;(compareRawData || []).forEach((p: { promotion_id: string; list_data?: Array<any> }) => {
      const id = String(p.promotion_id)
      const dateMap = new Map<string, number>()
      ;(p.list_data || []).forEach((d: { date: string; revenue_net?: number; total_amount?: number }) => {
        const label = labelFromDate(String(d.date))
        const value = Number(d.revenue_net ?? d.total_amount ?? 0)
        dateMap.set(label, (dateMap.get(label) || 0) + value)
      })
      mapPerPromotion.set(id, dateMap)
    })

    const start = new Date(dateRange.from)
    const end = new Date(dateRange.to)
    start.setHours(0, 0, 0, 0)
    end.setHours(0, 0, 0, 0)

    const rows: Array<Record<string, any>> = []
    for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
      const name = format(dt, 'dd/MM', { locale: vi })
      const row: Record<string, any> = { name }
      promotions.forEach((p: { id: string }, idx: number) => {
        const key = `p_${p.id}_${idx}`
        const val = mapPerPromotion.get(p.id)?.get(name) || 0
        row[key] = val
      })
      rows.push(row)
    }

    if (rows.length === 1 && !isSingleDayRange) {
      const prevDay = new Date(dateRange.from)
      prevDay.setDate(prevDay.getDate() - 1)
      rows.unshift({ ...rows[0], name: format(prevDay, 'dd/MM', { locale: vi }) })
    }

    const palette = (idx: number) => `var(--chart-${(idx % 12) + 1})`
    const series = promotions.map((p: { id: string; name: string }, idx: number) => ({
      key: `p_${p.id}_${idx}`,
      name: p.name,
      color: palette(idx)
    }))

    return { data: rows, series }
  }, [compareRawData, dateRange, isSingleDayRange])

  const isEmpty = useMemo(() => {
    const rows = chartSeries.data as Array<Record<string, any>>
    if (rows.length === 0) return true
    return rows.every(r => Object.keys(r).every(k => k === 'name' || (Number(r[k]) || 0) === 0))
  }, [chartSeries])
  const isCompareEmpty = useMemo(() => {
    const rows = compareChartSeries.data as Array<Record<string, any>>
    if (rows.length === 0) return true
    return rows.every(r => Object.keys(r).every(k => k === 'name' || (Number(r[k]) || 0) === 0))
  }, [compareChartSeries])

  const dataForRender = useMemo(() => {
    if (isEmpty) {
      return [
        { name: '01/01', revenue: 0, detail: { total_amount: 0, total_bill: 0, discount_amount: 0 } },
        { name: '02/01', revenue: 0, detail: { total_amount: 0, total_bill: 0, discount_amount: 0 } }
      ]
    }
    return chartSeries.data
  }, [isEmpty, chartSeries])

  const compareDataForRender = useMemo(() => {
    if (isCompareEmpty) {
      return [
        { name: '01/01', revenue: 0, detail: { total_amount: 0, total_bill: 0, discount_amount: 0 } },
        { name: '02/01', revenue: 0, detail: { total_amount: 0, total_bill: 0, discount_amount: 0 } }
      ]
    }
    return compareChartSeries.data
  }, [isCompareEmpty, compareChartSeries])

  const xTicks = useMemo(() => {
    const labels = dataForRender.map(d => d.name)
    if (labels.length <= 10) return labels
    const desiredCount = 8
    const step = Math.max(1, Math.ceil(labels.length / desiredCount))
    const ticks = labels.filter((_, idx) => idx % step === 0)
    if (ticks[ticks.length - 1] !== labels[labels.length - 1]) {
      ticks.push(labels[labels.length - 1])
    }
    return ticks
  }, [dataForRender])

  const compareXTicks = useMemo(() => {
    const labels = compareDataForRender.map(d => d.name)
    if (labels.length <= 10) return labels
    const desiredCount = 8
    const step = Math.max(1, Math.ceil(labels.length / desiredCount))
    const ticks = labels.filter((_, idx) => idx % step === 0)
    if (ticks[ticks.length - 1] !== labels[labels.length - 1]) {
      ticks.push(labels[labels.length - 1])
    }
    return ticks
  }, [compareDataForRender])

  const chartConfig = useMemo(() => {
    const cfg: Record<string, { label: string; color: string }> = {}
    chartSeries.series.forEach(
      (s: { key: string; name: string; color: string }) => (cfg[s.key] = { label: s.name, color: s.color })
    )
    return cfg
  }, [chartSeries])

  if (hasCompareData) {
    return (
      <div className='grid grid-cols-1 gap-6 lg:grid-cols-2'>
        <Card>
          <CardHeader>
            <CardTitle>Doanh thu bao gồm: Phí ship, VAT</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={chartConfig} className='h-[300px] w-full'>
              <LineChart accessibilityLayer data={dataForRender} margin={{ left: 12, right: 12 }}>
                <CartesianGrid vertical={false} />
                <XAxis dataKey='name' tickLine={false} axisLine={false} tickMargin={8} interval={0} ticks={xTicks} />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={formatLargeNumber}
                  domain={isEmpty ? [0, 5] : ([0, (dataMax: any) => Math.ceil(Number(dataMax) * 1.2)] as any)}
                  ticks={isEmpty ? [0, 1, 2, 3, 4, 5] : undefined}
                />
                <ChartTooltip cursor={false} content={<ChartTooltipContent indicator='line' />} />
                {chartSeries.series.map((s: { key: string }) => (
                  <Line
                    key={s.key}
                    dataKey={s.key}
                    type='linear'
                    stroke={isTodayRange ? 'transparent' : `var(--color-${s.key})`}
                    strokeWidth={2}
                    dot={
                      isTodayRange ? { r: 4, stroke: `var(--color-${s.key})`, fill: `var(--color-${s.key})` } : false
                    }
                  />
                ))}
                <ChartLegend verticalAlign='bottom' content={<ChartLegendContent />} />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Doanh thu bao gồm: Phí ship, VAT</CardTitle>
          </CardHeader>
          <CardContent>
            {isCompareLoading ? (
              <div className='flex h-[300px] items-center justify-center'>
                <div className='text-muted-foreground'>Đang tải dữ liệu so sánh...</div>
              </div>
            ) : (
              <ChartContainer config={chartConfig} className='h-[300px] w-full'>
                <LineChart accessibilityLayer data={compareDataForRender} margin={{ left: 12, right: 12 }}>
                  <CartesianGrid vertical={false} />
                  <XAxis
                    dataKey='name'
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    interval={0}
                    ticks={compareXTicks}
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    tickMargin={8}
                    tickFormatter={formatLargeNumber}
                    domain={isCompareEmpty ? [0, 5] : ([0, (dataMax: any) => Math.ceil(Number(dataMax) * 1.2)] as any)}
                    ticks={isCompareEmpty ? [0, 1, 2, 3, 4, 5] : undefined}
                  />
                  <ChartTooltip cursor={false} content={<ChartTooltipContent indicator='line' />} />
                  {chartSeries.series.map((s: { key: string }) => (
                    <Line
                      key={s.key}
                      dataKey={s.key}
                      type='linear'
                      stroke={isTodayRange ? 'transparent' : `var(--color-${s.key})`}
                      strokeWidth={2}
                      dot={
                        isTodayRange ? { r: 4, stroke: `var(--color-${s.key})`, fill: `var(--color-${s.key})` } : false
                      }
                    />
                  ))}
                  <ChartLegend verticalAlign='bottom' content={<ChartLegendContent />} />
                </LineChart>
              </ChartContainer>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Doanh thu bao gồm: Phí ship, VAT</CardTitle>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className='h-[300px] w-full'>
          <LineChart accessibilityLayer data={dataForRender} margin={{ left: 12, right: 12 }}>
            <CartesianGrid vertical={false} />
            <XAxis dataKey='name' tickLine={false} axisLine={false} tickMargin={8} interval={0} ticks={xTicks} />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={formatLargeNumber}
              domain={isEmpty ? [0, 5] : ([0, (dataMax: any) => Math.ceil(Number(dataMax) * 1.2)] as any)}
              ticks={isEmpty ? [0, 1, 2, 3, 4, 5] : undefined}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent indicator='line' />} />
            {chartSeries.series.map((s: { key: string }) => (
              <Line
                key={s.key}
                dataKey={s.key}
                type='linear'
                stroke={isTodayRange ? 'transparent' : `var(--color-${s.key})`}
                strokeWidth={2}
                dot={isTodayRange ? { r: 4, stroke: `var(--color-${s.key})`, fill: `var(--color-${s.key})` } : false}
              />
            ))}
            <ChartLegend verticalAlign='bottom' content={<ChartLegendContent />} />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
