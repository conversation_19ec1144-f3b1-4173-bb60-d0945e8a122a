import { useState, useMemo, useEffect, useCallback } from 'react'
import { toast } from 'sonner'
import { GripVertical } from 'lucide-react'
import { useQueryClient } from '@tanstack/react-query'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'

import { PosModal } from '@/components/pos'

import { useTablesData } from '@/hooks/api/use-tables'
import { useAreasData } from '@/hooks/api/use-areas'
import { useBulkUpdateTables } from '@/hooks/api/use-tables'
import { useCurrentCompany, useCurrentBrand } from '@/stores/posStore'
import { type Area } from '@/lib/api'
import { apiClient } from '@/lib/api/pos/pos-api'
import { QUERY_KEYS } from '@/constants/query-keys'

import type { Table } from '@/lib/tables-api'

interface Store {
  id: string
  store_name: string
  active: number
}

interface ArrangeTablesModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCancel: () => void
  onSuccess: () => void
}

export function ArrangeTablesModal({
  open,
  onOpenChange,
  onCancel,
  onSuccess
}: ArrangeTablesModalProps) {
  const [selectedStoreId, setSelectedStoreId] = useState<string>('')
  const [selectedAreaId, setSelectedAreaId] = useState<string>('')
  const [arrangedTables, setArrangedTables] = useState<Table[]>([])
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)
  const [sortedAreas, setSortedAreas] = useState<Area[]>([])
  const [draggedAreaIndex, setDraggedAreaIndex] = useState<number | null>(null)

  const queryClient = useQueryClient()
  const { company } = useCurrentCompany()
  const { selectedBrand } = useCurrentBrand()
  const bulkUpdateTablesMutation = useBulkUpdateTables()
  const stores = useMemo(() => {
    try {
      const posStoresData = localStorage.getItem('pos_stores_data')
      if (posStoresData) {
        const storesData = JSON.parse(posStoresData)
        return Array.isArray(storesData)
          ? storesData.filter((store: Store) => store.active === 1)
          : []
      }
      return []
    } catch (error) {
      return []
    }
  }, [])
  const { data: areas = [] } = useAreasData({
    storeUid: selectedStoreId,
    page: 1,
    results_per_page: 15000
  })
  const { data: allTables = [] } = useTablesData({
    storeUid: selectedStoreId,
    enabled: open && !!selectedStoreId
  })
  useEffect(() => {
    if (open && stores.length > 0 && !selectedStoreId) {
      setSelectedStoreId(stores[0].id)
    }
  }, [open, stores.length, selectedStoreId])

  useEffect(() => {
    if (areas.length > 0) {
      setSortedAreas([...areas].sort((a, b) => a.sort - b.sort))
    } else {
      setSortedAreas([])
    }
  }, [areas.length, areas.map(a => a.id).join(',')])

  useEffect(() => {
    if (selectedAreaId) {
      const filteredTables = allTables
        .filter(table => table.area_uid === selectedAreaId)
        .sort((a, b) => a.sort - b.sort)
      setArrangedTables(filteredTables)
    } else {
      setArrangedTables([])
    }
  }, [selectedAreaId, allTables.length, allTables.map(t => t.id).join(',')])

  useEffect(() => {
    if (!open) {
      const timeoutId = setTimeout(() => {
        setSelectedStoreId('')
        setSelectedAreaId('')
        setArrangedTables([])
        setSortedAreas([])
        setDraggedAreaIndex(null)
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [open])

  const handleStoreChange = useCallback((storeId: string) => {
    setSelectedStoreId(storeId)
    setSelectedAreaId('')
    setArrangedTables([])
  }, [])

  const handleAreaChange = useCallback((areaId: string) => {
    setSelectedAreaId(areaId)
  }, [])

  const handleAreaDragStart = useCallback((e: React.DragEvent, index: number) => {
    setDraggedAreaIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', index.toString())
  }, [])

  const handleAreaDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }, [])

  const handleAreaDrop = useCallback((e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()

    if (draggedAreaIndex === null || draggedAreaIndex === dropIndex) {
      setDraggedAreaIndex(null)
      return
    }

    setSortedAreas(prevAreas => {
      const items = Array.from(prevAreas)
      const [reorderedItem] = items.splice(draggedAreaIndex, 1)
      items.splice(dropIndex, 0, reorderedItem)

      return items.map((area, index) => ({
        ...area,
        sort: index
      }))
    })
    setDraggedAreaIndex(null)
  }, [draggedAreaIndex])

  const handleAreaDragEnd = useCallback(() => {
    setDraggedAreaIndex(null)
  }, [])

  const handleDragStart = useCallback((e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', index.toString())
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }, [])

  const handleDrop = useCallback((e: React.DragEvent, dropIndex: number) => {
    e.preventDefault()

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null)
      return
    }

    setArrangedTables(prevTables => {
      const items = Array.from(prevTables)
      const [reorderedItem] = items.splice(draggedIndex, 1)
      items.splice(dropIndex, 0, reorderedItem)

      return items.map((table, index) => ({
        ...table,
        sort: index + 1
      }))
    })
    setDraggedIndex(null)
  }, [draggedIndex])

  const handleDragEnd = useCallback(() => {
    setDraggedIndex(null)
  }, [])

  const handleSave = async () => {
    if (!selectedStoreId) {
      toast.error('Vui lòng chọn cửa hàng')
      return
    }

    const companyUid = company?.id || ''
    const brandUid = selectedBrand?.id || ''

    if (!companyUid || !brandUid) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    try {
      if (sortedAreas.length > 0) {
        const areasUpdateData = sortedAreas.map(area => ({
          id: area.id,
          sort: area.sort,
          company_uid: companyUid,
          brand_uid: brandUid,
          store_uid: selectedStoreId
        }))

        console.log('Sending areas update data:', areasUpdateData)
        const response = await apiClient.post('/pos/v1/area', areasUpdateData)
        console.log('Areas update response:', response)

        if (!response || response.status !== 200) {
          throw new Error('API response không thành công')
        }
      }

      if (selectedAreaId && arrangedTables.length > 0) {
        await bulkUpdateTablesMutation.mutateAsync({
          storeUid: selectedStoreId,
          tables: arrangedTables
        })
      }

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.AREAS_LIST]
        }),
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.TABLES_LIST]
        })
      ])

      await new Promise(resolve => setTimeout(resolve, 500))

      toast.success('Sắp xếp thành công!')
      onSuccess()
    } catch (error) {
      console.error('Error saving arrangement:', error)
      toast.error('Lỗi khi sắp xếp. Vui lòng thử lại.')
    }
  }

  const selectedArea = areas.find(area => area.id === selectedAreaId)

  return (
    <PosModal
      title='Sắp xếp bàn'
      open={open}
      onOpenChange={onOpenChange}
      onCancel={onCancel}
      onConfirm={handleSave}
      confirmText='Lưu'
      cancelText='Đóng'
      centerTitle={true}
      maxWidth='sm:max-w-6xl'
      isLoading={bulkUpdateTablesMutation.isPending}
      confirmDisabled={!selectedStoreId || sortedAreas.length === 0}
    >
      <div className='space-y-4 max-h-[70vh] overflow-hidden'>
        <div className='space-y-2'>
          <Select value={selectedStoreId} onValueChange={handleStoreChange}>
            <SelectTrigger className='w-full'>
              <SelectValue placeholder='Chọn điểm áp dụng' />
            </SelectTrigger>
            <SelectContent>
              {stores.map(store => (
                <SelectItem key={store.id} value={store.id}>
                  {store.store_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedStoreId && (
          <div className='flex gap-4 h-[500px]'>
            <div className='w-1/3 border rounded-lg overflow-hidden'>
              <div className='bg-gray-50 p-3 border-b'>
                <h4 className='font-medium text-sm'>Khu vực</h4>
              </div>
              <div className='overflow-y-auto h-full'>
                {sortedAreas.length === 0 ? (
                  <div className='p-4 text-center text-gray-500 text-sm'>
                    Vui lòng chọn cửa hàng
                  </div>
                ) : (
                  <div className='space-y-1 p-2'>
                    {sortedAreas.map((area, index) => {
                      const tablesCount = allTables.filter(t => t.area_uid === area.id).length
                      const isDragging = draggedAreaIndex === index
                      const isSelected = selectedAreaId === area.id

                      return (
                        <div
                          key={area.id}
                          draggable
                          onDragStart={(e) => handleAreaDragStart(e, index)}
                          onDragOver={handleAreaDragOver}
                          onDrop={(e) => handleAreaDrop(e, index)}
                          onDragEnd={handleAreaDragEnd}
                          onClick={() => handleAreaChange(area.id)}
                          className={`w-full text-left p-3 rounded-md text-sm transition-all duration-200 flex items-center justify-between cursor-move ${
                            isDragging
                              ? 'opacity-50 shadow-lg scale-105'
                              : isSelected
                                ? 'bg-blue-50 text-blue-700 border border-blue-200 shadow-md'
                                : 'hover:bg-gray-50 hover:shadow-md border border-transparent'
                          }`}
                        >
                          <span className='flex items-center gap-2'>
                            <GripVertical className='h-4 w-4 text-gray-400 flex-shrink-0' />
                            <span className='text-gray-400'>🏢</span>
                            <span className='truncate'>{area.area_name}</span>
                          </span>
                          <div className='flex items-center gap-2'>
                            {tablesCount > 0 && (
                              <span className='text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full'>
                                {tablesCount}
                              </span>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>

            <div className='flex-1 border rounded-lg overflow-hidden'>
              <div className='bg-gray-50 p-3 border-b'>
                <h4 className='font-medium text-sm'>
                  {selectedArea ? selectedArea.area_name : 'Chọn khu vực'}
                </h4>
              </div>
              <div className='p-4 overflow-y-auto h-full'>
                {!selectedAreaId ? (
                  <div className='flex items-center justify-center h-full text-gray-500 text-sm'>
                    Vui lòng chọn khu vực
                  </div>
                ) : arrangedTables.length === 0 ? (
                  <div className='flex items-center justify-center h-full text-gray-500 text-sm'>
                    Không có bàn trong khu vực
                  </div>
                ) : (
                  <div className='grid grid-cols-4 gap-4 p-4'>
                    {arrangedTables.map((table, index) => {
                      const tableColor = (table.extra_data as any)?.color || '#3B82F6'

                      const hexToRgb = (hex: string) => {
                        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
                        return result ? {
                          r: parseInt(result[1], 16),
                          g: parseInt(result[2], 16),
                          b: parseInt(result[3], 16)
                        } : { r: 59, g: 130, b: 246 } 
                      }

                      const rgb = hexToRgb(tableColor)

                      return (
                        <div
                          key={table.id}
                          draggable
                          onDragStart={(e) => handleDragStart(e, index)}
                          onDragOver={handleDragOver}
                          onDrop={(e) => handleDrop(e, index)}
                          onDragEnd={handleDragEnd}
                          className={`
                            relative cursor-move
                            ${draggedIndex === index
                              ? 'scale-105 opacity-70 z-10 rotate-2'
                              : ''
                            }
                          `}
                        >
                          <div className={`
                            relative bg-white border-2 rounded-xl shadow-md
                            overflow-hidden h-32 w-full
                            ${draggedIndex === index
                              ? 'shadow-blue-200'
                              : 'border-gray-200'
                            }
                          `}
                          style={{
                            borderColor: draggedIndex === index ? tableColor : undefined
                          }}>
                            {/* Accent bar with table color */}
                            <div
                              className='absolute top-0 left-0 w-full h-1'
                              style={{ backgroundColor: tableColor }}
                            ></div>

                            <div className='p-4 h-full flex flex-col justify-center'>
                              <div className='text-center'>
                                <h3 className='text-lg font-bold text-gray-800 mb-2'>
                                  {table.table_name}
                                </h3>
                                <div className='flex items-center justify-center gap-1 text-sm text-gray-500 mb-3'>
                                  <span
                                    className='w-2 h-2 rounded-full'
                                    style={{ backgroundColor: tableColor }}
                                  ></span>
                                  <span>Thứ tự: {index + 1}</span>
                                </div>

                                <div
                                  className='flex items-center justify-center gap-1.5 text-[10px] rounded-lg py-1.5 px-2'
                                  style={{
                                    color: tableColor,
                                    backgroundColor: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`
                                  }}
                                >
                                  <svg className='w-2.5 h-2.5' fill='currentColor' viewBox='0 0 24 24'>
                                    <path d='M7 7h10v3l4-4-4-4v3H5v6h2V7zm10 10H7v-3l-4 4 4 4v-3h12v-6h-2v4z'/>
                                  </svg>
                                  <span className='font-medium whitespace-nowrap'>Kéo để sắp xếp</span>
                                </div>
                              </div>
                            </div>


                          </div>

                          <div className={`
                            absolute -bottom-1 left-1/2 transform -translate-x-1/2
                            w-20 h-2 bg-gray-300 rounded-full opacity-20 blur-sm
                            ${draggedIndex === index ? 'scale-110 opacity-40' : ''}
                          `}></div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </PosModal>
  )
}
