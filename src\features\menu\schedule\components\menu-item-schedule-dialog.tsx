import React, { useState } from 'react'
import { X } from 'lucide-react'
import { useAuthStore } from '@/stores/authStore'
import { useItemsData } from '@/hooks/api'
import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'

import { useMenuItemForm } from '../hooks/use-menu-item-form'
import type { MenuItemFormData } from '../schemas/menu-item-form-schema'
import type { MenuItem } from '../types/menu-schedule-api'
import { MenuItemActionSelector } from './menu-item-action-selector'
import { MenuItemAdvancedFields } from './menu-item-advanced-fields'
import { MenuItemBasicFields } from './menu-item-basic-fields'
import { MenuItemCategoryFields } from './menu-item-category-fields'
import { MenuItemSelector } from './menu-item-selector'

interface MenuItemScheduleDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (items: MenuItem[]) => void
  cityUid?: string
  storeUid?: string
  selectedItem?: MenuItem | null
}

export function MenuItemScheduleDialog({
  open,
  onOpenChange,
  onConfirm,
  cityUid,
  storeUid,
  selectedItem
}: MenuItemScheduleDialogProps) {
  const [selectedAction, setSelectedAction] = useState('')
  const [selectedItemId, setSelectedItemId] = useState('')

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]
  const { form, createMenuItem } = useMenuItemForm({ selectedItem, open })

  const { data: items = [] } = useItemsData({
    params: {
      company_uid: company?.id,
      brand_uid: selectedBrand?.id,
      city_uid: cityUid,
      store_uid: storeUid
    },
    enabled: !!(company?.id && selectedBrand?.id && (cityUid || storeUid))
  })

  React.useEffect(() => {
    if (selectedItem) {
      setSelectedAction(selectedItem.action)
      setSelectedItemId(selectedItem.id)
    } else if (open) {
      setSelectedAction('')
      setSelectedItemId('')
    }
  }, [selectedItem, open])

  const handleConfirm = (data: MenuItemFormData) => {
    if (!selectedAction) return
    if (selectedAction === 'UPDATE' && !selectedItemId) return

    const item = createMenuItem(data, selectedAction, selectedItemId)
    onConfirm([item])

    form.reset()
    setSelectedAction('')
    setSelectedItemId('')
    onOpenChange(false)
  }

  const handleItemSelect = React.useCallback(
    (itemId: string) => {
      setSelectedItemId(itemId)
      const item = items.find(i => i.id === itemId)
      if (item) {
        const itemData = {
          item_name: item.item_name,
          item_id: item.item_id,
          ots_price: item.ots_price || 0,
          item_class_uid: item.item_class_uid || '',
          item_type_uid: item.item_type_uid || '',
          unit_uid: item.unit_uid || '',
          unit_secondary_uid: item.unit_secondary_uid || ''
        }

        Object.entries(itemData).forEach(([key, value]) => {
          form.setValue(key as keyof MenuItemFormData, value)
        })
      }
    },
    [items, form]
  )

  const showFormFields = React.useMemo(() => {
    const action = selectedItem?.action || selectedAction
    return action === 'CREATE' || action === 'UPDATE'
  }, [selectedItem?.action, selectedAction])

  if (!open) return null

  const handleClose = () => {
    onOpenChange(false)
    form.reset()
    setSelectedAction('')
    setSelectedItemId('')
  }

  const isFormValid = selectedAction !== ''

  return (
    <div className='container mx-auto px-4 py-8'>
      {/* Header */}
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='button'
            disabled={!isFormValid}
            className='min-w-[100px]'
            onClick={() => form.handleSubmit(handleConfirm)()}
          >
            Xác nhận
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>Quản lý món ăn</h1>
          <p className='text-muted-foreground'>Thêm, sửa hoặc xóa món ăn trong menu schedule</p>
        </div>
      </div>

      {/* Form Content */}
      <div className='mx-auto max-w-4xl'>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleConfirm)} className='space-y-6'>
            <div className='rounded-lg border bg-white p-6 shadow-sm'>
              <div className='space-y-6'>
                <div>
                  <h2 className='text-lg font-medium text-gray-900'>Thông tin cơ bản</h2>
                </div>

                {/* Thao tác */}
                <MenuItemActionSelector
                  selectedAction={selectedAction}
                  onActionChange={setSelectedAction}
                  selectedItem={selectedItem}
                />

                {selectedAction === 'UPDATE' ||
                  (selectedAction === 'DELETE' && !selectedItem && (
                    <MenuItemSelector
                      selectedItemId={selectedItemId}
                      onItemSelect={handleItemSelect}
                      selectedItem={selectedItem}
                      items={items}
                    />
                  ))}
              </div>
            </div>

            {showFormFields && (
              <div className='rounded-lg border bg-white p-6 shadow-sm'>
                <div className='space-y-6'>
                  <div>
                    <h2 className='text-lg font-medium text-gray-900'>Chi tiết món ăn</h2>
                  </div>
                  <MenuItemBasicFields form={form} />
                  <MenuItemCategoryFields form={form} cityUid={cityUid} storeUid={storeUid} />
                  <MenuItemAdvancedFields form={form} cityUid={cityUid} storeUid={storeUid} />
                </div>
              </div>
            )}
          </form>
        </Form>
      </div>
    </div>
  )
}
