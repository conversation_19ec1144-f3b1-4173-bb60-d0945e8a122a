// Reports Stores API Types

export interface ReportsStoresDataItem {
  date: string
  tran_date: number
  total_sales: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  revenue_net: number
  peo_count: number
  discount_extra_amount: number
  amount_discount_detail: number
  partner_marketing_amount: number
  voucher_amount: number
  vat_amount: number
  discount_vat_amount: number
  deduct_tax_amount: number
}

export interface ReportsStoresData {
  store_uid: string
  store_name: string
  total_sales: number
  revenue_gross: number
  discount_amount: number
  commission_amount: number
  revenue_net: number
  peo_count: number
  list_data: ReportsStoresDataItem[]
  discount_extra_amount: number
  amount_discount_detail: number
  partner_marketing_amount: number
  voucher_amount: number
  vat_amount: number
  discount_vat_amount: number
  deduct_tax_amount: number
}

export interface ReportsStoresResponse {
  data: ReportsStoresData[]
  message: string
  track_id: string
}

export interface GetReportsStoresParams {
  brand_uid: string
  company_uid: string
  list_store_uid: string
  start_date: number
  end_date: number
  store_open_at?: number
  limit?: number
}
