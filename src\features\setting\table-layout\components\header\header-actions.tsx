import React from 'react'

import { Loader2, Upload, Settings, Arrow<PERSON><PERSON>Down, Palette, ChevronDownIcon } from 'lucide-react'

import { Button, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui'

interface HeaderActionsProps {
  onCreateNew?: () => void
  onSaveLayout?: () => void
  isUpdating?: boolean
  onImportTables?: () => void
  onEditTableInfo?: () => void
  onSortTables?: () => void
  onCustomizeTable?: () => void
}

export const HeaderActions: React.FC<HeaderActionsProps> = ({
  onCreateNew,
  onSaveLayout,
  isUpdating = false,
  onImportTables,
  onEditTableInfo,
  onSortTables,
  onCustomizeTable
}) => {
  return (
    <div className='flex items-center gap-2'>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant='outline' size='sm'>
            Tiện ích
            <ChevronDownIcon className='ml-2 h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-56'>
          <DropdownMenuItem onClick={onImportTables}>
            <Upload className='mr-2 h-4 w-4' />
            Thêm bàn từ file
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onEditTableInfo}>
            <Settings className='mr-2 h-4 w-4' />
            Sửa thông tin bàn
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onSortTables}>
            <ArrowUpDown className='mr-2 h-4 w-4' />
            Sắp xếp bàn
          </DropdownMenuItem>
          <DropdownMenuItem onClick={onCustomizeTable}>
            <Palette className='mr-2 h-4 w-4' />
            Cấu hình bàn
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Button onClick={onCreateNew} className='bg-blue-600 hover:bg-blue-700' size='sm'>
        Tạo bàn mới
      </Button>
      <Button onClick={onSaveLayout} className='bg-blue-600 hover:bg-blue-700' size='sm' disabled={isUpdating}>
        {isUpdating && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
        Lưu
      </Button>
    </div>
  )
}
