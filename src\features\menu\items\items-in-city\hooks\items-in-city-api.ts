import * as XLSX from 'xlsx'

import { api } from '@/lib/api/pos/pos-api'

import type { ItemsInCity } from '../data'

interface CityData {
  id: string
  city_name: string
}

/**
 * Get all city UIDs from localStorage for filtering
 */
const getAllCityUids = (): string[] => {
  try {
    const citiesData = localStorage.getItem('pos_cities_data')
    if (citiesData) {
      const cities: CityData[] = JSON.parse(citiesData)
      return cities.map(city => city.id)
    }
  } catch {}
  return []
}

/**
 * Get actual city GUID from city name or GUID
 */
const getActualCityUid = (cityUidOrName: string): string | null => {
  if (!cityUidOrName) return null

  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  if (guidRegex.test(cityUidOrName)) {
    return cityUidOrName
  }

  try {
    const citiesData = localStorage.getItem('pos_cities_data')
    if (citiesData) {
      const cities: CityData[] = JSON.parse(citiesData)
      const cityByName = cities.find(city => city.city_name === cityUidOrName)
      return cityByName?.id || null
    }
  } catch {}

  return null
}

// Type definitions
export interface ItemExtraData {
  id: string
  item_name: string
  description?: string
  ots_price: number
  ots_tax: number
  time_cooking: number
  item_id_barcode?: string
  is_eat_with: boolean
  is_featured: boolean
  item_class_uid: string
  item_type_uid: string
  city_uid: string
  item_id?: string
  unit_uid: string
  unit_secondary_uid?: string
  enable_edit_price: boolean
  is_print_label: boolean
  is_allow_discount: boolean
  is_virtual_item: boolean
  is_item_service: boolean
  is_buffet_item: boolean
  inqrFormula?: string
  is_service: boolean
  sort: number
}

export interface ItemCity {
  id: string
  city_name: string
}

export interface ItemInCity extends ItemExtraData {
  created_at: string | number
  updated_at: string | number
  active: boolean
  revision?: number
  cities?: ItemCity[]
  item_id_eat_with?: string
  customization_uid?: string
  extra_data?: {
    enable_edit_price?: boolean
    is_virtual_item?: boolean
    is_item_service?: boolean
    is_buffet_item?: boolean
    [key: string]: unknown
  }
}

export interface ItemsInCityApiResponse {
  data: ItemInCity[]
  total_item: number
  track_id: string
}

export interface GetItemsInCityParams {
  company_uid: string
  brand_uid: string
  page?: number
  city_uid?: string
  list_city_uid?: string
  item_type_uid?: string
  time_sale_date_week?: string
  active?: number
  reverse?: number
  search?: string
  limit?: number
  skip_limit?: boolean
}

export interface DeleteItemInCityParams {
  company_uid: string
  brand_uid: string
  id: string
}

export interface DeleteMultipleItemsInCityParams {
  company_uid: string
  brand_uid: string
  list_item_uid: string[]
}

export interface CreateItemInCityRequest extends Omit<ItemExtraData, 'id'> {
  company_uid: string
  brand_uid: string
}

export interface UpdateItemInCityRequest extends ItemExtraData {
  company_uid: string
  brand_uid: string
}

export interface UpdateItemCustomizationRequest {
  menuItem: ItemsInCity
  customization_uid: string | null
  company_uid: string
  brand_uid: string
}

export interface UpdateItemBuffetConfigRequest {
  menuItem: ItemsInCity
  isBuffetItem: boolean
  excludeItems: string[]
}

export interface GetItemByListIdParams {
  company_uid: string
  brand_uid: string
  list_item_id: string
}

export interface GetItemByIdParams {
  id: string
  company_uid?: string
  brand_uid?: string
}

export interface UpdateItemStatusRequest {
  id: string
  active: number
  company_uid: string
  brand_uid: string
}

export interface DownloadTemplateParams {
  company_uid: string
  brand_uid: string
  city_uid?: string
  item_type_uid?: string
  active?: string
}

const itemsInCityCache = new Map<string, { data: ItemsInCityApiResponse; timestamp: number }>()
const pendingRequests = new Map<string, Promise<ItemsInCityApiResponse>>()
const CACHE_DURATION = 10 * 60 * 1000 // 10 minutes

interface ApiError {
  response?: {
    status?: number
    data?: {
      message?: string
    }
  }
}

function isApiError(error: unknown): error is ApiError {
  return typeof error === 'object' && error !== null && 'response' in error
}

interface ImportItemsRequest {
  company_uid: string
  brand_uid: string
  items: unknown[]
}

// Helper function to create Excel blob
function createItemsExcelBlob(data: unknown[]): Blob {
  const worksheet = XLSX.utils.json_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Items')
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  return new Blob([excelBuffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })
}

export const itemsInCityApiService = {
  /**
   * Get items in city data with request deduplication and caching
   */
  getItemsInCity: async (params: GetItemsInCityParams): Promise<ItemsInCityApiResponse> => {
    const actualCityUid =
      params.city_uid && params.city_uid !== 'all' ? getActualCityUid(params.city_uid) : params.city_uid

    const activeValue = params.active !== undefined ? params.active.toString() : 'undefined'
    const skipLimitValue = params.skip_limit ? 'true' : 'false'
    const requestKey = `${params.company_uid}-${params.brand_uid}-${params.page || 1}-${actualCityUid || 'all'}-${params.list_city_uid || 'all'}-${params.item_type_uid || 'all'}-${params.time_sale_date_week || ''}-${activeValue}-${params.reverse || 0}-${params.search || ''}-${params.limit || 50}-${skipLimitValue}`

    const cached = itemsInCityCache.get(requestKey)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }

    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      return existingRequest
    }

    const requestPromise = (async () => {
      try {
        const queryParams = new URLSearchParams()
        queryParams.append('company_uid', params.company_uid)
        queryParams.append('brand_uid', params.brand_uid)

        if (params.page) {
          queryParams.append('page', params.page.toString())
        }

        if (params.item_type_uid) {
          queryParams.append('item_type_uid', params.item_type_uid)
        }

        if (params.list_city_uid) {
          queryParams.append('list_city_uid', params.list_city_uid)
        } else if (params.city_uid && params.city_uid !== 'all') {
          const actualCityUid = getActualCityUid(params.city_uid)
          if (actualCityUid) {
            queryParams.append('city_uid', actualCityUid)
          }
        } else {
          const allCityUids = getAllCityUids()
          if (allCityUids.length > 0) {
            queryParams.append('list_city_uid', allCityUids.join(','))
          }
        }

        if (params.time_sale_date_week) {
          queryParams.append('time_sale_date_week', params.time_sale_date_week)
        }

        if (params.reverse !== undefined) {
          queryParams.append('reverse', params.reverse.toString())
        }

        if (params.search) {
          queryParams.append('search', params.search)
        }

        if (params.active !== undefined) {
          queryParams.append('active', params.active.toString())
        }

        if (params.limit) {
          queryParams.append('limit', params.limit.toString())
        }

        if (params.skip_limit) {
          queryParams.append('skip_limit', 'true')
        }

        const response = await api.get(`/mdata/v1/items?${queryParams.toString()}`)

        if (!response.data || typeof response.data !== 'object') {
          throw new Error('Invalid response format from items in city API')
        }

        const result = response.data as ItemsInCityApiResponse

        itemsInCityCache.set(requestKey, {
          data: result,
          timestamp: Date.now()
        })

        return result
      } finally {
        pendingRequests.delete(requestKey)
      }
    })()

    pendingRequests.set(requestKey, requestPromise)
    return requestPromise
  },

  /**
   * Delete item in city
   */
  deleteItemInCity: async (params: DeleteItemInCityParams): Promise<void> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('company_uid', params.company_uid)
      queryParams.append('brand_uid', params.brand_uid)
      queryParams.append('id', params.id)

      await api.delete(`/mdata/v1/item?${queryParams.toString()}`)

      itemsInCityCache.clear()
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  /**
   * Delete multiple items in city
   */
  deleteMultipleItemsInCity: async (params: DeleteMultipleItemsInCityParams): Promise<void> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('company_uid', params.company_uid)
      queryParams.append('brand_uid', params.brand_uid)
      queryParams.append('list_item_uid', params.list_item_uid.join(','))

      await api.delete(`/mdata/v1/items?${queryParams.toString()}`)

      itemsInCityCache.clear()
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Items not found.')
      }
      throw error
    }
  },

  /**
   * Download template for items in city
   */
  downloadTemplate: async (params: DownloadTemplateParams): Promise<Blob> => {
    // Convert city name to GUID if needed
    const actualCityUid = params.city_uid && params.city_uid !== 'all' ? getActualCityUid(params.city_uid) : null

    const queryParams = new URLSearchParams({
      skip_limit: 'true',
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      ...(actualCityUid && { city_uid: actualCityUid }),
      ...(params.item_type_uid &&
        params.item_type_uid !== 'all' && {
          item_type_uid: params.item_type_uid
        }),
      ...(params.active && params.active !== 'all' && { active: params.active })
    })

    const response = await api.get(`/mdata/v1/items?${queryParams}`)
    const data = Array.isArray(response.data?.data) ? response.data.data : []
    return createItemsExcelBlob(data)
  },

  /**
   * Create a new item in city
   */
  createItemInCity: async (data: CreateItemInCityRequest): Promise<unknown> => {
    try {
      const response = await api.post('/mdata/v1/item', data)

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Update an existing item in city
   */
  updateItemInCity: async (data: UpdateItemInCityRequest): Promise<unknown> => {
    try {
      const response = await api.put('/mdata/v1/item', data, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Update only customization_uid for an item
   */
  updateItemCustomization: async (data: UpdateItemCustomizationRequest): Promise<unknown> => {
    try {
      const currentItem = await itemsInCityApiService.getItemById({ id: data.menuItem.id })
      const updateData = {
        ...(currentItem.data as Record<string, unknown>),
        customization_uid: data.customization_uid,
        company_uid: data.company_uid,
        brand_uid: data.brand_uid
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error) {
      throw new Error('Không thể cập nhật customization. Vui lòng thử lại.')
    }
  },

  /**
   * Update buffet configuration for an item
   */
  updateItemBuffetConfig: async (data: UpdateItemBuffetConfigRequest): Promise<unknown> => {
    try {
      const posUserData = localStorage.getItem('pos_user_data')
      const posBrandData = localStorage.getItem('pos_brands_data')
      let companyUid = ''
      let brandUid = ''

      if (posUserData) {
        try {
          const userData = JSON.parse(posUserData)
          companyUid = userData.company_uid || ''
        } catch {}
      }

      if (posBrandData) {
        try {
          const brandData = JSON.parse(posBrandData)
          if (Array.isArray(brandData) && brandData.length > 0) {
            brandUid = brandData[0].id || ''
          }
        } catch {}
      }

      const currentItem = await itemsInCityApiService.getItemById({
        id: data.menuItem.id,
        company_uid: companyUid,
        brand_uid: brandUid
      })

      const currentExtraData = (currentItem.data as any)?.extra_data || {}
      const updatedExtraData = {
        ...currentExtraData,
        is_buffet_item: data.isBuffetItem ? 1 : 0,
        exclude_items_buffet: data.excludeItems
      }

      const updateData = {
        ...(currentItem.data as Record<string, unknown>),
        extra_data: updatedExtraData
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: any) {
      throw new Error('Không thể cập nhật cấu hình buffet. Vui lòng thử lại.')
    }
  },

  /**
   * Get item by list_item_id for copy functionality
   */
  getItemByListId: async (params: GetItemByListIdParams): Promise<{ data: unknown }> => {
    try {
      const queryParams = new URLSearchParams({
        skip_limit: 'true',
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        is_all: 'true',
        list_item_id: params.list_item_id
      })

      const response = await api.get(`/mdata/v1/items?${queryParams}`, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      const items = Array.isArray(response.data?.data) ? response.data.data : []
      if (!items.length) {
        throw new Error('Item not found')
      }

      return { data: items[0] }
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  /**
   * Get item by ID
   */
  getItemById: async (params: GetItemByIdParams): Promise<{ data: unknown }> => {
    try {
      const queryParams = new URLSearchParams()
      queryParams.append('id', params.id)

      if (params.company_uid) {
        queryParams.append('company_uid', params.company_uid)
      }

      if (params.brand_uid) {
        queryParams.append('brand_uid', params.brand_uid)
      }

      const response = await api.get(`/mdata/v1/item?${queryParams.toString()}`)

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('Invalid response format from item detail API')
      }

      return response.data as { data: unknown }
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 404) {
        throw new Error('Item not found.')
      }
      throw error
    }
  },

  importItems: async (params: ImportItemsRequest) => {
    const response = await api.post('/mdata/v1/items/import', {
      company_uid: params.company_uid,
      brand_uid: params.brand_uid,
      items: params.items
    })
    return response.data
  },

  /**
   * Update item status (toggle active/inactive)
   */
  updateItemStatus: async (data: UpdateItemStatusRequest): Promise<unknown> => {
    try {
      const currentItem = await itemsInCityApiService.getItemById({ id: data.id })

      const updateData = {
        ...(currentItem.data as Record<string, unknown>),
        active: data.active,
        company_uid: data.company_uid,
        brand_uid: data.brand_uid
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      itemsInCityCache.clear()
      return response.data.data || response.data
    } catch (error: unknown) {
      if (isApiError(error) && error.response?.status === 400) {
        throw new Error(error.response.data?.message || 'Invalid data provided.')
      }
      throw error
    }
  },

  /**
   * Clear cache for items in city
   */
  clearCache: () => {
    itemsInCityCache.clear()
    pendingRequests.clear()
  },

  /**
   * Get cache stats
   */
  getCacheStats: () => ({
    cacheSize: itemsInCityCache.size,
    pendingRequests: pendingRequests.size
  })
}
