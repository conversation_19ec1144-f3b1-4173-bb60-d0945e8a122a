import { useState, useMemo, useEffect } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Input } from '@/components/ui/input'
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import { PosModal } from '@/components/pos'

import type { ItemsInStore, ItemsInStoreTable } from '../data'
import { useUpdateItemBuffetConfig, useItemsInStoreData, itemsInStoreApiService } from '../hooks'

interface BuffetConfigModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  menuItem: ItemsInStoreTable | null
  menuItems: ItemsInStoreTable[]
  isDetailMode?: boolean
}

// Helper functions to handle both ItemsInStore and ItemsInStoreTable types
const getItemName = (item: ItemsInStore | ItemsInStoreTable): string => {
  return 'name' in item ? item.name : item.item_name
}

const getItemId = (item: ItemsInStore | ItemsInStoreTable): string => {
  return 'originalData' in item ? item.originalData?.item_id || '' : item.item_id
}

export function BuffetConfigModal({
  open,
  onOpenChange,
  menuItem,
  menuItems,
  isDetailMode = false
}: BuffetConfigModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [isBuffetItem, setIsBuffetItem] = useState(false)
  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(false)

  const updateBuffetConfigMutation = useUpdateItemBuffetConfig()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  // Fetch all items (both active and inactive) for proper filtering logic
  const { data: additionalItemsData = [] } = useItemsInStoreData({
    params: {
      store_uid: menuItem?.originalData?.store_uid || 'all',
      skip_limit: true
      // Remove active: '1' to get all items
    },
    enabled: open && !isDetailMode && !!menuItem?.originalData?.store_uid
  })

  const additionalMenuItems: ItemsInStore[] = additionalItemsData

  const effectiveMenuItems = !isDetailMode && menuItems.length === 0 ? additionalMenuItems : menuItems

  useEffect(() => {
    const fetchItemData = async () => {
      if (open && menuItem && company?.id && selectedBrand?.id && effectiveMenuItems.length > 0) {
        try {
          const apiResponse = await itemsInStoreApiService.getItemInStoreDetail({
            item_uid: menuItem.id
          })

          const realItemData = apiResponse as any

          const currentIsBuffetItem = realItemData?.extra_data?.is_buffet_item === 1
          const currentExcludeItemIds = realItemData?.extra_data?.exclude_items_buffet || []

          const excludeItemUuids = new Set<string>()
          currentExcludeItemIds.forEach((itemId: string) => {
            const foundItem = effectiveMenuItems.find(item => {
              // Handle both ItemsInStore and ItemsInStoreTable types
              const itemIdToCheck = 'originalData' in item ? item.originalData?.item_id : item.item_id
              return itemIdToCheck === itemId
            })
            if (foundItem) {
              excludeItemUuids.add(foundItem.id)
            }
          })

          setIsBuffetItem(isDetailMode ? true : currentIsBuffetItem)
          setSelectedItems(excludeItemUuids)
        } catch (error) {
          console.error('Error fetching item data:', error)
          toast.error('Lỗi khi tải dữ liệu món ăn')
        }
      }
    }

    fetchItemData()
  }, [open, menuItem, company?.id, selectedBrand?.id, isDetailMode, effectiveMenuItems])

  const baseFilteredItems = useMemo(() => {
    if (!menuItem) return []

    // Check if current menu item is active - handle both types
    const isCurrentItemActive = 'isActive' in menuItem ? menuItem.isActive : (menuItem as any).active === 1

    if (isCurrentItemActive) {
      // If current item is active: show only other active items (exclude current item and deactive items)
      return effectiveMenuItems.filter(item => {
        const itemIsActive = 'isActive' in item ? item.isActive : (item as any).active === 1
        return itemIsActive && item.id !== menuItem.id
      })
    } else {
      // If current item is deactive: show all active items (don't exclude current item since it's already deactive)
      return effectiveMenuItems.filter(item => {
        const itemIsActive = 'isActive' in item ? item.isActive : (item as any).active === 1
        return itemIsActive
      })
    }
  }, [effectiveMenuItems, menuItem])

  const selectedItemsList = useMemo(() => {
    return baseFilteredItems.filter(item => selectedItems.has(item.id))
  }, [baseFilteredItems, selectedItems])

  const remainingItemsList = useMemo(() => {
    let items = baseFilteredItems.filter(item => !selectedItems.has(item.id))

    if (searchTerm) {
      items = items.filter(item => {
        const itemName = 'name' in item ? item.name : (item as any).item_name
        return itemName.toLowerCase().includes(searchTerm.toLowerCase())
      })
    }

    return items
  }, [baseFilteredItems, selectedItems, searchTerm])

  const handleItemToggle = (itemId: string) => {
    const newSelectedItems = new Set(selectedItems)
    if (newSelectedItems.has(itemId)) {
      newSelectedItems.delete(itemId)
    } else {
      newSelectedItems.add(itemId)
    }
    setSelectedItems(newSelectedItems)
  }

  const handleCancel = () => {
    onOpenChange(false)
    setSearchTerm('')
    setSelectedItems(new Set())
    setIsBuffetItem(false)
  }

  const handleConfirm = async () => {
    try {
      if (!menuItem) {
        toast.error('Không có dữ liệu món ăn')
        return
      }

      const excludeItemIds = Array.from(selectedItems)
        .map(uuid => {
          const foundItem = effectiveMenuItems.find(item => item.id === uuid)
          if (foundItem) {
            const itemId = 'originalData' in foundItem ? foundItem.originalData?.item_id : (foundItem as any).item_id
            return itemId
          }
          return uuid
        })
        .filter(Boolean)

      await updateBuffetConfigMutation.mutateAsync({
        item_uid: menuItem.id,
        buffet_config: {
          is_buffet_item: isBuffetItem,
          exclude_items_buffet: excludeItemIds
        }
      })

      onOpenChange(false)
    } catch (error) {}
  }

  return (
    <PosModal
      title={`Cấu hình buffet cho món ${menuItem ? getItemName(menuItem) : ''}`}
      centerTitle
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      confirmText='Lưu'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
    >
      <div className='space-y-4'>
        {!isDetailMode && (
          <div className='flex items-center space-x-2'>
            <Checkbox
              id='buffet-item-checkbox'
              checked={isBuffetItem}
              onCheckedChange={checked => setIsBuffetItem(!!checked)}
            />
            <label className='text-sm font-medium' htmlFor='buffet-item-checkbox'>
              Cấu hình món ăn là về buffet
            </label>
          </div>
        )}

        {(isBuffetItem || isDetailMode) && (
          <>
            {isDetailMode ? (
              <div className='space-y-4'>
                <h3 className='text-center text-lg font-medium'>Chọn danh sách món không đi kèm vé buffet</h3>
                <Input
                  placeholder='Tìm kiếm'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='w-full'
                />

                <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
                  <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                    <span className='font-medium'>Đã chọn {selectedItemsList.length}</span>
                    {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                  </CollapsibleTrigger>
                  <CollapsibleContent className='mt-2'>
                    <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                      {selectedItemsList.length === 0 ? (
                        <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>
                      ) : (
                        selectedItemsList.map(item => (
                          <label
                            key={item.id}
                            className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                          >
                            <Checkbox
                              checked={selectedItems.has(item.id)}
                              onCheckedChange={() => handleItemToggle(item.id)}
                            />
                            <div className='flex-1'>
                              <p className='text-sm font-medium'>
                                {getItemName(item)} - {getItemId(item)}
                              </p>
                            </div>
                          </label>
                        ))
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
                  <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                    <span className='font-medium'>Còn lại {remainingItemsList.length}</span>
                    {remainingSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                  </CollapsibleTrigger>
                  <CollapsibleContent className='mt-2'>
                    <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                      {remainingItemsList.length === 0 ? (
                        <p className='text-sm text-gray-500'>Không có món nào</p>
                      ) : (
                        remainingItemsList.map(item => (
                          <label
                            key={item.id}
                            className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                          >
                            <Checkbox
                              checked={selectedItems.has(item.id)}
                              onCheckedChange={() => handleItemToggle(item.id)}
                            />
                            <div className='flex-1'>
                              <p className='text-sm font-medium'>
                                {getItemName(item)} - {getItemId(item)}
                              </p>
                            </div>
                          </label>
                        ))
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            ) : (
              <Tabs defaultValue='exclude' className='w-full'>
                <TabsList className='grid w-full grid-cols-2'>
                  <TabsTrigger value='exclude'>Danh sách món không đi kèm về buffet</TabsTrigger>
                  <TabsTrigger value='upsize'>Danh sách về buffet được upsize</TabsTrigger>
                </TabsList>

                <TabsContent value='exclude' className='space-y-4'>
                  <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
                    <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                      <span className='font-medium'>Đã chọn {selectedItemsList.length}</span>
                      {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                    </CollapsibleTrigger>
                    <CollapsibleContent className='mt-2'>
                      <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                        {selectedItemsList.length === 0 ? (
                          <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>
                        ) : (
                          selectedItemsList.map(item => (
                            <label
                              key={item.id}
                              className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                            >
                              <Checkbox
                                checked={selectedItems.has(item.id)}
                                onCheckedChange={() => handleItemToggle(item.id)}
                              />
                              <div className='flex-1'>
                                <p className='text-sm font-medium'>
                                  {getItemName(item)} - {getItemId(item)}
                                </p>
                              </div>
                            </label>
                          ))
                        )}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>

                  <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
                    <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                      <span className='font-medium'>Còn lại {remainingItemsList.length}</span>
                      {remainingSectionOpen ? (
                        <ChevronDown className='h-4 w-4' />
                      ) : (
                        <ChevronRight className='h-4 w-4' />
                      )}
                    </CollapsibleTrigger>
                    <CollapsibleContent className='mt-2'>
                      <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                        {remainingItemsList.length === 0 ? (
                          <p className='text-sm text-gray-500'>Không có món nào</p>
                        ) : (
                          remainingItemsList.map(item => (
                            <label
                              key={item.id}
                              className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                            >
                              <Checkbox
                                checked={selectedItems.has(item.id)}
                                onCheckedChange={() => handleItemToggle(item.id)}
                              />
                              <div className='flex-1'>
                                <p className='text-sm font-medium'>
                                  {getItemName(item)} - {getItemId(item)}
                                </p>
                              </div>
                            </label>
                          ))
                        )}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                </TabsContent>

                <TabsContent value='upsize' className='space-y-4'>
                  <div className='py-8 text-center text-gray-500'>
                    <p>Tính năng upsize sẽ được phát triển trong tương lai</p>
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </>
        )}
      </div>
    </PosModal>
  )
}
