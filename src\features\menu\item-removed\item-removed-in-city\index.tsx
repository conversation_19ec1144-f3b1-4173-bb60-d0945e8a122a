import { useState, useMemo } from 'react'

import { RemovedItem } from '@/types/item-removed'
import { toast } from 'sonner'

import { getErrorMessage } from '@/utils/error-utils'

import { useRemovedItemsData, useRestoreRemovedItem, useBulkRestoreRemovedItems, useCitiesData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ConfirmModal } from '@/components/pos'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import { removedItemColumns, RemovedItemDataTable, ActionBar } from './components'

export default function RemovedItemsInCityPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCityId, setSelectedCityId] = useState<string>('all')
  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<RemovedItem | null>(null)
  const [selectedItems, setSelectedItems] = useState<RemovedItem[]>([])
  const [clearSelection, setClearSelection] = useState(false)

  const restoreItemMutation = useRestoreRemovedItem()
  const bulkRestoreItemsMutation = useBulkRestoreRemovedItems()
  const { data: cities = [] } = useCitiesData()

  const listCityUid = useMemo(() => {
    if (selectedCityId === 'all' || !selectedCityId) {
      return cities.map(city => city.id)
    }
    return [selectedCityId]
  }, [selectedCityId, cities])

  const {
    data: removedItems,
    isLoading,
    error
  } = useRemovedItemsData({
    searchTerm: searchTerm || undefined,
    listCityUid
  })

  const handleRestoreItem = (item: RemovedItem) => {
    setSelectedItem(item)
    setSelectedItems([])
    setConfirmModalOpen(true)
  }

  const handleConfirmRestore = async () => {
    try {
      if (selectedItems.length > 0) {
        const itemUids = selectedItems.map(item => item.id)
        await bulkRestoreItemsMutation.mutateAsync(itemUids)
        toast.success(`${selectedItems.length} món đã được khôi phục thành công!`)
        setSelectedItems([])
        setClearSelection(true)
        setTimeout(() => setClearSelection(false), 100)
      } else if (selectedItem) {
        await bulkRestoreItemsMutation.mutateAsync([selectedItem.id])
        toast.success(`Món "${selectedItem.item_name}" đã được khôi phục thành công!`)
        setSelectedItem(null)
      }

      setConfirmModalOpen(false)
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const getCityName = (cityUid: string) => {
    const city = cities.find(c => c.id === cityUid)
    return city?.city_name || cityUid
  }

  const handleBulkRestore = (items: RemovedItem[]) => {
    setSelectedItems(items)
    setSelectedItem(null)
    setConfirmModalOpen(true)
  }

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <ActionBar
            searchQuery={searchQuery}
            onSearchQueryChange={setSearchQuery}
            onSearchSubmit={setSearchTerm}
            selectedCityId={selectedCityId}
            onCityChange={setSelectedCityId}
            cities={cities}
            removedItems={removedItems || []}
          />

          {error && (
            <div className='py-8 text-center'>
              <p className='text-red-600'>{getErrorMessage(error)}</p>
            </div>
          )}
          {!error && isLoading && (
            <div className='py-8 text-center'>
              <p>Đang tải dữ liệu món đã xóa...</p>
            </div>
          )}
          {!error && !isLoading && (
            <RemovedItemDataTable
              columns={removedItemColumns}
              data={removedItems || []}
              onRestoreItem={handleRestoreItem}
              getCityName={getCityName}
              onBulkRestore={handleBulkRestore}
              clearSelection={clearSelection}
            />
          )}

          <ConfirmModal
            open={confirmModalOpen}
            onOpenChange={setConfirmModalOpen}
            content={
              selectedItems.length > 0
                ? `Bạn có muốn khôi phục ${selectedItems.length} món đã chọn?`
                : 'Bạn có muốn khôi phục?'
            }
            confirmText='Xác nhận'
            onConfirm={handleConfirmRestore}
            isLoading={restoreItemMutation.isPending || bulkRestoreItemsMutation.isPending}
          />
        </div>
      </Main>
    </>
  )
}
