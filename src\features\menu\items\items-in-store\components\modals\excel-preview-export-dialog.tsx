import { useEffect, useState } from 'react'

import { Trash2 } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { useItemTypesData, useItemClassesData, useUnitsData, useStoreData } from '@/hooks/api'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import { useBulkUpdateItemsInStore } from '../../hooks/use-update-item-in-store'
import type { BulkUpdateItemInStoreRequest } from '../../hooks/use-update-item-in-store'

export interface ImportItem {
  id: string
  item_id: string
  city_name: string
  store_name: string
  item_name: string
  ots_price: number
  active: number
  item_id_barcode: string
  is_eat_with: number
  no_update_quantity_toping: number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

interface ImportPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: ImportItem[]
  storeUid?: string
}

export function ImportPreviewDialog({ open, onOpenChange, data, storeUid }: ImportPreviewDialogProps) {
  const [importData, setImportData] = useState<ImportItem[]>(data)
  const [isProcessing, setIsProcessing] = useState(false)

  const { user, company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { bulkUpdateItemsInStore, isBulkUpdating } = useBulkUpdateItemsInStore()

  const { data: storeData } = useStoreData(storeUid || '', !!storeUid)

  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true,
    ...(storeUid && storeUid !== 'all' ? { store_uid: storeUid } : {})
  })
  const { data: itemClasses = [] } = useItemClassesData({
    skip_limit: true
  })
  const { data: units = [] } = useUnitsData()

  useEffect(() => {
    setImportData(data)
  }, [data])

  const handleRemoveItem = (index: number) => {
    setImportData(prev => prev.filter((_, i) => i !== index))
  }

  const handleConfirm = async () => {
    if (!storeUid || !company || !selectedBrand) {
      toast.error('Thiếu thông tin cần thiết để cập nhật')
      return
    }

    setIsProcessing(true)

    const transformedData: BulkUpdateItemInStoreRequest[] = importData.map(item => {
        // Find unit by unit_id (mã đơn vị)
        const unit = units.find(u => u.unit_id === item.unit_id)

        // Find item type by ID or name
        const itemType = itemTypes.find(it =>
          it.item_type_id === item.item_type_id ||
          it.item_type_name === item.item_type_name
        )

        // Find item class by ID or name
        const itemClass = itemClasses.find(ic =>
          ic.item_class_id === item.item_class_id ||
          ic.item_class_name === item.item_class_name
        )

        return {
          id: item.id,
          item_id: item.item_id,
          item_name: item.item_name,
          description: item.description || '',
          ots_price: item.ots_price || 0,
          ots_tax: (item.ots_tax || 0) / 100, 
          ta_price: item.ots_price || 0, 
          ta_tax: (item.ots_tax || 0) / 100,
          time_sale_hour_day: String(item.time_sale_hour_day || '0'),
          time_sale_date_week: String(item.time_sale_date_week || '0'),
          allow_take_away: 1, 
          is_eat_with: item.is_eat_with || 0,
          image_path: item.image_path || '',
          image_path_thumb: item.image_path ? `${item.image_path}?width=185` : '',
          item_color: '',
          list_order: item.list_order || 0,
          is_service: 0,
          is_material: 0,
          active: item.active || 1,
          user_id: '',
          is_foreign: 0,
          quantity_default: 0,
          price_change: item.price_change || 0,
          currency_type_id: '',
          point: 0,
          is_gift: 0,
          is_fc: 0,
          show_on_web: 0,
          show_price_on_web: 0,
          cost_price: 0,
          is_print_label: 0,
          quantity_limit: 0,
          is_kit: 0,
          time_cooking: (item.time_cooking || 0) * 60000, // Convert minutes to milliseconds
          item_id_barcode: item.item_id_barcode || '',
          process_index: 0,
          is_allow_discount: 0,
          quantity_per_day: 0,
          item_id_eat_with: '',
          is_parent: 0,
          is_sub: 0,
          item_id_mapping: String(item.sku || ''),
          effective_date: 0,
          expire_date: 0,
          sort: item.list_order || 1,
          extra_data: {
            formula_qrcode: item.inqr_formula || '',
            is_buffet_item: item.is_buffet_item || 0,
            up_size_buffet: [],
            is_item_service: item.is_item_service || 0,
            is_virtual_item: item.is_virtual_item || 0,
            price_by_source: [],
            enable_edit_price: item.price_change || 0,
            exclude_items_buffet: [],
            no_update_quantity_toping: item.no_update_quantity_toping || 0
          },
          revision: 0,
          unit_uid: unit?.id || '',
          unit_secondary_uid: null,
          item_type_uid: itemType?.id || '',
          item_class_uid: itemClass?.id || null,
          source_uid: null,
          brand_uid: selectedBrand.id,
          company_uid: company.id,
          customization_uid: '',
          is_fabi: 1,
          deleted: false,
          created_by: user?.email || '',
          updated_by: user?.email || '',
          deleted_by: null,
          created_at: Math.floor(Date.now() / 1000),
          updated_at: Math.floor(Date.now() / 1000),
          deleted_at: null,
          apply_with_store: 2, // Default value from cURL
          store_uid: storeUid,
          city_uid: storeData?.cityId || '' // Get city_uid from store data
        }
      })

    bulkUpdateItemsInStore(transformedData, {
      onSuccess: () => {
        setIsProcessing(false)
        onOpenChange(false)
      },
      onError: (error) => {
        console.error('Error updating items:', error)
        toast.error('Có lỗi xảy ra khi cập nhật món ăn')
        setIsProcessing(false)
      }
    })
  }

  const columns = [
    { key: 'item_id', label: 'Mã món', width: '120px' },
    { key: 'city_name', label: 'Thành phố', width: '120px' },
    { key: 'store_name', label: 'Cửa hàng', width: '140px' },
    { key: 'item_name', label: 'Tên', width: '200px' },
    { key: 'ots_price', label: 'Giá', width: '100px' },
    { key: 'active', label: 'Trạng thái', width: '100px' },
    { key: 'item_id_barcode', label: 'Mã barcode', width: '120px' },
    { key: 'is_eat_with', label: 'Món ăn kèm', width: '120px' },
    { key: 'no_update_quantity_toping', label: 'Không cập nhật số lượng', width: '180px' },
    { key: 'unit_name', label: 'Đơn vị', width: '100px' },
    { key: 'item_type_id', label: 'Nhóm', width: '120px' },
    { key: 'item_type_name', label: 'Tên nhóm', width: '150px' },
    { key: 'item_class_id', label: 'Loại món', width: '120px' },
    { key: 'item_class_name', label: 'Tên loại', width: '150px' },
    { key: 'description', label: 'Mô tả', width: '200px' },
    { key: 'sku', label: 'SKU', width: '100px' },
    { key: 'ots_tax', label: 'VAT (%)', width: '80px' },
    { key: 'time_cooking', label: 'Thời gian chế biến (phút)', width: '180px' },
    { key: 'price_change', label: 'Cho phép sửa giá khi bán', width: '180px' },
    { key: 'is_virtual_item', label: 'Cấu hình món ảo', width: '150px' },
    { key: 'is_item_service', label: 'Cấu hình món dịch vụ', width: '180px' },
    { key: 'is_buffet_item', label: 'Cấu hình món ăn là vé buffet', width: '200px' },
    { key: 'time_sale_hour_day', label: 'Giờ', width: '80px' },
    { key: 'time_sale_date_week', label: 'Ngày', width: '80px' },
    { key: 'list_order', label: 'Thứ tự', width: '80px' },
    { key: 'image_path', label: 'Hình ảnh', width: '120px' },
    { key: 'inqr_formula', label: 'Công thức inQR cho máy pha trà', width: '220px' }
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-7xl sm:max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Xuất, sửa thực đơn</DialogTitle>
        </DialogHeader>

        <div className='space-y-4 overflow-hidden'>
          <ScrollArea>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-12'></TableHead>
                  {columns.map(column => (
                    <TableHead key={column.key} style={{ width: column.width }}>
                      {column.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {importData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Button
                        variant='ghost'
                        size='icon'
                        onClick={() => handleRemoveItem(index)}
                        className='h-8 w-8 text-red-500 hover:text-red-700'
                      >
                        <Trash2 className='h-4 w-4' />
                      </Button>
                    </TableCell>
                    {columns.map(column => (
                      <TableCell key={column.key} style={{ width: column.width }}>
                        {column.key === 'ots_price' ? (
                          <span className='text-right'>
                            {item[column.key]?.toLocaleString('vi-VN')} ₫
                          </span>
                        ) : column.key === 'active' ? (
                          <span>{item[column.key]}</span>
                        ) : column.key === 'item_id' || column.key === 'item_id_barcode' ? (
                          <span className='font-mono text-sm'>{item[column.key]}</span>
                        ) : column.key === 'item_name' ? (
                          <span className='font-medium'>{item[column.key]}</span>
                        ) : column.key === 'is_eat_with' ||
                          column.key === 'no_update_quantity_toping' ||
                          column.key === 'price_change' ||
                          column.key === 'is_virtual_item' ||
                          column.key === 'is_item_service' ||
                          column.key === 'is_buffet_item' ? (
                          <span className='text-center'>{item[column.key]}</span>
                        ) : (
                          <span>{item[column.key] || ''}</span>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <ScrollBar orientation='horizontal' />
          </ScrollArea>

          <div className='flex items-center justify-between border-t pt-4'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              Đóng
            </Button>
            <Button onClick={handleConfirm} disabled={isProcessing || isBulkUpdating}>
              {isProcessing || isBulkUpdating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
