import type { UseFormReturn } from 'react-hook-form'

import type { ItemClass } from '@/types/item-class'
import type { CityData as City } from '@/types/item-removed'
import { Check, ChevronsUpDown, Trash2, Upload } from 'lucide-react'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'
import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'

import type { FormValues } from './items-in-city-mutate'

interface Props {
  form: UseFormReturn<FormValues>
  itemTypes: ItemType[]
  itemClasses: ItemClass[]
  units: Unit[]
  cities: City[]
  imageFile?: File | null
  onImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  imagePreview?: string | null
  onImageRemove?: () => void
}

export function ItemBasicInfo({
  form,
  itemTypes,
  itemClasses,
  units,
  cities,
  imageFile,
  onImageChange,
  imagePreview,
  onImageRemove
}: Props) {
  return (
    <div className='space-y-4'>
      <div className='grid grid-cols-1 gap-4'>
        <FormField
          control={form.control}
          name='item_name'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tên *</FormLabel>
              <FormControl>
                <Input placeholder='Nhập tên món' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='ots_price'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Giá *</FormLabel>
              <FormControl>
                <Input
                  placeholder='0'
                  value={field.value ? new Intl.NumberFormat('vi-VN').format(field.value) : ''}
                  onChange={e => {
                    const value = e.target.value.replace(/[^\d]/g, '')
                    field.onChange(value ? Number(value) : 0)
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='item_id'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mã món</FormLabel>
              <div className='flex items-center space-x-2'>
                <FormControl>
                  <Input
                    placeholder='Nếu để trống, hệ thống sẽ tự động tạo một mã món'
                    {...field}
                    disabled={!form.watch('enable_custom_item_id')}
                  />
                </FormControl>
                <FormField
                  control={form.control}
                  name='enable_custom_item_id'
                  render={({ field: checkboxField }) => (
                    <FormControl>
                      <Checkbox
                        checked={checkboxField.value}
                        onCheckedChange={checked => {
                          checkboxField.onChange(checked)

                          if (!checked) {
                            form.setValue('item_id', '')
                          }
                        }}
                        className='border-2 border-blue-500 data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500'
                      />
                    </FormControl>
                  )}
                />
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className='space-y-2'>
          <FormLabel>Ảnh món ăn</FormLabel>
          <div className='relative'>
            <div
              className='flex h-32 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 transition-colors hover:bg-gray-100'
              onClick={() => document.getElementById('image-upload')?.click()}
            >
              {imagePreview ? (
                <img src={imagePreview} alt='Preview' className='h-full w-full rounded-lg object-cover' />
              ) : (
                <div className='flex flex-col items-center justify-center pt-5 pb-6'>
                  <Upload className='mb-2 h-8 w-8 text-gray-400' />
                  <p className='mb-2 text-sm text-gray-500'>
                    <span className='font-semibold'>Chọn ảnh/hình</span>
                  </p>
                  <p className='text-xs text-gray-400'>PNG, JPG, GIF up to 10MB</p>
                </div>
              )}
            </div>
            {imagePreview && onImageRemove && (
              <button
                type='button'
                onClick={onImageRemove}
                className='absolute -top-2 -right-2 rounded-full bg-red-500 p-1 text-white transition-colors hover:bg-red-600'
              >
                <Trash2 className='h-3 w-3' />
              </button>
            )}
            <input id='image-upload' type='file' className='hidden' accept='image/*' onChange={onImageChange} />
          </div>
          {imageFile && <p className='text-sm text-gray-600'>Đã chọn: {imageFile.name}</p>}
        </div>
      </div>

      {/* Mã barcode */}
      <FormField
        control={form.control}
        name='item_id_barcode'
        render={({ field }) => (
          <FormItem>
            <FormLabel>Mã barcode</FormLabel>
            <FormControl>
              <Input
                placeholder='Nếu bạn sử dụng tính năng scan QR thì POS hay tạo mã barcode'
                maxLength={15}
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Món ăn kèm */}
      <FormField
        control={form.control}
        name='is_eat_with'
        render={({ field }) => (
          <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
            <FormControl>
              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
            <div className='space-y-1 leading-none'>
              <FormLabel>Món ăn kèm</FormLabel>
            </div>
          </FormItem>
        )}
      />

      {/* Không cập nhật số lượng món ăn kèm */}
      <FormField
        control={form.control}
        name='no_update_quantity_toping'
        render={({ field }) => (
          <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
            <FormControl>
              <Checkbox checked={field.value === 1} onCheckedChange={checked => field.onChange(checked ? 1 : 0)} />
            </FormControl>
            <div className='space-y-1 leading-none'>
              <FormLabel>Không cập nhật số lượng món ăn kèm</FormLabel>
            </div>
          </FormItem>
        )}
      />

      <div className='grid grid-cols-2 gap-4'>
        {/* Nhóm */}
        <FormField
          control={form.control}
          name='item_type_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nhóm</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button variant='outline' role='combobox' className={cn('w-full justify-between text-blue-500')}>
                      {field.value
                        ? itemTypes.find(itemType => itemType.id === field.value)?.item_type_name || 'Uncategory'
                        : 'Uncategory'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className='w-full p-0'>
                  <Command>
                    <CommandInput placeholder='Tìm kiếm...' />
                    <CommandList>
                      <CommandEmpty>Không tìm thấy nhóm.</CommandEmpty>
                      <CommandGroup>
                        {itemTypes.map(itemType => (
                          <CommandItem
                            value={itemType.id}
                            key={itemType.id}
                            onSelect={() => {
                              field.onChange(itemType.id)
                            }}
                            disabled={itemType.active === 0}
                          >
                            <Check
                              className={cn('mr-2 h-4 w-4', itemType.id === field.value ? 'opacity-100' : 'opacity-0')}
                            />
                            <div className='flex w-full items-center justify-between'>
                              <span className={cn(itemType.active === 0 ? 'text-muted-foreground' : 'text-blue-500')}>
                                {itemType.item_type_name}
                              </span>
                              {itemType.active === 0 && (
                                <span className='ml-2 rounded bg-red-100 px-2 py-1 text-xs text-red-700'>Deactive</span>
                              )}
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Loại món */}
        <FormField
          control={form.control}
          name='item_class_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Loại món</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button variant='outline' role='combobox' className={cn('w-full justify-between text-blue-500')}>
                      {field.value
                        ? itemClasses.find(itemClass => itemClass.id === field.value)?.item_class_name || 'None'
                        : 'None'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className='w-full p-0'>
                  <Command>
                    <CommandInput placeholder='Tìm kiếm loại món...' />
                    <CommandList>
                      <CommandEmpty>Không tìm thấy loại món.</CommandEmpty>
                      <CommandGroup>
                        {itemClasses.map(itemClass => (
                          <CommandItem
                            value={itemClass.id}
                            key={itemClass.id}
                            onSelect={() => {
                              field.onChange(itemClass.id)
                            }}
                            disabled={itemClass.active === 0}
                          >
                            <Check
                              className={cn('mr-2 h-4 w-4', itemClass.id === field.value ? 'opacity-100' : 'opacity-0')}
                            />
                            <div className='flex w-full items-center justify-between'>
                              <span className={cn(itemClass.active === 0 ? 'text-muted-foreground' : 'text-blue-500')}>
                                {itemClass.item_class_name}
                              </span>
                              {itemClass.active === 0 && (
                                <span className='ml-2 rounded bg-red-100 px-2 py-1 text-xs text-red-700'>Deactive</span>
                              )}
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className='grid grid-cols-1 gap-4'>
        {/* Mô tả */}
        <FormField
          control={form.control}
          name='description'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mô tả</FormLabel>
              <FormControl>
                <Textarea
                  placeholder='Nếu để trống thì tên món sẽ tự động làm mô tả món'
                  className='min-h-[80px]'
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Thành phố */}
        <FormField
          control={form.control}
          name='city_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Thành phố *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='Chọn thành phố' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {cities.map(city => (
                    <SelectItem key={city.id} value={city.id}>
                      {city.city_name || city.id}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* SKU */}
        <FormField
          control={form.control}
          name='item_id_mapping'
          render={({ field }) => (
            <FormItem>
              <FormLabel>SKU</FormLabel>
              <FormControl>
                <Input placeholder='Nhập mã SKU' maxLength={50} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className='grid grid-cols-1 gap-4'>
        {/* Đơn vị tính */}
        <FormField
          control={form.control}
          name='unit_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Đơn vị tính *</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button variant='outline' role='combobox' className={cn('w-full justify-between text-blue-500')}>
                      {field.value ? units.find(unit => unit.id === field.value)?.unit_name || 'Món' : 'Món'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className='w-full p-0'>
                  <Command>
                    <CommandInput placeholder='Tìm kiếm đơn vị tính...' />
                    <CommandList>
                      <CommandEmpty>Không tìm thấy đơn vị tính.</CommandEmpty>
                      <CommandGroup>
                        {units.map(unit => (
                          <CommandItem
                            value={unit.id}
                            key={unit.id}
                            onSelect={() => {
                              field.onChange(unit.id)
                            }}
                            className='text-blue-500'
                          >
                            <Check
                              className={cn('mr-2 h-4 w-4', unit.id === field.value ? 'opacity-100' : 'opacity-0')}
                            />
                            {unit.unit_name || unit.id}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Đơn vị tính thứ 2 */}
        <FormField
          control={form.control}
          name='unit_secondary_uid'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Đơn vị tính thứ 2</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button variant='outline' role='combobox' className={cn('w-full justify-between text-blue-500')}>
                      {field.value ? units.find(unit => unit.id === field.value)?.unit_name || 'None' : 'None'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className='w-full p-0'>
                  <Command>
                    <CommandInput placeholder='Tìm kiếm đơn vị tính...' />
                    <CommandList>
                      <CommandEmpty>Không tìm thấy đơn vị tính.</CommandEmpty>
                      <CommandGroup>
                        {units.map(unit => (
                          <CommandItem
                            value={unit.id}
                            key={unit.id}
                            onSelect={() => {
                              field.onChange(unit.id)
                            }}
                            className='text-blue-500'
                          >
                            <Check
                              className={cn('mr-2 h-4 w-4', unit.id === field.value ? 'opacity-100' : 'opacity-0')}
                            />
                            {unit.unit_name || unit.id}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className='grid grid-cols-1 gap-4'>
        {/* VAT */}
        <FormField
          control={form.control}
          name='ots_tax'
          render={({ field }) => (
            <FormItem>
              <FormLabel>VAT món ăn</FormLabel>
              <div className='flex items-center space-x-2'>
                <FormControl>
                  <Input
                    type='number'
                    placeholder='0'
                    className='w-full'
                    value={field.value ? Math.round(field.value * 100) : ''}
                    onChange={e => {
                      const value = e.target.value
                      field.onChange(value ? Number(value) / 100 : 0)
                    }}
                  />
                </FormControl>
                <span>%</span>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Thời gian chế biến */}
        <FormField
          control={form.control}
          name='time_cooking'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Thời gian chế biến (phút)</FormLabel>
              <FormControl>
                <Input
                  type='number'
                  placeholder='0'
                  value={field.value ? Math.round(field.value / 60000) : ''}
                  onChange={e => {
                    const minutes = e.target.value
                    field.onChange(minutes ? Number(minutes) * 60000 : 0)
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  )
}
