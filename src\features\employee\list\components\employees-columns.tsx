import { ColumnDef } from '@tanstack/react-table'

import type { User } from '@/types/user'

import { DataTableColumnHeader } from '@/components/data-table'
import { Badge } from '@/components/ui'

export const createEmployeesColumns = (
  onToggleStatus?: (user: User) => void,
  stores?: Array<{ id: string; store_name: string }>
): ColumnDef<User>[] => [
  {
    id: 'index',
    header: '#',
    cell: ({ row }) => {
      return <div className='w-8'>{row.index + 1}</div>
    },
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'full_name',
    header: 'Tên',
    cell: ({ row }) => {
      return <div className='font-medium'>{row.getValue('full_name')}</div>
    }
  },
  {
    accessorKey: 'role_name',
    header: 'Chức vụ',
    cell: ({ row }) => {
      return <div>{row.getValue('role_name')}</div>
    }
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => {
      return <div className='text-muted-foreground'>{row.getValue('email')}</div>
    }
  },
  {
    accessorKey: 'phone',
    header: 'Số điện thoại',
    cell: ({ row }) => {
      return <div>{row.getValue('phone')}</div>
    }
  },
  {
    id: 'stores',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Cửa hàng' />,
    cell: ({ row }) => {
      const user = row.original

      const userStores = user.stores || {}
      const storeIds: string[] = []

      Object.values(userStores).forEach(brandStores => {
        if (typeof brandStores === 'object' && brandStores !== null) {
          Object.values(brandStores).forEach(cityStores => {
            if (Array.isArray(cityStores)) {
              storeIds.push(...(cityStores as string[]))
            }
          })
        }
      })

      if (storeIds.length === 0) {
        return <div className='text-muted-foreground'></div>
      }

      const storeNames = storeIds
        .map(storeId => {
          const store = stores?.find(s => s.id === storeId)
          return store?.store_name || `Store ${storeId.slice(0, 8)}...`
        })
        .filter(Boolean)

      if (storeNames.length === 0) {
        return <div className='text-muted-foreground'>-</div>
      }

      if (storeNames.length === 1) {
        return <div className='text-sm'>{storeNames[0]}</div>
      }

      return (
        <div className='text-sm'>
          {storeNames[0]}
          <span className='text-muted-foreground ml-1 text-xs'>(+{storeNames.length - 1})</span>
        </div>
      )
    },
    enableSorting: false
  },
  {
    accessorKey: 'active',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Thao tác' />,
    cell: ({ row }) => {
      const user = row.original
      const isActive = row.getValue('active') === 1
      return (
        <div
          onClick={e => {
            e.stopPropagation()
            onToggleStatus?.(user)
          }}
          className='cursor-pointer'
        >
          <Badge
            variant={isActive ? 'default' : 'destructive'}
            className={isActive ? 'bg-green-500 hover:bg-green-600' : 'bg-red-500 hover:bg-red-600'}
          >
            {isActive ? 'Active' : 'Deactive'}
          </Badge>
        </div>
      )
    },
    enableSorting: false
  }
]
