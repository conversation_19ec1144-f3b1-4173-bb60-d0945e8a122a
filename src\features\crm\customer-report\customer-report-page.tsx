import { useState, useEffect, useMemo, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, ReferenceLine } from 'recharts'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'
import { DateRangePicker } from './components'
import {
  useCustomerReport
} from '@/hooks/crm'
import type {
  CustomerChartData,
  CustomerFrequencyChartData,
  StoreReportData,
  ReportByDate,
  ReportByPos
} from '@/types/api/customer-report'

const defaultCustomerData = {
  member_register_count: 0,
  total_eat_count: 0,
  first_time_count: 0,
  second_times_count: 0,
  three_and_above_times_count: 0,
  report_by_pos: [],
  report_by_date: []
}

export function CustomerReportPage() {
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [dateRange, setDateRange] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [activePreset, setActivePreset] = useState<string>('7days')

  const apiParams = useMemo(() => ({
    start_date: startDate ? format(startDate, 'yyyy-M-d') : '',
    end_date: endDate ? format(endDate, 'yyyy-M-d') : '',
    pos_parent: 'BRAND-953H'
  }), [startDate, endDate])

  const {
    data: allReportData,
    isLoading,
    error: reportError,
    updateParams
  } = useCustomerReport()

  useEffect(() => {
    if (apiParams.start_date && apiParams.end_date) {
      updateParams(apiParams)
    }
  }, [apiParams.start_date, apiParams.end_date, apiParams.pos_parent, updateParams])

  const hasError = reportError
  const customerData = allReportData.registrationReport || defaultCustomerData

  const formatDateHash = (dateHash: string) => {
    if (!dateHash || dateHash.length !== 8) return dateHash
    const year = dateHash.substring(0, 4)
    const month = dateHash.substring(4, 6)
    const day = dateHash.substring(6, 8)
    return `${year}/${month}/${day}`
  }

  const customerChartData: CustomerChartData[] = useMemo(() => {
    if (!customerData.report_by_date?.length) return []

    return customerData.report_by_date.map((item: ReportByDate) => ({
      name: formatDateHash(item.date_hash),
      value: item.number_member_register || 0,
      date_hash: item.date_hash
    }))
  }, [customerData.report_by_date])

  const customerFrequencyChartData: CustomerFrequencyChartData[] = useMemo(() => {
    if (!customerData.report_by_date?.length) return []

    return customerData.report_by_date.map((item: ReportByDate) => ({
      name: formatDateHash(item.date_hash),
      'Lượt khách hàng chi tiêu lần đầu': item.number_eat_first_time || 0,
      'Lượt khách hàng chi tiêu lần 2': item.number_eat_2nd_time || 0,
      'Lượt khách hàng chi tiêu từ 3 lần': item.number_eat_3_or_more_time || 0,
      date_hash: item.date_hash
    }))
  }, [customerData.report_by_date])

  const storeReportData: StoreReportData[] = useMemo(() => {
    if (!customerData.report_by_pos?.length) return []

    return customerData.report_by_pos.map((item: ReportByPos) => ({
      store_name: 'Tutimi-Bình Lợi',
      first_time_count: item.number_eat_first_time || 0,
      second_times_count: item.number_eat_2nd_time || 0,
      three_and_above_times_count: item.number_eat_3_or_more_time || 0
    }))
  }, [customerData.report_by_pos])

  const memberTierChartData: CustomerChartData[] = useMemo(() => {
    return []
  }, [])

  const formatDateRange = useCallback((start: Date, end: Date) => {
    return `${format(start, 'dd/MM/yyyy')} - ${format(end, 'dd/MM/yyyy')}`
  }, [])

  const handlePresetDate = useCallback((preset: string) => {
    const today = new Date()
    let start: Date
    let end: Date = endOfDay(today)

    switch (preset) {
      case 'today':
        start = startOfDay(today)
        break
      case 'yesterday':
        start = startOfDay(subDays(today, 1))
        end = endOfDay(subDays(today, 1))
        break
      case '7days':
        start = startOfDay(subDays(today, 6))
        break
      case '15days':
        start = startOfDay(subDays(today, 14))
        break
      case '30days':
        start = startOfDay(subDays(today, 29))
        break
      default:
        return
    }

    setStartDate(start)
    setEndDate(end)
    setDateRange(formatDateRange(start, end))
    setActivePreset(preset)
  }, [formatDateRange])

  useEffect(() => {
    handlePresetDate('7days')
  }, [handlePresetDate])

  const handleDateRangeChange = useCallback((value: string) => {
    setDateRange(value)
    setActivePreset('')

    const dateRangeRegex = /^(\d{2}\/\d{2}\/\d{4})\s*-\s*(\d{2}\/\d{2}\/\d{4})$/
    const match = value.match(dateRangeRegex)

    if (match) {
      try {
        const [, startStr, endStr] = match
        const [startDay, startMonth, startYear] = startStr.split('/').map(Number)
        const [endDay, endMonth, endYear] = endStr.split('/').map(Number)

        const start = new Date(startYear, startMonth - 1, startDay)
        const end = new Date(endYear, endMonth - 1, endDay)

        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          setStartDate(start)
          setEndDate(end)
        }
      } catch (error) {

      }
    }
  }, [])



  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Biến động khách hàng</h1>
        <p className="text-gray-600 mt-2">
          Theo dõi và phân tích sự thay đổi trong cơ sở khách hàng
        </p>
      </div>
      <div className="flex justify-end items-center gap-2 mb-6">
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'today' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('today')}
          >
            Hôm nay
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === 'yesterday' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('yesterday')}
          >
            Hôm qua
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '7days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('7days')}
          >
            7 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '15days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('15days')}
          >
            15 ngày trước
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={`text-xs ${activePreset === '30days' ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
            onClick={() => handlePresetDate('30days')}
          >
            30 ngày trước
          </Button>
        </div>
        <DateRangePicker
          startDate={startDate}
          endDate={endDate}
          onDateChange={(start, end) => {
            setStartDate(start)
            setEndDate(end)
            if (start && end) {
              setDateRange(formatDateRange(start, end))
              setActivePreset('')
            }
          }}
          dateRange={dateRange}
          onDateRangeChange={handleDateRangeChange}
        />
      </div>

      <div className="grid grid-cols-5 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            {isLoading ? (
              <div className="text-2xl font-bold text-gray-400">...</div>
            ) : hasError ? (
              <div className="text-2xl font-bold text-red-500">--</div>
            ) : (
              <div className="text-2xl font-bold text-gray-900">{customerData.member_register_count.toLocaleString()}</div>
            )}
            <div className="text-sm text-gray-600 mt-1">KHÁCH ĐĂNG KÝ</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            {isLoading ? (
              <div className="text-2xl font-bold text-gray-400">...</div>
            ) : hasError ? (
              <div className="text-2xl font-bold text-red-500">--</div>
            ) : (
              <div className="text-2xl font-bold text-gray-900">{customerData.total_eat_count.toLocaleString()}</div>
            )}
            <div className="text-sm text-gray-600 mt-1">LƯỢT CHI TIÊU</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            {isLoading ? (
              <div className="text-2xl font-bold text-gray-400">...</div>
            ) : hasError ? (
              <div className="text-2xl font-bold text-red-500">--</div>
            ) : (
              <div className="text-2xl font-bold text-gray-900">{customerData.first_time_count.toLocaleString()}</div>
            )}
            <div className="text-sm text-gray-600 mt-1">LƯỢT CHI TIÊU LẦN ĐẦU</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            {isLoading ? (
              <div className="text-2xl font-bold text-gray-400">...</div>
            ) : hasError ? (
              <div className="text-2xl font-bold text-red-500">--</div>
            ) : (
              <div className="text-2xl font-bold text-gray-900">{customerData.second_times_count.toLocaleString()}</div>
            )}
            <div className="text-sm text-gray-600 mt-1">LƯỢT CHI TIÊU LẦN 2</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            {isLoading ? (
              <div className="text-2xl font-bold text-gray-400">...</div>
            ) : hasError ? (
              <div className="text-2xl font-bold text-red-500">--</div>
            ) : (
              <div className="text-2xl font-bold text-gray-900">{customerData.three_and_above_times_count.toLocaleString()}</div>
            )}
            <div className="text-sm text-gray-600 mt-1">LƯỢT CHI TIÊU TỪ 3 LẦN</div>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex justify-center mb-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-sm text-gray-600">Số lượng khách đăng ký</span>
            </div>
          </div>
          <div className="h-[500px]">
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-gray-500">Đang tải dữ liệu...</div>
              </div>
            ) : hasError ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-red-500">Không thể tải dữ liệu</div>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={customerChartData} margin={{ top: 20, right: 30, left: 50, bottom: 50 }}>
                  <XAxis
                    dataKey="name"
                    axisLine={true}
                    tickLine={true}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    stroke="#d1d5db"
                  />
                  <YAxis
                    domain={[0, 1]}
                    ticks={[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]}
                    tickFormatter={(value) => value === 0 ? '0' : value.toFixed(1)}
                    axisLine={true}
                    tickLine={true}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    width={50}
                    stroke="#d1d5db"
                  />
                  {[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0].map(value => (
                    <ReferenceLine
                      key={value}
                      y={value}
                      stroke="#e5e7eb"
                      strokeDasharray="none"
                    />
                  ))}
                  {customerChartData.map((data, index) => (
                    <ReferenceLine
                      key={`vertical-${index}`}
                      x={data.name}
                      stroke="#d1d5db"
                      strokeWidth={1}
                    />
                  ))}
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#22c55e"
                    strokeWidth={2}
                    dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex justify-center gap-6 mb-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-sm text-gray-600">Lượt khách hàng chi tiêu lần đầu</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-sm text-gray-600">Lượt khách hàng chi tiêu lần 2</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              <span className="text-sm text-gray-600">Lượt khách hàng chi tiêu từ 3 lần</span>
            </div>
          </div>
          <div className="h-[500px]">
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-gray-500">Đang tải dữ liệu...</div>
              </div>
            ) : hasError ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-red-500">Không thể tải dữ liệu</div>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={customerFrequencyChartData} margin={{ top: 20, right: 30, left: 50, bottom: 50 }}>
                  <XAxis
                    dataKey="name"
                    axisLine={true}
                    tickLine={true}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    stroke="#d1d5db"
                  />
                  <YAxis
                    domain={[0, 1]}
                    ticks={[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]}
                    tickFormatter={(value) => value === 0 ? '0' : value.toFixed(1)}
                    axisLine={true}
                    tickLine={true}
                    tick={{ fontSize: 12, fill: '#6b7280' }}
                    width={50}
                    stroke="#d1d5db"
                  />
                  {[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0].map(value => (
                    <ReferenceLine
                      key={value}
                      y={value}
                      stroke="#e5e7eb"
                      strokeDasharray="none"
                    />
                  ))}
                  {customerFrequencyChartData.map((data, index) => (
                    <ReferenceLine
                      key={`vertical-${index}`}
                      x={data.name}
                      stroke="#d1d5db"
                      strokeWidth={1}
                    />
                  ))}
                  <Line
                    type="monotone"
                    dataKey="Lượt khách hàng chi tiêu lần đầu"
                    stroke="#22c55e"
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="Lượt khách hàng chi tiêu lần 2"
                    stroke="#3b82f6"
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="Lượt khách hàng chi tiêu từ 3 lần"
                    stroke="#f97316"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base font-medium text-gray-700 text-center">
              GIAO DỊCH THEO HẠNG THÀNH VIÊN
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[500px]">
              {isLoading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-gray-500">Đang tải dữ liệu...</div>
                </div>
              ) : hasError ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-red-500">Không thể tải dữ liệu</div>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={memberTierChartData} margin={{ top: 20, right: 30, left: 50, bottom: 50 }}>
                    <XAxis
                      dataKey="name"
                      axisLine={true}
                      tickLine={true}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      stroke="#d1d5db"
                    />
                    <YAxis
                      domain={[0, 1]}
                      ticks={[0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]}
                      tickFormatter={(value) => value === 0 ? '0' : value.toFixed(1)}
                      axisLine={true}
                      tickLine={true}
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      width={50}
                      stroke="#d1d5db"
                    />
                    {[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0].map(value => (
                      <ReferenceLine
                        key={value}
                        y={value}
                        stroke="#e5e7eb"
                        strokeDasharray="none"
                      />
                    ))}
                    {memberTierChartData.map((data, index) => (
                      <ReferenceLine
                        key={`vertical-${index}`}
                        x={data.name}
                        stroke="#d1d5db"
                        strokeWidth={1}
                      />
                    ))}
                    <Line
                      type="monotone"
                      dataKey="value"
                      stroke="#22c55e"
                      strokeWidth={2}
                      dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base font-medium text-gray-700">
              BIẾN ĐỘNG HẠNG THÀNH VIÊN
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-gray-50">
                    <th className="text-left p-3 text-sm font-medium text-gray-600">Từ</th>
                    <th className="text-left p-3 text-sm font-medium text-gray-600">Lên</th>
                    <th className="text-left p-3 text-sm font-medium text-gray-600">Số khách hàng</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td colSpan={3} className="text-center p-8 text-gray-500">
                      Chưa có dữ liệu
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-base font-medium text-gray-700">
            THỐNG KÊ LƯỢT CHI TIÊU THEO CỬA HÀNG
          </CardTitle>
          <Input
            placeholder="Tìm kiếm nhanh"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64 text-xs"
          />
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-3 font-medium text-gray-700">#</th>
                  <th className="text-left p-3 font-medium text-gray-700">Cửa hàng</th>
                  <th className="text-center p-3 font-medium text-gray-700">Lượt khách hàng chi tiêu lần đầu</th>
                  <th className="text-center p-3 font-medium text-gray-700">Lượt khách hàng chi tiêu lần 2</th>
                  <th className="text-center p-3 font-medium text-gray-700">Lượt khách hàng chi tiêu từ 3 lần</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={5} className="text-center p-8 text-gray-500">
                      Đang tải dữ liệu...
                    </td>
                  </tr>
                ) : hasError ? (
                  <tr>
                    <td colSpan={5} className="text-center p-8 text-red-500">
                      Không thể tải dữ liệu
                    </td>
                  </tr>
                ) : storeReportData.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="text-center p-8 text-gray-500">
                      Chưa có dữ liệu
                    </td>
                  </tr>
                ) : (
                  storeReportData
                    .filter(item =>
                      item.store_name?.toLowerCase().includes(searchTerm.toLowerCase())
                    )
                    .map((item, index) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="p-3 text-gray-900">{index + 1}</td>
                        <td className="p-3 text-gray-900">{item.store_name}</td>
                        <td className="p-3 text-center text-gray-900">{item.first_time_count.toLocaleString()}</td>
                        <td className="p-3 text-center text-gray-900">{item.second_times_count.toLocaleString()}</td>
                        <td className="p-3 text-center text-gray-900">{item.three_and_above_times_count.toLocaleString()}</td>
                      </tr>
                    ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
