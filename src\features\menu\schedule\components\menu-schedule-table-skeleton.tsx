import { Skeleton } from '@/components/ui/skeleton'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

export function MenuScheduleTableSkeleton() {
  return (
    <div className='space-y-4'>
      {/* Toolbar skeleton */}
      <div className='flex items-center justify-between'>
        <div className='flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:space-x-2'>
          <Skeleton className='h-8 w-[250px]' />
          <Skeleton className='h-8 w-[150px]' />
          <Skeleton className='h-8 w-[150px]' />
          <Skeleton className='h-8 w-[150px]' />
        </div>
        <Skeleton className='h-8 w-[70px]' />
      </div>

      {/* Table skeleton */}
      <div className='rounded-md border'>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className='w-[50px]'>
                <Skeleton className='h-4 w-6' />
              </TableHead>
              <TableHead>
                <Skeleton className='h-4 w-20' />
              </TableHead>
              <TableHead>
                <Skeleton className='h-4 w-20' />
              </TableHead>
              <TableHead>
                <Skeleton className='h-4 w-24' />
              </TableHead>
              <TableHead>
                <Skeleton className='h-4 w-20' />
              </TableHead>
              <TableHead>
                <Skeleton className='h-4 w-28' />
              </TableHead>
              <TableHead>
                <Skeleton className='h-4 w-16' />
              </TableHead>
              <TableHead>
                <Skeleton className='h-4 w-20' />
              </TableHead>
              <TableHead className='w-[70px]'>
                <Skeleton className='h-4 w-12' />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 10 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell>
                  <Skeleton className='h-4 w-6' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-4 w-20' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-4 w-20' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-4 w-24' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-4 w-20' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-4 w-32' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-4 w-16' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-6 w-16 rounded-full' />
                </TableCell>
                <TableCell>
                  <Skeleton className='h-8 w-8 rounded' />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination skeleton */}
      <div className='flex items-center justify-between px-2'>
        <Skeleton className='h-4 w-32' />
        <div className='flex items-center space-x-2'>
          <Skeleton className='h-8 w-8' />
          <Skeleton className='h-8 w-8' />
          <Skeleton className='h-8 w-8' />
          <Skeleton className='h-8 w-8' />
        </div>
      </div>
    </div>
  )
}
