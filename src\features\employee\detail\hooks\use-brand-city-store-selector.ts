import { useState, useMemo, useEffect } from 'react'

import { useHierarchicalData } from './use-hierarchical-data'

export function useBrandCityStoreSelector(selectedItems: string[]) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedBrands, setExpandedBrands] = useState<Set<string>>(new Set())
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())

  const { hierarchicalData, filterHierarchicalData } = useHierarchicalData()

  const [selectedStores, setSelectedStores] = useState<Set<string>>(() => {
    const storeIds = new Set<string>()
    selectedItems.forEach(item => {
      if (item.startsWith('store:')) {
        storeIds.add(item.replace('store:', ''))
      }
    })
    return storeIds
  })

  useEffect(() => {
    const storeIds = new Set<string>()
    selectedItems.forEach(item => {
      if (item.startsWith('store:')) {
        storeIds.add(item.replace('store:', ''))
      }
    })
    setSelectedStores(storeIds)
  }, [selectedItems, hierarchicalData])

  const filteredData = useMemo(() => {
    return filterHierarchicalData(searchTerm)
  }, [filterHierarchicalData, searchTerm])

  const toggleBrandExpansion = (brandId: string) => {
    const newExpanded = new Set(expandedBrands)
    if (newExpanded.has(brandId)) {
      newExpanded.delete(brandId)
    } else {
      newExpanded.add(brandId)
    }
    setExpandedBrands(newExpanded)
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  const handleItemToggle = (itemId: string, type: 'brand' | 'city' | 'store') => {
    const newSelectedStores = new Set(selectedStores)

    if (type === 'brand') {
      const brand = hierarchicalData.find(b => b.id === itemId)
      if (!brand) return

      const isCurrentlySelected = isBrandHasAnySelection(itemId)

      if (isCurrentlySelected) {
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelectedStores.delete(store.id)
          })
        })
      } else {
        brand.cities.forEach(city => {
          city.stores.forEach(store => {
            newSelectedStores.add(store.id)
          })
        })
      }
    } else if (type === 'city') {
      const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === itemId)
      if (!city) return

      const isCurrentlySelected = isCityHasAnySelection(itemId)

      if (isCurrentlySelected) {
        city.stores.forEach(store => {
          newSelectedStores.delete(store.id)
        })
      } else {
        city.stores.forEach(store => {
          newSelectedStores.add(store.id)
        })
      }
    } else if (type === 'store') {
      const isCurrentlySelected = newSelectedStores.has(itemId)

      if (isCurrentlySelected) {
        newSelectedStores.delete(itemId)

        const city = hierarchicalData.flatMap(b => b.cities).find(c => c.stores.some(s => s.id === itemId))
        const brand = hierarchicalData.find(b => b.cities.some(c => c.stores.some(s => s.id === itemId)))

        if (city && brand) {
          const remainingStoresInCity = city.stores.filter(s => s.id !== itemId && newSelectedStores.has(s.id))
          if (remainingStoresInCity.length === 0) {
            const remainingCitiesWithStores = brand.cities.filter(
              c => c.id !== city.id && c.stores.some(s => newSelectedStores.has(s.id))
            )
            if (remainingCitiesWithStores.length === 0) {
            }
          }
        }
      } else {
        newSelectedStores.add(itemId)

        const city = hierarchicalData.flatMap(b => b.cities).find(c => c.stores.some(s => s.id === itemId))
        const brand = hierarchicalData.find(b => b.cities.some(c => c.stores.some(s => s.id === itemId)))

        if (city && brand) {
          const hasOtherSelectedStores = brand.cities.some(c =>
            c.stores.some(s => s.id !== itemId && newSelectedStores.has(s.id))
          )

          if (!hasOtherSelectedStores) {
          }
        }
      }
    }

    setSelectedStores(newSelectedStores)
  }

  const isCityHasAnySelection = (cityId: string): boolean => {
    const city = hierarchicalData.flatMap(b => b.cities).find(c => c.id === cityId)
    if (!city || city.stores.length === 0) return false
    return city.stores.some(store => selectedStores.has(store.id))
  }

  const isBrandHasAnySelection = (brandId: string): boolean => {
    const brand = hierarchicalData.find(b => b.id === brandId)
    if (!brand || brand.cities.length === 0) return false
    return brand.cities.some(city => city.stores.some(store => selectedStores.has(store.id)))
  }

  const isItemSelected = (itemId: string, type: 'brand' | 'city' | 'store'): boolean => {
    if (type === 'store') {
      return selectedStores.has(itemId)
    } else if (type === 'city') {
      return isCityHasAnySelection(itemId)
    } else if (type === 'brand') {
      return isBrandHasAnySelection(itemId)
    }
    return false
  }

  const isItemIndeterminate = (_itemId: string, _type: 'brand' | 'city'): boolean => {
    return false
  }

  const getSelectedCount = () => {
    return selectedStores.size
  }

  const localSelectedItems = new Set<string>()
  selectedStores.forEach(storeId => {
    localSelectedItems.add(`store:${storeId}`)
  })

  return {
    searchTerm,
    setSearchTerm,
    expandedBrands,
    expandedCities,
    localSelectedItems,
    setLocalSelectedItems: (newSelection: Set<string>) => {
      const storeIds = new Set<string>()
      newSelection.forEach(item => {
        if (item.startsWith('store:')) {
          storeIds.add(item.replace('store:', ''))
        }
      })
      setSelectedStores(storeIds)
    },
    hierarchicalData,
    filteredData,
    toggleBrandExpansion,
    toggleCityExpansion,
    handleItemToggle,
    isItemSelected,
    isItemIndeterminate,
    getSelectedCount
  }
}
