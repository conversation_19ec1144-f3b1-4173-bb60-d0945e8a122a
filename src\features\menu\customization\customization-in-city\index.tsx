import { useNavigate } from '@tanstack/react-router'

import { Customization } from '@/types'

import { useCustomizationsData } from '@/hooks/api'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Select, SelectContent, SelectTrigger, SelectValue, SelectItem } from '@/components/ui/select'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Button, Input } from '@/components/ui'

import {
  customizationColumns,
  CustomizationDataTable,
  ActionBar,
  DeleteCustomizationModal,
  CopyCustomizationModal,
  ExportCustomizationModal,
  ImportCustomizationModal
} from './components'
import {
  useCustomizationSearch,
  useCustomizationModals,
  useCustomizationCopy,
  useCustomizationDelete,
  useCustomizationExport,
  useCustomizationImport
} from './hooks'

export default function CustomizationInCityPage() {
  const navigate = useNavigate()

  const search = useCustomizationSearch()
  const modals = useCustomizationModals()
  const copy = useCustomizationCopy()
  const deleteHook = useCustomizationDelete()
  const exportHook = useCustomizationExport()
  const importHook = useCustomizationImport()

  const {
    data: customizations,
    isLoading,
    error
  } = useCustomizationsData({
    searchTerm: search.searchTerm || undefined,
    list_city_uid: search.listCityUid
  })

  const handleCopyCustomization = (customization: Customization) => {
    copy.initializeCopyName(customization)
    modals.openCopyModal(customization)
  }

  const handleDeleteCustomization = (customization: Customization) => {
    modals.openDeleteModal(customization)
  }

  const handleConfirmCopyCustomization = async () => {
    if (!modals.selectedCustomization) return

    const targetCityUid = search.selectedCityId !== 'all' ? search.selectedCityId : undefined
    const success = await copy.handleCopyCustomization(modals.selectedCustomization, targetCityUid)
    if (success) modals.closeCopyModal()
  }

  const handleConfirmDeleteCustomization = async () => {
    if (!modals.selectedCustomization) return

    const success = await deleteHook.handleConfirmDelete(modals.selectedCustomization)
    if (success) modals.closeDeleteModal()
  }

  const handleCancelCopyModal = () => {
    copy.resetCopyName()
    modals.closeCopyModal()
  }

  const handleExportCustomizations = () => {
    exportHook.resetExportState()
    modals.openExportModal()
  }

  const handleCloseExportModal = () => {
    exportHook.resetExportState()
    modals.closeExportModal()
  }

  const handleSaveImportedData = async () => {
    const success = await exportHook.handleSaveImportedData()
    if (success) modals.closeExportModal()
  }

  const handleImportCustomizations = () => {
    importHook.resetImportState()
    modals.openImportModal()
  }

  const handleCloseImportModal = () => {
    importHook.resetImportState()
    modals.closeImportModal()
  }

  const handleSaveImportedCustomizations = async () => {
    const success = await importHook.handleSaveImportedCustomizations()
    if (success) modals.closeImportModal()
  }

  const handleCreateCustomization = () => {
    navigate({ to: '/menu/customization/customization-in-city/detail' })
  }

  const handleEditCustomization = (customization: Customization) => {
    navigate({
      to: '/menu/customization/customization-in-city/detail/$customizationId',
      params: { customizationId: customization.id }
    })
  }

  const columns = customizationColumns({
    onCopyCustomization: handleCopyCustomization,
    onDeleteCustomization: handleDeleteCustomization
  })

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <div className='mb-6 flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <h2 className='text-xl font-semibold'>Customization</h2>
              <Input
                placeholder='Tìm kiếm customization'
                className='w-64'
                value={search.searchQuery}
                onChange={e => search.setSearchQuery(e.target.value)}
                onKeyDown={search.handleSearchKeyDown}
              />
              <Select value={search.selectedCityId} onValueChange={search.setSelectedCityId}>
                <SelectTrigger className='w-48'>
                  <SelectValue placeholder='Chọn thành phố' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>Tất cả thành phố</SelectItem>
                  {search.cities.map(city => (
                    <SelectItem key={city.id} value={city.id}>
                      {city.city_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className='flex items-center gap-4'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size='sm' variant='outline'>
                    Tiện ích
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuItem onClick={handleExportCustomizations}>Xuất, Chi tiết customization</DropdownMenuItem>
                  <DropdownMenuItem onClick={handleImportCustomizations}>Thêm customization từ file</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button size='sm' variant='default' onClick={handleCreateCustomization}>
                Tạo customization
              </Button>
            </div>
          </div>

          {error && (
            <div className='mb-4 rounded-md bg-red-50 p-4 text-red-700'>
              Có lỗi xảy ra khi tải dữ liệu: {error.message}
            </div>
          )}

          <CustomizationDataTable
            columns={columns}
            data={customizations || []}
            isLoading={isLoading}
            onRowClick={handleEditCustomization}
          />

          <DeleteCustomizationModal
            open={modals.confirmModalOpen}
            onOpenChange={modals.setConfirmModalOpen}
            selectedCustomization={modals.selectedCustomization}
            onCancel={modals.closeDeleteModal}
            onConfirm={handleConfirmDeleteCustomization}
            isLoading={deleteHook.isLoading}
          />

          <CopyCustomizationModal
            open={modals.copyModalOpen}
            onOpenChange={modals.setCopyModalOpen}
            selectedCustomization={modals.selectedCustomization}
            copyName={copy.copyName}
            onCopyNameChange={copy.setCopyName}
            onCancel={handleCancelCopyModal}
            onConfirm={handleConfirmCopyCustomization}
            isLoading={copy.isLoading}
          />

          <ExportCustomizationModal
            open={modals.exportModalOpen}
            onOpenChange={modals.setExportModalOpen}
            showParsedData={exportHook.showParsedData}
            exportCityId={exportHook.exportCityId}
            onExportCityIdChange={exportHook.setExportCityId}
            cities={exportHook.cities}
            selectedFile={exportHook.selectedFile}
            parsedData={exportHook.parsedData}
            isExporting={exportHook.isExporting}
            isSaving={exportHook.isSaving}
            onCancel={handleCloseExportModal}
            onConfirm={exportHook.showParsedData ? handleSaveImportedData : handleCloseExportModal}
            onDownloadExportFile={exportHook.handleDownloadExportFile}
            onUploadFile={exportHook.handleUploadFile}
          />

          <ImportCustomizationModal
            open={modals.importModalOpen}
            onOpenChange={modals.setImportModalOpen}
            showImportParsedData={importHook.showImportParsedData}
            importSelectedFile={importHook.importSelectedFile}
            importParsedData={importHook.importParsedData}
            isLoading={importHook.isLoading}
            onCancel={handleCloseImportModal}
            onConfirm={importHook.showImportParsedData ? handleSaveImportedCustomizations : handleCloseImportModal}
            onDownloadTemplate={importHook.handleDownloadTemplate}
            onImportFileUpload={importHook.handleImportFileUpload}
          />

          <input
            type='file'
            ref={exportHook.fileInputRef}
            onChange={exportHook.handleFileChange}
            accept='.xlsx,.xls'
            style={{ display: 'none' }}
          />
          <input
            type='file'
            ref={importHook.importFileInputRef}
            onChange={importHook.handleImportFileChange}
            accept='.xlsx,.xls'
            style={{ display: 'none' }}
          />
        </div>
      </Main>

      <div className='container mx-auto px-4 py-8'>
        <ActionBar
          searchQuery={search.searchQuery}
          onSearchQueryChange={search.setSearchQuery}
          onSearchKeyDown={search.handleSearchKeyDown}
          selectedCityId={search.selectedCityId}
          onCityChange={search.setSelectedCityId}
          cities={search.cities}
          onExportCustomizations={handleExportCustomizations}
          onImportCustomizations={handleImportCustomizations}
          onCreateCustomization={handleCreateCustomization}
        />

        {error && (
          <div className='mb-4 rounded-md bg-red-50 p-4 text-red-700'>
            Có lỗi xảy ra khi tải dữ liệu: {error.message}
          </div>
        )}

        <CustomizationDataTable
          columns={columns}
          data={customizations || []}
          isLoading={isLoading}
          onRowClick={handleEditCustomization}
        />

        <DeleteCustomizationModal
          open={modals.confirmModalOpen}
          onOpenChange={modals.setConfirmModalOpen}
          selectedCustomization={modals.selectedCustomization}
          onCancel={modals.closeDeleteModal}
          onConfirm={handleConfirmDeleteCustomization}
          isLoading={deleteHook.isLoading}
        />

        <CopyCustomizationModal
          open={modals.copyModalOpen}
          onOpenChange={modals.setCopyModalOpen}
          selectedCustomization={modals.selectedCustomization}
          copyName={copy.copyName}
          onCopyNameChange={copy.setCopyName}
          onCancel={handleCancelCopyModal}
          onConfirm={handleConfirmCopyCustomization}
          isLoading={copy.isLoading}
        />

        <ExportCustomizationModal
          open={modals.exportModalOpen}
          onOpenChange={modals.setExportModalOpen}
          showParsedData={exportHook.showParsedData}
          exportCityId={exportHook.exportCityId}
          onExportCityIdChange={exportHook.setExportCityId}
          cities={exportHook.cities}
          selectedFile={exportHook.selectedFile}
          parsedData={exportHook.parsedData}
          isExporting={exportHook.isExporting}
          isSaving={exportHook.isSaving}
          onCancel={handleCloseExportModal}
          onConfirm={exportHook.showParsedData ? handleSaveImportedData : handleCloseExportModal}
          onDownloadExportFile={exportHook.handleDownloadExportFile}
          onUploadFile={exportHook.handleUploadFile}
        />

        <ImportCustomizationModal
          open={modals.importModalOpen}
          onOpenChange={modals.setImportModalOpen}
          showImportParsedData={importHook.showImportParsedData}
          importSelectedFile={importHook.importSelectedFile}
          importParsedData={importHook.importParsedData}
          isLoading={importHook.isLoading}
          onCancel={handleCloseImportModal}
          onConfirm={importHook.showImportParsedData ? handleSaveImportedCustomizations : handleCloseImportModal}
          onDownloadTemplate={importHook.handleDownloadTemplate}
          onImportFileUpload={importHook.handleImportFileUpload}
        />

        <input
          type='file'
          ref={exportHook.fileInputRef}
          onChange={exportHook.handleFileChange}
          accept='.xlsx,.xls'
          style={{ display: 'none' }}
        />
        <input
          type='file'
          ref={importHook.importFileInputRef}
          onChange={importHook.handleImportFileChange}
          accept='.xlsx,.xls'
          style={{ display: 'none' }}
        />
      </div>
    </>
  )
}
