import { Settings } from 'lucide-react'

import { DataTable, DataTableColumn } from '@/components/ui/data-table'

import { Badge, Button } from '@/components/ui'

import { LocalUser } from '../data'

interface AccountTableProps {
  users: LocalUser[]
  isLoading: boolean
  onEditUser: (user: LocalUser) => void
  onToggleStatus: (userId: string) => void
}

export function AccountTable({ users, isLoading, onEditUser, onToggleStatus }: AccountTableProps) {
  const columns: DataTableColumn<LocalUser>[] = [
    {
      key: 'username',
      header: 'Tên người dùng',
      width: '200px'
    },
    {
      key: 'email',
      header: 'Email',
      width: '250px'
    },
    {
      key: 'status',
      header: 'Trạng thái',
      width: '120px',
      render: (value: string) => (
        <Badge variant={value === 'active' ? 'default' : 'secondary'}>
          {value === 'active' ? 'Hoạt động' : 'Không hoạt động'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: '',
      width: '100px',
      render: (_, user: LocalUser) => (
        <div className='flex items-center gap-2'>
          <Button variant='ghost' size='icon' onClick={() => onEditUser(user)} className='h-8 w-8'>
            <Settings className='h-4 w-4' />
          </Button>
          <Button variant='ghost' size='icon' onClick={() => onToggleStatus(user.id)} className='h-8 w-8'>
            {user.status === 'active' ? 'Hủy' : 'Kích hoạt'}
          </Button>
        </div>
      )
    }
  ]

  if (isLoading) {
    return (
      <div className='py-8 text-center'>
        <p>Đang tải dữ liệu...</p>
      </div>
    )
  }

  return (
    <DataTable
      data={users}
      columns={columns}
      isLoading={isLoading}
      pageSize={20}
      emptyMessage='Không có tài khoản nào'
      loadingMessage='Đang tải...'
    />
  )
}
