import { useState } from 'react'

import { toast } from 'sonner'

interface MenuItem {
  id: string
  name: string
  price: number
  code?: string
  size?: string
}

interface CustomizationGroup {
  id: string
  name: string
  minRequired: number
  maxAllowed: number
  items: MenuItem[]
}

export function useGroupManagement() {
  const [customizationGroups, setCustomizationGroups] = useState<CustomizationGroup[]>([])
  const [editingGroupId, setEditingGroupId] = useState<string | null>(null)

  // Group form state
  const [groupName, setGroupName] = useState('')
  const [minRequired, setMinRequired] = useState('0')
  const [maxAllowed, setMaxAllowed] = useState('0')
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])

  const handleCreateGroup = () => {
    setEditingGroupId(null)
    resetGroupForm()
  }

  const handleEditGroup = (groupId: string) => {
    const group = customizationGroups.find(g => g.id === groupId)
    if (group) {
      setEditingGroupId(groupId)
      setGroupName(group.name)
      setMinRequired(group.minRequired.toString())
      setMaxAllowed(group.maxAllowed.toString())
      setMenuItems(group.items)
    }
  }

  const handleDeleteGroup = (groupId: string) => {
    setCustomizationGroups(groups => groups.filter(g => g.id !== groupId))
    toast.success('Đã xóa nhóm thành công!')
  }

  const handleSaveGroup = (items: Array<{ id: string; item_id: string; code?: string }>) => {
    if (!groupName.trim()) {
      toast.error('Vui lòng nhập tên nhóm')
      return false
    }

    const minReq = parseInt(minRequired)
    const maxAllow = parseInt(maxAllowed)

    if (isNaN(minReq) || minReq < 0) {
      toast.error('Yêu cầu phải là số hợp lệ')
      return false
    }

    if (isNaN(maxAllow) || maxAllow < 0) {
      toast.error('Tối đa phải là số hợp lệ')
      return false
    }

    if (menuItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một món')
      return false
    }

    const groupData: CustomizationGroup = {
      id: editingGroupId || Date.now().toString(),
      name: groupName.trim(),
      minRequired: minReq,
      maxAllowed: maxAllow,
      items: menuItems.map(item => {
        const originalItem = items.find(apiItem => apiItem.id === item.id)
        return {
          ...item,
          code: originalItem?.item_id || item.code || item.id,
          size: 'M'
        }
      })
    }

    if (editingGroupId) {
      setCustomizationGroups(groups => groups.map(g => (g.id === editingGroupId ? groupData : g)))
      toast.success('Đã cập nhật nhóm thành công!')
    } else {
      setCustomizationGroups(groups => [...groups, groupData])
      toast.success('Đã tạo nhóm thành công!')
    }

    resetGroupForm()
    return true
  }

  const resetGroupForm = () => {
    setGroupName('')
    setMinRequired('0')
    setMaxAllowed('0')
    setMenuItems([])
    setEditingGroupId(null)
  }

  return {
    // State
    customizationGroups,
    editingGroupId,
    groupName,
    minRequired,
    maxAllowed,
    menuItems,

    // Actions
    setGroupName,
    setMinRequired,
    setMaxAllowed,
    setMenuItems,
    setCustomizationGroups,
    handleCreateGroup,
    handleEditGroup,
    handleDeleteGroup,
    handleSaveGroup,
    resetGroupForm,

    // Computed
    isEditing: !!editingGroupId
  }
}
