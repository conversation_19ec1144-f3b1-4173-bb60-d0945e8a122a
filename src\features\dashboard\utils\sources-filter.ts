import { ReportsSourcesData } from '@/types/api'

// Platform mapping based on source_name patterns
export const PLATFORM_MAPPING = {
  shopee: ['ShopeeFood', 'NOWDHI', '10000169'],
  grab: ['GRA<PERSON>OOD', 'GRAPDHI', '10000232'],
  be: ['BE_FOOD', 'BEFOOD', 'BE_TUTIMI', 'BE-DHI'],
  taiCho: ['TẠI CHỖ', '10000172'],
  mangVe: ['MANG VỀ', '10000171']
} as const

export type PlatformType = keyof typeof PLATFORM_MAPPING

/**
 * Filter sources data by platform
 */
export function filterSourcesByPlatform(
  sourcesData: ReportsSourcesData[],
  platform: PlatformType
): ReportsSourcesData[] {
  const platformKeywords = PLATFORM_MAPPING[platform]
  
  return sourcesData.filter(source => 
    platformKeywords.some(keyword => 
      source.source_name.includes(keyword) || 
      source.source_id === keyword
    )
  )
}

/**
 * Get total revenue for a specific platform
 */
export function getPlatformRevenue(
  sourcesData: ReportsSourcesData[],
  platform: PlatformType
): number {
  const platformSources = filterSourcesByPlatform(sourcesData, platform)
  return platformSources.reduce((total, source) => total + source.revenue_gross, 0)
}

/**
 * Get total bills for a specific platform
 */
export function getPlatformBills(
  sourcesData: ReportsSourcesData[],
  platform: PlatformType
): number {
  const platformSources = filterSourcesByPlatform(sourcesData, platform)
  return platformSources.reduce((total, source) => total + source.total_bill, 0)
}

/**
 * Get platform summary data
 */
export function getPlatformSummary(
  sourcesData: ReportsSourcesData[],
  platform: PlatformType
) {
  const platformSources = filterSourcesByPlatform(sourcesData, platform)
  
  const totalRevenue = platformSources.reduce((total, source) => total + source.revenue_gross, 0)
  const totalBills = platformSources.reduce((total, source) => total + source.total_bill, 0)
  const totalDiscount = platformSources.reduce((total, source) => total + source.discount_amount, 0)
  const totalCommission = platformSources.reduce((total, source) => total + source.commission_amount, 0)
  const revenueNet = platformSources.reduce((total, source) => total + source.revenue_net, 0)

  return {
    sources: platformSources,
    totalRevenue,
    totalBills,
    totalDiscount,
    totalCommission,
    revenueNet,
    sourceCount: platformSources.length
  }
}

/**
 * Format platform name for display
 */
export function getPlatformDisplayName(platform: PlatformType): string {
  const displayNames = {
    shopee: 'Shopee',
    grab: 'Grab',
    be: 'Be',
    taiCho: 'Tại chỗ',
    mangVe: 'Mang về'
  }
  
  return displayNames[platform]
}
