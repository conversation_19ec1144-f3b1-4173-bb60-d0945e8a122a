import { useQuery } from '@tanstack/react-query'

import type { 
  KtvMonthReportParams, 
  KtvMonthReportResponse 
} from '@/types/api/ktv-reports'

import { getKtvMonthReport } from '@/lib/api/pos/ktv-month-report-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UseKtvMonthReportOptions {
  params: KtvMonthReportParams
  enabled?: boolean
}

export const useKtvMonthReport = ({ params, enabled = true }: UseKtvMonthReportOptions) => {
  const queryKey = [
    QUERY_KEYS.KTV_REPORTS,
    'month-report',
    params.store_uid,
    params.date_time
  ]

  const queryFn = async (): Promise<KtvMonthReportResponse> => {
    if (!params.store_uid) {
      return { 
        data: {
          ts_time: 0,
          store_uid: '',
          revenue_net: 0,
          material_cost: 0,
          material_cost_detail: [],
          purchase_detail: [],
          count_item_not_price: 0,
          count_item_price: 0,
          count_item: 0,
          tool_cost: 0,
          tool_cost_detail: [],
          count_tool: 0,
          cost_amount_inex: 0,
          cost_inex_detail: [],
          count_cost_out: 0,
          count_cost_out_all: 0,
          income_amount: 0,
          income_detail: [],
          profit: 0,
          date_time_filter: null,
          require_close_report: 0,
          ivt_price: 0,
          status_report: 'open',
          percentage_material: 0,
          percentage_tool: 0,
          percentage_inex: 0,
          percentage_profit: 0
        },
        track_id: '' 
      }
    }

    return getKtvMonthReport(params)
  }

  const query = useQuery({
    queryKey,
    queryFn,
    enabled: enabled && !!params.store_uid,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false
  })

  return {
    data: query.data?.data || null,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasData: !!query.data?.data,
    trackId: query.data?.track_id || ''
  }
}
