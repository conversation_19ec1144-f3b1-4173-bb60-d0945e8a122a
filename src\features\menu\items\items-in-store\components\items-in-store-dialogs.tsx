import { ConfirmDialog } from '@/components/confirm-dialog'

import { useItemsInStore } from '../context'
import { useItemsInStoreForTable } from '../hooks'
import { useStoresData } from '@/hooks/api'
import { BuffetConfigModal } from './buffet-config-modal'
import { ExportDialog, ImportDialog, PriceBySourceConfigModal, SortMenuModal, CopyMenuModal, TimeFrameConfigModal } from './modals'
import { ItemsInStoreMutate } from './items-in-store-mutate'

interface Props {
  storeUid: string
}

export function ItemsInStoreDialogs({ storeUid }: Props) {
  const {
    createOpen, setCreateOpen,
    copyOpen, setCopyOpen,
    updateOpen, setUpdateOpen,
    deleteOpen, setDeleteOpen,
    exportOpen, setExportOpen,
    importOpen, setImportOpen,
    buffetConfigOpen, setBuffetConfigOpen,
    priceBySourceConfigOpen, setPriceBySourceConfigOpen,
    sortMenuOpen, setSortMenuOpen,
    copyMenuOpen, setCopyMenuOpen,
    timeFrameConfigOpen, setTimeFrameConfigOpen,
    currentRow, setCurrentRow
  } = useItemsInStore()

  // Fetch stores data for modals
  const { data: stores = [] } = useStoresData()

  // Fetch menu items for buffet config - use table data
  const { data: menuItemsForTable = [] } = useItemsInStoreForTable({
    enabled: buffetConfigOpen && !!currentRow
  })

  return (
    <>
      <ExportDialog
        open={exportOpen}
        onOpenChange={setExportOpen}
      />

      <ImportDialog
        open={importOpen}
        onOpenChange={setImportOpen}
        storeUid={storeUid}
      />

      <ItemsInStoreMutate
        key='items-in-store-create'
        open={createOpen}
        onOpenChange={isOpen => {
          setCreateOpen(isOpen)
        }}
      />

      {currentRow && (
        <>
          <ItemsInStoreMutate
            key={`items-in-store-copy-${currentRow.id}`}
            open={copyOpen}
            onOpenChange={isOpen => {
              setCopyOpen(isOpen)
              if (!isOpen) {
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
            isCopyMode={true}
          />

          <ItemsInStoreMutate
            key={`items-in-store-update-${currentRow.id}`}
            open={updateOpen}
            onOpenChange={isOpen => {
              setUpdateOpen(isOpen)
              if (!isOpen) {
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
          />

          <ConfirmDialog
            key='quantity-day-delete'
            destructive
            open={deleteOpen}
            onOpenChange={isOpen => {
              if (!isOpen) {
                setDeleteOpen(false)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={async () => {
              setDeleteOpen(false)
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
              // TODO: Add delete mutation when available
            }}
            className='max-w-md'
            title={`Bạn có muốn xoá ?`}
            desc={<>Hành động không thể hoàn tác.</>}
            confirmText='Xoá'
          />
        </>
      )}

      <BuffetConfigModal
        open={buffetConfigOpen}
        onOpenChange={isOpen => {
          setBuffetConfigOpen(isOpen)
          if (!isOpen) {
            setTimeout(() => {
              setCurrentRow(null)
            }, 500)
          }
        }}
        menuItem={currentRow ? menuItemsForTable.find(item => item.id === currentRow.id) || null : null}
        menuItems={menuItemsForTable}
      />

      <PriceBySourceConfigModal
        open={priceBySourceConfigOpen}
        onOpenChange={setPriceBySourceConfigOpen}
        stores={stores}
      />

      <SortMenuModal
        open={sortMenuOpen}
        onOpenChange={setSortMenuOpen}
      />

      <CopyMenuModal
        open={copyMenuOpen}
        onOpenChange={setCopyMenuOpen}
      />

      <TimeFrameConfigModal
        open={timeFrameConfigOpen}
        onOpenChange={setTimeFrameConfigOpen}
      />
    </>
  )
}
