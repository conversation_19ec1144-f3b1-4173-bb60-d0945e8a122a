import { useQuery } from '@tanstack/react-query'

import { api } from '@/lib/api'

interface SaleChangeLogParams {
  company_uid: string
  brand_uid: string
  store_uid: string
  tran_id: string
  start_date: number
  end_date: number
}

interface SaleChangeLogResponse {
  data: Array<{
    id: string
    tran_id: string
    action: string
    action_time: string
    user_name: string
    old_value?: string
    new_value?: string
    field_name?: string
    description?: string
    created_at: number
    updated_at: number
    device_code: string
    start_date: number
    log_type: string
    table_name: string
    change_data: {
      end_hour: number
      re_print_new: number
      end_minute: number
      sale_by_source_payment_status: string
      sale_note: string
      commission: number
      shift_id: string
      commission_amount: number
      currency: string
      partner_marketing: number
      employee_id: string
      partner_marketing_amount: number
      employee_name: string
      last_order: number
      sale_type: string
      item_not_done: number
      ship_fee_amount: number
      sale_detail: Array<{
        toppings: Array<{
          check_active: number
          sub_topping: any[]
          raw_quantity: number
          id_group_cus: string
          minute: number
          item_type_name: string
          item_type_id: string
          item_class_id: string
          price: number
          price_org: number
          item_id_mapping: string
          item_id: string
          item_name: string
          quantity: number
          amount: number
          unit_id: string
          quantity_secondary: number
          amount_all_topping: number
          note: string
          is_eat_with: number
          is_vat: number
          package_id: string
          amount_discount_on_price: number
          discount_extra_amount: number
          service_charge_amount: number
          ship_fee_amount: number
          voucher_amount: number
          id_sale_detail: string
          commission_amount: number
          quantity_done: number
          tran_id: string
          quantity_print_temp: number
          vat_tax_rate: number
          partner_marketing_amount: number
          vat_tax_amount: number
          not_update_quantop: number
          vat_tax_reverse_amount: number
          split_quantity: number
          time_cooking: number
          start_date: number
          discount_vat_amount: number
          discount_vat: number
          discount: number
          discount_values_amount: number
          discount_amount: number
          deduct_tax_amount: number
          hour: number
        }>
        split_order: boolean
        is_printed: number
        sub_quantity: number
        status: string
        request_kds_before_del: number
        is_order_o2o: boolean
        sort_index: number
        last_time_service: number
        minute: number
        item_type_name: string
        item_type_id: string
        item_class_id: string
        item_class_name: string
        price: number
        price_org: number
        item_id_mapping: string
        item_id: string
        item_name: string
        quantity: number
        amount: number
        unit_id: string
        quantity_secondary: number
        amount_all_topping: number
        note: string
        is_eat_with: number
        is_vat: number
        package_id: string
        amount_discount_on_price: number
        discount_extra_amount: number
        service_charge_amount: number
        ship_fee_amount: number
        voucher_amount: number
        id_sale_detail: string
        commission_amount: number
        quantity_done: number
        tran_id: string
        quantity_print_temp: number
        vat_tax_rate: number
        partner_marketing_amount: number
        vat_tax_amount: number
        not_update_quantop: number
        vat_tax_reverse_amount: number
        split_quantity: number
        time_cooking: number
        start_date: number
        discount_vat_amount: number
        discount_vat: number
        discount: number
        discount_values_amount: number
        discount_amount: number
        deduct_tax_amount: number
        hour: number
      }>
      is_export_vat: boolean
      sale_payment_method: any[]
      brand_id: string
      foodbook_order_id: string
      enable_vat_pos: number
      source_fb_id: string
      is_sync_vat: number
      order_type: string
      device_code: string
      source_deli: string
      total_amount: number
      voucher_code: string
      voucher_name: string
      voucher_extra: number
      voucher_amount: number
      voucher_amount_paid: number
      tran_id: string
      vat_extra: number
      origin_tran_id: string
      vat_amount: number
      tran_no: string
      vat_tax_reverse_amount: number
      order_no: string
      discount_vat_amount: number
      start_date: number
      deduct_tax_rate: number
      tran_date: number
      deduct_tax_amount: number
      discount_extra: number
      discount_extra_amount: number
      discount_extra_amount_campaign: number
      service_charge_name: string
      service_charge: number
      service_charge_amount: number
      amount_origin: number
      amount_discount_detail: number
      amount_discount_price: number
      state_action_bill: number
      version_app: string
      sale_updated_at: number
      extra_data: any
      store_uid: string
      brand_uid: string
      rev_local: number
      city_uid: string
      last_rev_local: number
      company_uid: string
      last_size: number
      table_name: string
      is_edited: number
      start_hour: number
      stt_order: number
      start_minute: number
      merge_table_id: string
    }
    sale_current: any
    fmt_created_at: string
    fmt_updated_at: string
  }>
  message?: string
}

export const useSaleChangeLog = (params: SaleChangeLogParams, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['sale-change-log', params],
    queryFn: async (): Promise<SaleChangeLogResponse> => {
      const searchParams = new URLSearchParams({
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        store_uid: params.store_uid,
        tran_id: params.tran_id,
        start_date: params.start_date.toString(),
        end_date: params.end_date.toString()
      })

      const response = await api.get(`/v3/pos-cms/sale-change-log?${searchParams.toString()}`)
      return response.data as SaleChangeLogResponse
    },
    enabled: enabled && !!params.tran_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false
  })
}
