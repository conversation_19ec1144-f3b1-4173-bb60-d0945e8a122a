import { useAuthStore } from '@/stores/authStore'

import { api } from '@/lib/api/pos/pos-api'

import type { ItemsInStore } from '../data'

// Type definitions
export interface ItemExtraData {
  id: string
  item_uid: string
  extra_name: string
  extra_price: number
  extra_type: string
  active: boolean
  created_at: string
  updated_at: string
}

export interface ItemStore {
  id: string
  item_uid: string
  store_uid: string
  store_name: string
  active: boolean
  created_at: string
  updated_at: string
}

export interface ItemInStore extends ItemsInStore {
  item_extra_data?: ItemExtraData[]
  item_stores?: ItemStore[]
}

export interface ItemsInStoreApiResponse {
  data: ItemsInStore[]
  total: number
  page: number
  per_page: number
}

// API Parameters
export interface GetItemsInStoreParams {
  company_uid?: string
  brand_uid?: string
  page?: number
  reverse?: number
  apply_with_store?: string | number
  item_type_uid?: string
  store_uid?: string
  list_store_uid?: string
  time_sale_date_week?: string
  active?: string
  skip_limit?: boolean
  limit?: number
}

export interface DeleteItemInStoreParams {
  item_uid: string
}

export interface DeleteMultipleItemsInStoreParams {
  list_item_uid: string[]
}

export interface CreateItemInStoreRequest {
  item_name: string
  item_description?: string
  item_price: number
  item_type_uid: string
  active?: boolean
  // Add other fields as needed
}

export interface UpdateItemInStoreRequest {
  item_uid: string
  item_name?: string
  item_description?: string
  item_price?: number
  item_type_uid?: string
  active?: boolean
  // Add other fields as needed
}

export interface UpdateItemCustomizationRequest {
  menuItem: ItemsInStore
  customization_uid: string | null
  company_uid: string
  brand_uid: string
}

export interface UpdateItemBuffetConfigRequest {
  menuItem: ItemsInStore
  isBuffetItem: boolean
  excludeItems: string[]
}

export interface UpdateItemStatusRequest {
  id: string
  active: number
  company_uid: string
  brand_uid: string
}

export interface GetItemByListIdParams {
  list_id: string
}

export interface GetItemByIdParams {
  item_uid: string
}

export interface DownloadTemplateParams {
  template_type: string
}

export interface BulkUpdateItemsInStoreRequest {
  items: ItemsInStore[]
}

export interface CloneMenuRequest {
  company_uid: string
  brand_uid: string
  store_uid_root: string
  store_uid_target: string
  list_item_id: string[]
  menu_type: 'store' | 'brand' | 'city'
}

export interface UseItemsInStoreDataOptions {
  params?: GetItemsInStoreParams
  enabled?: boolean
}

/**
 * Get auth data helper
 */
const getAuthData = () => {
  const { company, brands } = useAuthStore.getState().auth
  const selectedBrand = brands?.[0]

  return {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || ''
  }
}

/**
 * Items In Store API Service
 */
export const itemsInStoreApiService = {
  /**
   * Get items in store list
   */
  async getItemsInStore(params: GetItemsInStoreParams = {}): Promise<ItemsInStore[]> {
    const authData = getAuthData()

    const dynamicParams = {
      company_uid: authData.company_uid,
      brand_uid: authData.brand_uid,
      page: 1,
      reverse: 1,
      ...params
    }

    const queryParams = new URLSearchParams()

    Object.entries(dynamicParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // Skip skip_limit and limit here as they are handled separately
        if (key !== 'skip_limit' && key !== 'limit') {
          queryParams.append(key, value.toString())
        }
      }
    })

    // Add skip_limit if specified (avoid duplicate)
    if (params.skip_limit || dynamicParams.skip_limit) {
      queryParams.append('skip_limit', 'true')
    }

    // Add limit if specified (avoid duplicate)
    if (params.limit || dynamicParams.limit) {
      queryParams.append('limit', (params.limit || dynamicParams.limit)!.toString())
    }

    const apiUrl = `/mdata/v1/items?${queryParams.toString()}`

    const response = await api.get(apiUrl)

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Invalid response format from items API')
    }

    // Handle different response structures
    const responseData = response.data
    if (responseData.data && Array.isArray(responseData.data)) {
      return responseData.data as ItemsInStore[]
    } else if (Array.isArray(responseData)) {
      return responseData as ItemsInStore[]
    } else {
      return (responseData.data as ItemsInStore[]) || []
    }
  },

  /**
   * Get items in store for table (with additional processing)
   */
  async getItemsInStoreForTable(params: GetItemsInStoreParams = {}): Promise<ItemsInStore[]> {
    return this.getItemsInStore(params)
  },

  /**
   * Get item in store detail
   */
  async getItemInStoreDetail(params: GetItemByIdParams): Promise<ItemInStore> {
    const authData = getAuthData()

    const queryParams = new URLSearchParams()
    queryParams.append('company_uid', authData.company_uid)
    queryParams.append('brand_uid', authData.brand_uid)
    queryParams.append('id', params.item_uid)

    const response = await api.get(`/mdata/v1/item?${queryParams.toString()}`)

    if (!response.data) {
      throw new Error('Item not found')
    }

    return response.data.data as ItemInStore
  },

  /**
   * Get item by list ID
   */
  async getItemByListId(params: GetItemByListIdParams): Promise<ItemInStore> {
    const authData = getAuthData()

    const queryParams = new URLSearchParams()
    queryParams.append('company_uid', authData.company_uid)
    queryParams.append('brand_uid', authData.brand_uid)
    queryParams.append('list_id', params.list_id)

    const response = await api.get(`/mdata/v1/items/by-list-id?${queryParams.toString()}`)

    if (!response.data) {
      throw new Error('Item not found')
    }

    return response.data.data as ItemInStore
  },

  /**
   * Create item in store
   */
  async createItemInStore(data: CreateItemInStoreRequest): Promise<ItemsInStore> {
    const authData = getAuthData()

    const payload = {
      ...data,
      company_uid: authData.company_uid,
      brand_uid: authData.brand_uid,
      is_fabi: 1,
      apply_with_store: 2,
      extra_data: {
        formula_qrcode: '',
        is_buffet_item: (data as any).is_buffet_item || 0,
        up_size_buffet: [],
        is_item_service: (data as any).is_item_service || 0,
        is_virtual_item: (data as any).is_virtual_item || 0,
        price_by_source: [],
        enable_edit_price: (data as any).enable_edit_price || 0,
        exclude_items_buffet: [],
        no_update_quantity_toping: 0
      }
    }

    const response = await api.post('/mdata/v1/item', payload)
    return response.data.data as ItemsInStore
  },

  /**
   * Update item in store
   */
  async updateItemInStore(data: UpdateItemInStoreRequest): Promise<ItemsInStore> {
    const authData = getAuthData()

    const payload = {
      ...data,
      company_uid: authData.company_uid,
      brand_uid: authData.brand_uid
    }

    const response = await api.put(`/mdata/v1/items/${data.item_uid}`, payload)
    return response.data.data as ItemsInStore
  },

  /**
   * Update item customization
   */
  async updateItemCustomization(data: UpdateItemCustomizationRequest): Promise<unknown> {
    try {
      const currentItem = await itemsInStoreApiService.getItemInStoreDetail({ item_uid: data.menuItem.id })
      const updateData = {
        ...(currentItem as unknown as Record<string, unknown>),
        customization_uid: data.customization_uid,
        company_uid: data.company_uid,
        brand_uid: data.brand_uid
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data.data || response.data
    } catch (error) {
      throw new Error('Không thể cập nhật customization. Vui lòng thử lại.')
    }
  },

  /**
   * Update item buffet config
   */
  async updateItemBuffetConfig(data: { item_uid: string; buffet_config: any }): Promise<unknown> {
    try {
      const authData = getAuthData()

      // Get current item data first, similar to updateItemCustomization
      const currentItem = await itemsInStoreApiService.getItemInStoreDetail({ item_uid: data.item_uid })

      // Update extra_data with buffet config instead of creating separate buffet_config field
      const currentItemData = currentItem as any
      const currentExtraData = currentItemData.extra_data || {}

      const updatedExtraData = {
        ...currentExtraData,
        is_buffet_item: data.buffet_config.is_buffet_item ? 1 : 0,
        exclude_items_buffet: data.buffet_config.exclude_items_buffet || []
      }

      const updateData = {
        ...(currentItem as unknown as Record<string, unknown>),
        extra_data: updatedExtraData,
        company_uid: authData.company_uid,
        brand_uid: authData.brand_uid
      }

      const response = await api.put('/mdata/v1/item', updateData, {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          'accept-language': 'vi',
          fabi_type: 'pos-cms',
          'x-client-timezone': '25200000'
        },
        timeout: 30000
      })

      return response.data.data || response.data
    } catch (error) {
      throw new Error('Không thể cập nhật cấu hình buffet. Vui lòng thử lại.')
    }
  },

  /**
   * Update item status
   */
  async updateItemInStoreStatus(data: { item_uid: string; active: boolean }): Promise<ItemsInStore> {
    const authData = getAuthData()

    // First, get the current item data
    const currentItem = await this.getItemInStoreDetail({ item_uid: data.item_uid })

    // Prepare the full item data with updated active status
    const payload = {
      ...currentItem,
      active: data.active ? 1 : 0,
      company_uid: authData.company_uid,
      brand_uid: authData.brand_uid
    }

    const response = await api.put('/mdata/v1/item', payload, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'accept-language': 'vi',
        fabi_type: 'pos-cms',
        'x-client-timezone': '25200000'
      }
    })
    return response.data.data as ItemsInStore
  },

  /**
   * Delete item in store
   */
  async deleteItemInStore(params: DeleteItemInStoreParams): Promise<void> {
    const authData = getAuthData()

    const queryParams = new URLSearchParams()
    queryParams.append('company_uid', authData.company_uid)
    queryParams.append('brand_uid', authData.brand_uid)

    await api.delete(`/mdata/v1/items/${params.item_uid}?${queryParams.toString()}`)
  },

  /**
   * Delete multiple items in store
   */
  async deleteMultipleItemsInStore(params: DeleteMultipleItemsInStoreParams): Promise<void> {
    const authData = getAuthData()

    const queryParams = new URLSearchParams()
    queryParams.append('company_uid', authData.company_uid)
    queryParams.append('brand_uid', authData.brand_uid)
    queryParams.append('list_item_uid', params.list_item_uid.join(','))

    await api.delete(`/mdata/v1/items?${queryParams.toString()}`)
  },

  /**
   * Download items template
   */
  async downloadItemsTemplate(params: DownloadTemplateParams): Promise<Blob> {
    const authData = getAuthData()

    const queryParams = new URLSearchParams()
    queryParams.append('company_uid', authData.company_uid)
    queryParams.append('brand_uid', authData.brand_uid)
    queryParams.append('template_type', params.template_type)

    const response = await api.get(`/mdata/v1/items/template?${queryParams.toString()}`, {
      responseType: 'blob'
    })

    return response.data as unknown as Blob
  },

  /**
   * Import items
   */
  async importItems(file: File): Promise<{ success: boolean; message: string }> {
    const authData = getAuthData()

    const formData = new FormData()
    formData.append('file', file)
    formData.append('company_uid', authData.company_uid)
    formData.append('brand_uid', authData.brand_uid)

    const response = await api.post('/mdata/v1/items/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    return {
      success: (response.data as any)?.success ?? true,
      message: (response.data as any)?.message ?? 'Import thành công'
    }
  },

  /**
   * Bulk update items in store
   */
  async bulkUpdateItemsInStore(items: ItemsInStore[]): Promise<any> {
    const response = await api.put('/mdata/v1/items', items)

    return response.data.data || response.data
  },

  /**
   * Clone menu items
   */
  async cloneMenu(data: CloneMenuRequest): Promise<any> {
    const response = await api.post('/mdata/v1/clone-menu', data)

    return response.data.data || response.data
  }
}
