import { useItemTypesData, useItemClassesData, useUnitsData, useCitiesData } from '@/hooks/api'

import { useItemsInStoreData } from './use-items-in-store-data'

interface UseItemsInStoreDataOptions {
  params?: Record<string, string>
  enabled?: boolean
}

export const useItemsInStoreForTable = (options: UseItemsInStoreDataOptions = {}) => {
  const itemsQuery = useItemsInStoreData(options)
  const storeUid = options.params?.store_uid

  const { data: itemTypesData = [] } = useItemTypesData({
    skip_limit: true,
    ...(storeUid && storeUid !== 'all' ? { store_uid: storeUid } : {})
  })
  const { data: itemClassesData = [] } = useItemClassesData({ skip_limit: true })
  const { data: unitsData = [] } = useUnitsData()
  const { data: citiesData = [] } = useCitiesData()

  const transformedData =
    itemsQuery.data?.map(item => ({
      ...item,
      item_type_name:
        itemTypesData.find(type => type.id === item.item_type_uid)?.item_type_name ||
        item.item_type_uid,
      item_class_name:
        itemClassesData.find(cls => cls.id === item.item_class_uid)?.item_class_name ||
        item.item_class_uid,
      unit_name: unitsData.find(unit => unit.id === item.unit_uid)?.unit_name || item.unit_uid,
      city_name: citiesData.find(city => city.id === item.city_uid)?.city_name || item.city_uid
    })) || []

  return {
    ...itemsQuery,
    data: transformedData
  }
}
