import ExcelJS from 'exceljs'

import type { Item } from '@/lib/item-api'
import type { ItemType } from '@/lib/item-types-api'

/**
 * Fixed source mapping for price by source configuration
 */
const FIXED_SOURCE_MAPPING = [
  { sourceId: '10000045', name: '<PERSON><PERSON><PERSON>' },
  { sourceId: '10000049', name: 'FACEBOOK' },
  { sourceId: '10000134', name: 'SO' },
  { sourceId: '10000162', name: 'CRM' },
  { sourceId: '10000165', name: 'VNPAY' },
  { sourceId: '10000168', name: 'GOJE<PERSON> (GOVIET)' },
  { sourceId: '10000169', name: 'ShopeeFood' },
  { sourceId: '10000171', name: 'MANG VỀ' },
  { sourceId: '10000172', name: 'TẠI CHỖ' },
  { sourceId: '10000176', name: 'CALL CENTER' },
  { sourceId: '10000216', name: 'O2<PERSON>' },
  { sourceId: '10000253', name: 'BEFOOD' }
]

/**
 * Create a new Excel workbook for price by source configuration
 */
export const createPriceBySourceWorkbook = (): ExcelJS.Workbook => {
  const workbook = new ExcelJS.Workbook()
  workbook.creator = 'POS System'
  workbook.lastModifiedBy = 'POS System'
  workbook.created = new Date()
  workbook.modified = new Date()
  return workbook
}

/**
 * Generate headers for price by source template
 * Using fixed source mapping instead of dynamic sources
 */
export const getPriceBySourceHeaders = (): string[] => {
  const baseHeaders = [
    'item_uid',
    'item_id',
    'item_name',
    'Nhóm món',
    'Giá gốc',
    'Vat (%)'
  ]

  // Add fixed source columns
  const sourceHeaders = FIXED_SOURCE_MAPPING.map(source =>
    `${source.name} [${source.sourceId}]`
  )

  return [...baseHeaders, ...sourceHeaders]
}

/**
 * Generate data rows for price by source template
 * Map prices from item.price_by_source data and group by item type
 */
export const generatePriceBySourceData = (
  items: Item[],
  itemTypes: ItemType[]
): any[][] => {
  console.log('🔍 Debug: Total items received:', items.length)

  // First, create item data with type information
  const itemsWithType = items.map(item => {
    // Find item type name
    const itemType = itemTypes.find(type => type.id === item.item_type_uid)
    const itemTypeName = itemType?.item_type_name || 'Uncategory'

    // Debug logging for specific item
    if (item.id === 'a67c1ae9-31f6-4ef2-898d-32e3b7f8363e') {
      console.log('🎯 Debug: Found target item:', {
        id: item.id,
        name: item.item_name,
        itemTypeName,
        price_by_source: item.extra_data?.price_by_source,
        extra_data: item.extra_data,
        rawItem: item
      })
    }

    return {
      item,
      itemTypeName
    }
  })

  // Group items by item type name
  const groupedItems = itemsWithType.reduce((groups, { item, itemTypeName }) => {
    if (!groups[itemTypeName]) {
      groups[itemTypeName] = []
    }
    groups[itemTypeName].push({ item, itemTypeName })
    return groups
  }, {} as Record<string, Array<{ item: Item; itemTypeName: string }>>)

  // Sort group names alphabetically
  const sortedGroupNames = Object.keys(groupedItems).sort()

  console.log('📊 Debug: Groups found:', sortedGroupNames)

  const data: any[][] = []

  // Process each group
  sortedGroupNames.forEach(groupName => {
    const groupItems = groupedItems[groupName]

    // Sort items within group by item name
    groupItems.sort((a, b) => a.item.item_name.localeCompare(b.item.item_name))

    // Process each item in the group
    groupItems.forEach(({ item, itemTypeName }) => {
      // Create base row data
      const row = [
        item.id, // item_uid
        item.item_id, // item_id
        item.item_name, // item_name
        itemTypeName, // Nhóm món
        item.ots_price || 0, // Giá gốc
        (item.ots_tax || 0) * 100 // Vat (%) - convert from decimal to percentage
      ]

      // Map prices from price_by_source data for each fixed source
      FIXED_SOURCE_MAPPING.forEach(sourceMapping => {
        // Find price for this source from item.extra_data.price_by_source
        const priceBySource = item.extra_data?.price_by_source?.find(
          pbs => pbs.source_id === sourceMapping.sourceId
        )

        // Debug logging for FACEBOOK source specifically
        if (sourceMapping.sourceId === '10000049' && item.id === 'a67c1ae9-31f6-4ef2-898d-32e3b7f8363e') {
          console.log('📘 Debug: FACEBOOK mapping for target item:', {
            sourceMapping,
            priceBySource,
            allPriceBySource: item.extra_data?.price_by_source,
            finalPrice: priceBySource?.price || ''
          })
        }

        // Add price if found, otherwise empty string
        row.push(priceBySource?.price || '')
      })

      data.push(row)
    })
  })

  console.log('✅ Debug: Generated data rows:', data.length)
  return data
}

/**
 * Create price by source worksheet
 */
export const createPriceBySourceWorksheet = (
  workbook: ExcelJS.Workbook,
  items: Item[],
  itemTypes: ItemType[]
): ExcelJS.Worksheet => {
  const worksheet = workbook.addWorksheet('Sheet')

  // Generate headers and data
  const headers = getPriceBySourceHeaders()
  const data = generatePriceBySourceData(items, itemTypes)

  // Add header row
  const headerRow = worksheet.addRow(headers)
  
  // Style header row
  headerRow.eachCell((cell: any) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF0560A6' }
    }
    cell.font = {
      color: { argb: 'FFFFFFFF' },
      bold: true,
      size: 11
    }
    cell.alignment = {
      horizontal: 'center',
      vertical: 'middle'
    }
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } }
    }
  })

  // Add data rows
  data.forEach(rowData => {
    const dataRow = worksheet.addRow(rowData)
    
    // Style data rows
    dataRow.eachCell((cell: any, colNumber: number) => {
      cell.font = { size: 10 }
      cell.border = {
        top: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        left: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        bottom: { style: 'thin', color: { argb: 'FFD0D0D0' } },
        right: { style: 'thin', color: { argb: 'FFD0D0D0' } }
      }

      // Make item_uid, item_id, item_name columns read-only by styling them differently
      if (colNumber <= 3) {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFF0F0F0' } // Light gray background for read-only columns
        }
        cell.font = { 
          size: 10,
          color: { argb: 'FF666666' } // Gray text for read-only columns
        }
      }
    })
  })

  // Set column widths
  const columnWidths = [
    40, // item_uid
    15, // item_id
    25, // item_name
    15, // Nhóm món
    12, // Giá gốc
    10, // Vat (%)
    ...FIXED_SOURCE_MAPPING.map(() => 15) // Source columns
  ]
  
  columnWidths.forEach((width, index) => {
    worksheet.getColumn(index + 1).width = width
  })

  return worksheet
}

/**
 * Generate and download Excel file for price by source configuration
 */
export const generatePriceBySourceExcelFile = async (
  items: Item[],
  itemTypes: ItemType[],
  storeName: string
): Promise<void> => {
  try {
    // Create workbook
    const workbook = createPriceBySourceWorkbook()

    // Create worksheet with data
    createPriceBySourceWorksheet(workbook, items, itemTypes)

    // Generate Excel file
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `price_by_source_config_${storeName}_${timestamp}.xlsx`
    
    // Download file
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    return Promise.resolve()
  } catch (error) {
    console.error('Error creating price by source Excel file:', error)
    return Promise.reject(error)
  }
}
