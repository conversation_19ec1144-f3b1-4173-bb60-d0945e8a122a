import { useState, useEffect } from 'react'
import { Role } from '@/types/role'
import { useRoleById } from '@/hooks/api/use-roles'

import { getAllPermissions } from '../utils'

interface UsePermissionsProps {
  roleData?: Role
  isEditMode?: boolean
  copyFromRoleId?: string
}

export const usePermissions = ({ roleData, isEditMode, copyFromRoleId }: UsePermissionsProps = {}) => {
  const [permissions, setPermissions] = useState<string[]>([])

  const isCopyMode = !!copyFromRoleId
  const { data: copyFromRoleData } = useRoleById(copyFromRoleId || '', isCopyMode)

  useEffect(() => {
    if (isEditMode && roleData) {
      const existingPermissions = [
        ...(roleData.allow_access || []),
        ...getAllPermissions().filter(p =>
          !['POS_CMS', 'POS_CLIENT', 'POS_MANAGER'].includes(p) &&
          !(roleData.reject_permissions || []).includes(p)
        )
      ]
      setPermissions(existingPermissions)
    } else if (isCopyMode && copyFromRoleData) {
      // For copy mode, use permissions from the source role
      const copyPermissions = [
        ...(copyFromRoleData.allow_access || []),
        ...getAllPermissions().filter(p =>
          !['POS_CMS', 'POS_CLIENT', 'POS_MANAGER'].includes(p) &&
          !(copyFromRoleData.reject_permissions || []).includes(p)
        )
      ]
      setPermissions(copyPermissions)
    } else {
      setPermissions(getAllPermissions())
    }
  }, [isEditMode, roleData, isCopyMode, copyFromRoleData])

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (checked) {
      setPermissions(prev => [...prev, permission])
    } else {
      setPermissions(prev => prev.filter(p => p !== permission))
    }
  }

  const handleParentPermissionChange = (
    _parentId: string,
    checked: boolean,
    childPermissions: string[]
  ) => {
    setPermissions(prev => {
      let newPermissions = [...prev]

      if (checked) {
        // Add all child permissions
        childPermissions.forEach(childPerm => {
          if (!newPermissions.includes(childPerm)) {
            newPermissions.push(childPerm)
          }
        })
      } else {
        // Remove all child permissions
        newPermissions = newPermissions.filter(p => !childPermissions.includes(p))
      }

      return newPermissions
    })
  }

  const handleChildPermissionChange = (
    childId: string,
    checked: boolean,
    _parentId: string,
    _allChildPermissions: string[]
  ) => {
    setPermissions(prev => {
      let newPermissions = [...prev]

      if (checked) {
        if (!newPermissions.includes(childId)) {
          newPermissions.push(childId)
        }
      } else {
        newPermissions = newPermissions.filter(p => p !== childId)
      }

      return newPermissions
    })
  }

  return {
    permissions,
    setPermissions,
    handlePermissionChange,
    handleParentPermissionChange,
    handleChildPermissionChange
  }
}
