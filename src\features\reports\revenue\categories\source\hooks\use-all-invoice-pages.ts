import { useState, useEffect, useMemo } from 'react'

import { useInvoiceSourceDetail } from './use-invoice-source-detail'

interface InvoiceEntry {
  id: string
  tran_id: string
  invoiceCode: string
  partnerInvoiceNumber: string
  branchName: string
  sourceType: string
  amount: number
  dateTime: string
  items?: Array<{
    name: string
    item_name?: string
    quantity: number
    price: number
    toppings?: Array<{
      name: string
      item_name?: string
    }>
    change_data?: {
      sale_detail: Array<{
        name: string
        item_name?: string
      }>
    }
  }>
  paymentMethod?: string
  paymentReference?: string
  employeeName?: string
  tranDate?: number
}

interface UseAllInvoicePagesParams {
  companyUid?: string
  brandUid?: string
  startDate?: number
  endDate?: number
  sourceId?: string
  storeUids?: string[]
  fallbackStartDate: number
  fallbackEndDate: number
  shouldFetchApi: boolean
}

export const useAllInvoicePages = ({
  companyUid,
  brandUid,
  startDate,
  endDate,
  sourceId,
  storeUids,
  fallbackStartDate,
  fallbackEndDate,
  shouldFetchApi
}: UseAllInvoicePagesParams) => {
  const [page, setPage] = useState(1)
  const [allData, setAllData] = useState<InvoiceEntry[]>([])
  const [hasError, setHasError] = useState<unknown>(null)
  const [hasMorePages, setHasMorePages] = useState(true)

  const apiParams = useMemo(() => {
    const params = {
      company_uid: companyUid || '',
      brand_uid: brandUid || '',
      start_date: startDate || fallbackStartDate,
      end_date: endDate || fallbackEndDate,
      source_id: sourceId || '',
      list_store_uid: storeUids || [],
      page,
      results_per_page: 20
    }
    return params
  }, [companyUid, brandUid, startDate, endDate, sourceId, storeUids, fallbackStartDate, fallbackEndDate, page])

  const { data: apiData, isLoading, error } = useInvoiceSourceDetail(apiParams, shouldFetchApi && hasMorePages)

  useEffect(() => {
    if (apiData?.data && Array.isArray(apiData.data)) {
      const newInvoices: InvoiceEntry[] = apiData.data.map(invoice => {
        const items = (invoice.sale_detail || []).map((item: any) => ({
          name: item.item_name || item.name || 'N/A',
          item_name: item.item_name || item.name || 'N/A',
          quantity: item.quantity || 0,
          price: item.price || 0,
          toppings: item.toppings || [],
          change_data: item.change_data || { sale_detail: [] }
        }))

        const paymentMethod = invoice.sale_payment_method?.[0]?.payment_method_name || 'N/A'
        const paymentReference = invoice.sale_payment_method?.[0]?.trace_no || ''
        const partnerInvoiceNumber = invoice.extra_data?.tran_no_partner || invoice.tran_no || 'N/A'

        const formatDate = (timestamp: number) => {
          if (!timestamp) return new Date().toISOString()
          const date = new Date(timestamp)
          return date.toLocaleString('vi-VN', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })
        }

        return {
          id: invoice.id || `invoice-${Math.random()}`,
          tran_id: invoice.tran_id || '',
          invoiceCode: invoice.tran_no || 'N/A',
          partnerInvoiceNumber: partnerInvoiceNumber,
          branchName: invoice.table_name || 'N/A',
          sourceType: invoice.source_deli || 'N/A',
          amount: invoice.total_amount || 0,
          dateTime: formatDate(invoice.tran_date),
          items: items,
          paymentMethod: paymentMethod,
          paymentReference: paymentReference,
          employeeName: invoice.employee_name || 'Ca 1 Sekai Bà Hạt',
          tranDate: invoice.tran_date
        }
      })

      if (page === 1) {
        setAllData(newInvoices)
      } else {
        setAllData(prev => [...prev, ...newInvoices])
      }

      if (newInvoices.length === 0) {
        setHasMorePages(false)
      } else if (newInvoices.length < 20) {
        setHasMorePages(false)
      } else {
        setTimeout(() => {
          setPage(prev => prev + 1)
        }, 100)
      }
    }
  }, [apiData, page])

  useEffect(() => {
    if (shouldFetchApi) {
      setPage(1)
      setAllData([])
      setHasMorePages(true)
      setHasError(null)
    }
  }, [shouldFetchApi, sourceId, companyUid, brandUid, startDate, endDate])

  useEffect(() => {
    if (error) {
      setHasError(error)
      setHasMorePages(false)
    }
  }, [error])

  return {
    invoices: allData,
    isLoading: isLoading || (page === 1 && allData.length === 0),
    error: hasError,
    hasMorePages,
    currentPage: page,
    totalInvoices: allData.length
  }
}

export type { InvoiceEntry }
