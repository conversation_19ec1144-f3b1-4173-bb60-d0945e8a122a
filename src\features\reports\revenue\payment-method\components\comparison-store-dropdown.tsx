import React, { useState, useMemo } from 'react'

import { usePosCitiesData } from '@/hooks'
import { usePosStores } from '@/stores'
import { ChevronDown } from 'lucide-react'

import { cn } from '@/lib/utils'

import { Combobox } from '@/components/pos/combobox'
import {
  Button,
  Checkbox,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Badge
} from '@/components/ui'

interface ComparisonStoreDropdownProps {
  selectedCities: string[]
  selectedStores: string[]
  comparisonCities: string[]
  comparisonStores: string[]
  onComparisonCitiesChange: (cities: string[]) => void
  onComparisonStoresChange: (stores: string[]) => void
  className?: string
}

export const ComparisonStoreDropdown: React.FC<ComparisonStoreDropdownProps> = ({
  selectedCities,
  selectedStores,
  comparisonCities,
  comparisonStores,
  onComparisonCitiesChange,
  onComparisonStoresChange,
  className
}) => {
  const [open, setOpen] = useState(false)
  const [expandedCities, setExpandedCities] = useState<Set<string>>(new Set())
  const [storeTypeFilter, setStoreTypeFilter] = useState<'all' | 'chain' | 'franchise'>('all')
  const [searchTerm, _setSearchTerm] = useState('')

  const { cities } = usePosCitiesData()
  const { currentBrandStores } = usePosStores()

  // Get all selected store IDs (from both cities and individual stores)
  const allSelectedStoreIds = useMemo(() => {
    const cityStoreIds = selectedCities.flatMap(cityId => {
      const cityStores = currentBrandStores.filter(store => store.city_uid === cityId)
      return cityStores.map(store => store.id || store.store_id)
    })
    return [...cityStoreIds, ...selectedStores]
  }, [selectedCities, selectedStores, currentBrandStores])

  const getAvailableStoresByCity = (cityId: string) => {
    return currentBrandStores
      .filter(store => store.city_uid === cityId)
      .filter(store => {
        const storeId = store.id || store.store_id
        return !allSelectedStoreIds.includes(storeId)
      })
      .filter(store => {
        if (storeTypeFilter === 'all') return true
        if (storeTypeFilter === 'chain') return (store as any).is_franchise === 0
        return (store as any).is_franchise === 1
      })
  }

  const toggleCityExpansion = (cityId: string) => {
    const newExpanded = new Set(expandedCities)
    if (newExpanded.has(cityId)) {
      newExpanded.delete(cityId)
    } else {
      newExpanded.add(cityId)
    }
    setExpandedCities(newExpanded)
  }

  const handleCityToggle = (cityId: string, checked: boolean) => {
    if (checked) {
      onComparisonCitiesChange([...comparisonCities, cityId])
    } else {
      onComparisonCitiesChange(comparisonCities.filter(id => id !== cityId))
      const cityStores = getAvailableStoresByCity(cityId)
      const cityStoreIds = cityStores.map(store => store.id || store.store_id)
      onComparisonStoresChange(comparisonStores.filter(storeId => !cityStoreIds.includes(storeId)))
    }
  }

  const handleStoreToggle = (storeId: string, checked: boolean) => {
    const store = currentBrandStores.find(s => (s.id || s.store_id) === storeId)
    const cityId = store?.city_uid

    if (checked) {
      const newComparisonStores = [...comparisonStores, storeId]
      onComparisonStoresChange(newComparisonStores)

      if (cityId) {
        const cityStores = getAvailableStoresByCity(cityId)
        const cityStoreIds = cityStores.map(s => s.id || s.store_id)
        const allCityStoresSelected = cityStoreIds.every(
          id => newComparisonStores.includes(id) || comparisonCities.includes(cityId)
        )

        if (allCityStoresSelected && cityStores.length > 0) {
          const storesWithoutCity = newComparisonStores.filter(id => !cityStoreIds.includes(id))
          onComparisonStoresChange(storesWithoutCity)
          onComparisonCitiesChange([...comparisonCities, cityId])
        }
      }
    } else {
      if (cityId && comparisonCities.includes(cityId)) {
        const cityStores = getAvailableStoresByCity(cityId)
        const cityStoreIds = cityStores.map(s => s.id || s.store_id)
        const otherCityStores = cityStoreIds.filter(id => id !== storeId)

        onComparisonCitiesChange(comparisonCities.filter(id => id !== cityId))
        onComparisonStoresChange([...comparisonStores, ...otherCityStores])
      } else {
        onComparisonStoresChange(comparisonStores.filter(id => id !== storeId))
      }
    }
  }

  const isCitySelected = (cityId: string): boolean => {
    return comparisonCities.includes(cityId)
  }

  const isCityPartiallySelected = (cityId: string): boolean => {
    const cityStores = getAvailableStoresByCity(cityId)
    const cityStoreIds = cityStores.map(store => store.id || store.store_id)
    const selectedCityStores = comparisonStores.filter(storeId => cityStoreIds.includes(storeId))
    return selectedCityStores.length > 0 && selectedCityStores.length < cityStoreIds.length
  }

  const isStoreSelected = (storeId: string): boolean => {
    return comparisonStores.includes(storeId)
  }

  const selectedCount = useMemo(() => {
    const cityStoreCount = comparisonCities.reduce((count, cityId) => {
      const cityStores = getAvailableStoresByCity(cityId)
      return count + cityStores.length
    }, 0)

    const individualStoreCount = comparisonStores.filter(storeId => {
      const store = currentBrandStores.find((s: any) => (s.id || s.store_id) === storeId)
      if (!store) return true

      const cityId = store.city_uid
      return !comparisonCities.includes(cityId)
    }).length

    return cityStoreCount + individualStoreCount
  }, [comparisonCities, comparisonStores, currentBrandStores, getAvailableStoresByCity])

  const displayText = selectedCount > 0 ? `Đã chọn ${selectedCount} cửa hàng` : 'Chọn cửa hàng so sánh'

  const hasData = useMemo(() => {
    const needle = searchTerm.trim().toLowerCase()
    return cities.some(city => {
      const cityId = (city as any).id || (city as any).city_id
      const stores = getAvailableStoresByCity(cityId)
      if (!needle) return stores.length > 0
      return stores.some(s =>
        String((s as any).store_name || '')
          .toLowerCase()
          .includes(needle)
      )
    })
  }, [cities, currentBrandStores, storeTypeFilter, searchTerm, allSelectedStoreIds])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={open}
          className={cn('w-[320px] justify-between', className)}
        >
          <span className='truncate'>{displayText}</span>
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-80 p-0' align='start'>
        <div className='max-h-96 overflow-y-auto'>
          <div className='p-2'>
            <div className='mb-2 flex flex-col items-start justify-center gap-2'>
              <Combobox
                options={[
                  { value: 'all', label: 'Tất cả cửa hàng' },
                  { value: 'chain', label: 'Chuỗi' },
                  { value: 'franchise', label: 'Nhượng quyền' }
                ]}
                value={storeTypeFilter}
                onValueChange={v => setStoreTypeFilter(!v ? 'all' : (v as 'all' | 'chain' | 'franchise'))}
                placeholder='Loại cửa hàng'
                searchPlaceholder='Tìm kiếm...'
                className='w-full'
              />
            </div>

            {!hasData && (
              <div className='text-muted-foreground flex h-40 items-center justify-center text-lg font-semibold'>
                Không có dữ liệu
              </div>
            )}

            {hasData &&
              cities
                .filter(city => {
                  const cityId = city.id || city.city_id
                  const cityStores = getAvailableStoresByCity(cityId)
                  const needle = searchTerm.trim().toLowerCase()
                  if (!needle) return cityStores.length > 0
                  return cityStores.some(s =>
                    String((s as any).store_name || '')
                      .toLowerCase()
                      .includes(needle)
                  )
                })
                .map(city => {
                  const cityId = city.id || city.city_id
                  const needle = searchTerm.trim().toLowerCase()
                  const allCityStores = getAvailableStoresByCity(cityId)
                  const cityStores = needle
                    ? allCityStores.filter(s =>
                        String((s as any).store_name || '')
                          .toLowerCase()
                          .includes(needle)
                      )
                    : allCityStores
                  const isExpanded = needle ? true : expandedCities.has(cityId)
                  const isCityChecked = isCitySelected(cityId)
                  const isPartiallySelected = isCityPartiallySelected(cityId)

                  return (
                    <div key={cityId} className='mb-2'>
                      <Collapsible open={isExpanded} onOpenChange={() => toggleCityExpansion(cityId)}>
                        <div className='flex items-center space-x-2 rounded p-2 hover:bg-gray-50'>
                          <Checkbox
                            checked={isCityChecked}
                            ref={el => {
                              if (el && isPartiallySelected && !isCityChecked) {
                                const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement
                                if (checkbox) {
                                  checkbox.indeterminate = true
                                }
                              }
                            }}
                            onCheckedChange={checked => handleCityToggle(cityId, checked as boolean)}
                          />
                          <CollapsibleTrigger className='flex flex-1 items-center space-x-2 text-left'>
                            <span className='font-medium'>{city.city_name}</span>
                            <span className='text-muted-foreground text-sm'>({cityStores.length} cửa hàng)</span>
                            <ChevronDown
                              className={cn(
                                'ml-auto h-4 w-4 transition-transform',
                                isExpanded && 'rotate-180 transform'
                              )}
                            />
                          </CollapsibleTrigger>
                        </div>

                        <CollapsibleContent>
                          {isExpanded && (
                            <div className='ml-6 space-y-1'>
                              {cityStores.map(store => {
                                const storeId = store.id || store.store_id
                                const isChecked = isStoreSelected(storeId) || isCityChecked

                                return (
                                  <div
                                    key={storeId}
                                    className='flex items-center space-x-2 rounded p-2 hover:bg-gray-50'
                                  >
                                    <Checkbox
                                      checked={isChecked}
                                      onCheckedChange={checked => handleStoreToggle(storeId, checked as boolean)}
                                    />
                                    <span className='text-sm'>{store.store_name}</span>
                                    {typeof (store as any).is_franchise !== 'undefined' &&
                                      ((store as any).is_franchise === 1 ? (
                                        <Badge className='ml-2'>Nhượng quyền</Badge>
                                      ) : (
                                        <Badge className='ml-2'>Chuỗi</Badge>
                                      ))}
                                  </div>
                                )
                              })}
                            </div>
                          )}
                        </CollapsibleContent>
                      </Collapsible>
                    </div>
                  )
                })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
