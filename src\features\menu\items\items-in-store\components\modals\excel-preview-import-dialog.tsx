import { useState, useEffect } from 'react'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { useItemTypesData, useItemClassesData, useUnitsData, useStoreData } from '@/hooks/api'

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

import { useBulkCreateItemsInStore } from '../../hooks/use-create-item-in-store'
import type { BulkCreateItemInStoreRequest } from '../../hooks/use-create-item-in-store'

export interface ImportItem {
  id: string
  item_id: string
  city_name: string
  store_name: string
  item_name: string
  ots_price: number
  active: number
  item_id_barcode: string
  is_eat_with: number
  no_update_quantity_toping: number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any
}

interface ImportPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: ImportItem[]
  storeUid?: string
}

export function ImportPreviewDialog({ open, onOpenChange, data, storeUid }: ImportPreviewDialogProps) {
  const [importData, setImportData] = useState<ImportItem[]>(data)
  const [isProcessing, setIsProcessing] = useState(false)

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { bulkCreateItemsInStore, isBulkCreating } = useBulkCreateItemsInStore()

  // Get store data to extract city_uid
  const { data: storeData } = useStoreData(storeUid || '', !!storeUid)

  // Get reference data for mapping
  const { data: itemTypes = [] } = useItemTypesData({
    skip_limit: true,
    ...(storeUid && storeUid !== 'all' ? { store_uid: storeUid } : {})
  })
  const { data: itemClasses = [] } = useItemClassesData({
    skip_limit: true
  })
  const { data: units = [] } = useUnitsData()

  useEffect(() => {
    setImportData(data)
  }, [data])

  const handleConfirm = async () => {
    if (!storeUid || !company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin cửa hàng hoặc thương hiệu')
      return
    }

    if (importData.length === 0) {
      toast.error('Không có dữ liệu để import')
      return
    }

    setIsProcessing(true)

    try {
      // Transform ImportItem to BulkCreateItemInStoreRequest
      const transformedData: BulkCreateItemInStoreRequest[] = importData.map(item => {
        // Find unit by unit_id (mã đơn vị)
        const unit = units.find(u => u.unit_id === item.unit_id)

        // Find item type by ID or name
        const itemType = itemTypes.find(it =>
          it.item_type_id === item.item_type_id ||
          it.item_type_name === item.item_type_name
        )

        // Find item class by ID or name
        const itemClass = itemClasses.find(ic =>
          ic.item_class_id === item.item_class_id ||
          ic.item_class_name === item.item_class_name
        )

        return {
          store_uid: storeUid,
          apply_with_store: 2, // Default value from cURL
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          city_uid: storeData?.cityId || '', // Get city_uid from store data
          item_id: item.item_id,
          unit_uid: unit?.id || '', // Use unit ID if found
          ots_price: item.ots_price || 0,
          ta_price: item.ots_price || 0, // Same as ots_price
          ots_tax: (item.ots_tax || 0) / 100, // Convert percentage to decimal
          ta_tax: (item.ots_tax || 0) / 100, // Convert percentage to decimal
          item_name: item.item_name,
          item_id_barcode: item.item_id_barcode || '',
          is_eat_with: item.is_eat_with || 0,
          item_type_uid: itemType?.id || '',
          item_class_uid: itemClass?.id || '',
          description: item.description || '',
          item_id_mapping: String(item.sku || ''),
          time_cooking: (item.time_cooking || 0) * 60000, // Convert minutes to milliseconds
          time_sale_date_week: item.time_sale_date_week || 0,
          time_sale_hour_day: item.time_sale_hour_day || 0,
          sort: item.list_order || 1,
          image_path_thumb: '',
          image_path: item.image_path || '',
          extra_data: {
            no_update_quantity_toping: item.no_update_quantity_toping || 0,
            enable_edit_price: item.price_change || 0,
            is_virtual_item: item.is_virtual_item || 0,
            is_item_service: item.is_item_service || 0,
            is_buffet_item: item.is_buffet_item || 0
          }
        }
      })

      await bulkCreateItemsInStore(transformedData)
      onOpenChange(false)
    } catch (error) {
      console.error('Error creating items:', error)
      toast.error('Có lỗi xảy ra khi tạo món ăn')
    } finally {
      setIsProcessing(false)
    }
  }

  const columns = [
    { key: 'item_name', label: 'Tên', width: '200px' },
    { key: 'ots_price', label: 'Giá', width: '100px' },
    { key: 'item_id', label: 'Mã món', width: '120px' },
    { key: 'item_id_barcode', label: 'Mã barcode', width: '120px' },
    { key: 'is_eat_with', label: 'Món ăn kèm', width: '120px' },
    { key: 'no_update_quantity_toping', label: 'Không cập nhật số lượng món ăn kèm', width: '220px' },
    { key: 'item_type_id', label: 'Nhóm', width: '120px' },
    { key: 'item_class_id', label: 'Loại món', width: '120px' },
    { key: 'description', label: 'Mô tả', width: '200px' },
    { key: 'sku', label: 'SKU', width: '100px' },
    { key: 'unit_id', label: 'Đơn vị', width: '100px' },
    { key: 'ots_tax', label: 'VAT (%)', width: '80px' },
    { key: 'time_cooking', label: 'Thời gian chế biến (phút)', width: '180px' },
    { key: 'price_change', label: 'Cho phép sửa giá khi bán', width: '180px' },
    { key: 'is_virtual_item', label: 'Cấu hình món ảo', width: '150px' },
    { key: 'is_item_service', label: 'Cấu hình món dịch vụ', width: '180px' },
    { key: 'is_buffet_item', label: 'Cấu hình món ăn là vé buffet', width: '200px' },
    { key: 'time_sale_date_week', label: 'Ngày', width: '80px' },
    { key: 'time_sale_hour_day', label: 'Giờ', width: '80px' },
    { key: 'image_path', label: 'Hình ảnh', width: '120px' },
    { key: 'inqr_formula', label: 'Công thức inQR cho máy pha trà', width: '220px' },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-h-[90vh] max-w-7xl sm:max-w-4xl'>
        <DialogHeader className='flex flex-row items-center justify-between space-y-0 pb-4'>
          <DialogTitle className='text-xl font-semibold'>Thêm mới</DialogTitle>
        </DialogHeader>

        <div className='space-y-4 overflow-hidden'>
          <ScrollArea>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className='w-16 text-center'>Thứ tự</TableHead>
                  {columns.map(column => (
                    <TableHead key={column.key} style={{ width: column.width }}>
                      {column.label}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {importData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className='text-center font-medium'>
                      {index + 1}
                    </TableCell>
                    {columns.map(column => (
                      <TableCell key={column.key} style={{ width: column.width }}>
                        {column.key === 'ots_price' ? (
                          <span className='text-right'>
                            {item[column.key]?.toLocaleString('vi-VN')} ₫
                          </span>
                        ) : column.key === 'active' ? (
                          <span>{item[column.key]}</span>
                        ) : column.key === 'item_id' || column.key === 'item_id_barcode' ? (
                          <span className='font-mono text-sm'>{item[column.key]}</span>
                        ) : column.key === 'item_name' ? (
                          <span className='font-medium'>{item[column.key]}</span>
                        ) : column.key === 'is_eat_with' ||
                          column.key === 'no_update_quantity_toping' ||
                          column.key === 'price_change' ||
                          column.key === 'is_virtual_item' ||
                          column.key === 'is_item_service' ||
                          column.key === 'is_buffet_item' ? (
                          <span className='text-center'>{item[column.key]}</span>
                        ) : (
                          <span>{item[column.key] || ''}</span>
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <ScrollBar orientation='horizontal' />
          </ScrollArea>

          <div className='flex items-center justify-between border-t pt-4'>
            <Button variant='outline' onClick={() => onOpenChange(false)}>
              Đóng
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={isProcessing || isBulkCreating || !storeUid}
            >
              {isProcessing || isBulkCreating ? 'Đang lưu...' : 'Lưu'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
