import { useEffect, useState, useMemo, useCallback } from 'react'

import { GripVertical, X as CloseIcon } from 'lucide-react'

import { formatNumberDisplay, parseNumberInput } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'

import { useGroupItemsDragDrop } from '../hooks/use-group-items-drag-drop'
import { ItemSelectionDialog } from './item-selection-dialog'

interface GroupCreateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (data: {
    name: string;
    required: number;
    max: number;
    items: string[];
    itemSettings: Record<string, { finalPrice: number; discountPercent: number; pricingMode: 'custom' | 'base' }>
  }) => void
  onCancel: () => void
  items?: import('@/lib/item-api').Item[]
  selectedItemIds?: string[]
  initialName?: string
  initialRequired?: number
  initialMax?: number
  initialItemSettings?: Record<string, { finalPrice: number; discountPercent: number; pricingMode: 'custom' | 'base' }>
}

export function GroupCreateDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  items = [],
  selectedItemIds = [],
  initialName,
  initialRequired,
  initialMax,
  initialItemSettings
}: GroupCreateDialogProps) {
  const [groupName, setGroupName] = useState(initialName || '')
  const [required, setRequired] = useState<number>(initialRequired ?? 0)
  const [max, setMax] = useState<number>(initialMax ?? 0)
  const [itemDialogOpen, setItemDialogOpen] = useState(false)
  const [selectedItems, setSelectedItems] = useState<string[]>(selectedItemIds)
  const [itemSettings, setItemSettings] = useState<Record<string, { finalPrice: number; discountPercent: number }>>({})
  const [pricingModes, setPricingModes] = useState<Record<string, 'custom' | 'base'>>({}) // 'custom' = Sửa giá, 'base' = Giá gốc
  const [nameError, setNameError] = useState('')

  const { draggedIndex, handleDragStart, handleDragOver, handleDrop } = useGroupItemsDragDrop()

  const stableSelectedItemIds = useMemo(() => selectedItemIds, [selectedItemIds?.join(',')])

  useEffect(() => {
    if (open) {
      setGroupName(initialName || '')
      setRequired(initialRequired ?? 0)
      setMax(initialMax ?? 0)
      setSelectedItems(stableSelectedItemIds)
      setItemSettings(prev => {
        const next: Record<string, { finalPrice: number; discountPercent: number }> = {}
        stableSelectedItemIds.forEach(id => {
          const found = items.find(i => i.id === id)
          const base = found?.ots_price ?? 0
          if (initialItemSettings?.[id]) {
            next[id] = {
              finalPrice: initialItemSettings[id].finalPrice,
              discountPercent: initialItemSettings[id].discountPercent
            }
          } else {
            next[id] = prev[id] ?? { finalPrice: base, discountPercent: 0 }
          }
        })
        return next
      })

      setPricingModes(prev => {
        const next: Record<string, 'custom' | 'base'> = {}
        stableSelectedItemIds.forEach(id => {
          if (initialItemSettings?.[id]) {
            next[id] = initialItemSettings[id].pricingMode
          } else {
            next[id] = prev[id] ?? 'custom'
          }
        })
        return next
      })
    }
  }, [open, initialName, initialRequired, initialMax, stableSelectedItemIds, items, initialItemSettings])

  const validateForm = () => {
    const trimmedName = groupName.trim()
    if (!trimmedName) {
      setNameError('Tên nhóm là bắt buộc')
      return false
    }
    setNameError('')
    return true
  }

  const handleConfirm = useCallback(() => {
    if (!validateForm()) {
      return
    }

    const finalItemSettings: Record<string, { finalPrice: number; discountPercent: number; pricingMode: 'custom' | 'base' }> = {}
    selectedItems.forEach(itemId => {
      const settings = itemSettings[itemId]
      const mode = pricingModes[itemId] || 'custom'
      finalItemSettings[itemId] = {
        finalPrice: settings?.finalPrice || 0,
        discountPercent: settings?.discountPercent || 0,
        pricingMode: mode
      }
    })

    onConfirm({
      name: groupName.trim(),
      required,
      max,
      items: selectedItems,
      itemSettings: finalItemSettings
    })
    setGroupName('')
    setRequired(0)
    setMax(0)
    setNameError('')
  }, [validateForm, onConfirm, groupName, required, max, selectedItems])

  const handlePricingModeChange = useCallback((itemId: string, mode: 'custom' | 'base', basePrice: number) => {
    setPricingModes(prev => ({ ...prev, [itemId]: mode }))

    if (mode === 'base') {
      setItemSettings(prev => ({
        ...prev,
        [itemId]: { finalPrice: basePrice, discountPercent: 0 }
      }))
    } else if (mode === 'custom') {
      setItemSettings(prev => ({
        ...prev,
        [itemId]: { ...prev[itemId], discountPercent: 0 }
      }))
    }
  }, [])

  const handleDiscountChange = useCallback((itemId: string, percent: number, basePrice: number, mode: 'custom' | 'base') => {
    if (mode === 'base') {
      const newFinalPrice = basePrice * (1 - percent / 100)
      setItemSettings(prev => ({
        ...prev,
        [itemId]: { finalPrice: newFinalPrice, discountPercent: percent }
      }))
    }
  }, [])

  const handleFinalPriceChange = useCallback((itemId: string, value: number, basePrice: number, mode: 'custom' | 'base') => {
    if (mode === 'custom') {
      setItemSettings(prev => ({
        ...prev,
        [itemId]: { ...prev[itemId], finalPrice: value }
      }))
    } else {
      const newDiscountPercent = basePrice > 0 ? ((basePrice - value) / basePrice) * 100 : 0
      setItemSettings(prev => ({
        ...prev,
        [itemId]: { finalPrice: value, discountPercent: Math.max(0, newDiscountPercent) }
      }))
    }
  }, [])

  const handleRemoveItem = useCallback((itemId: string) => {
    setSelectedItems(prev => prev.filter(x => x !== itemId))
    setItemSettings(prev => {
      const next = { ...prev }
      delete next[itemId]
      return next
    })
    setPricingModes(prev => {
      const next = { ...prev }
      delete next[itemId]
      return next
    })
  }, [])

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setGroupName(value)
    if (nameError && value.trim()) {
      setNameError('')
    }
  }

  const handleClose = useCallback((nextOpen: boolean) => {
    if (!nextOpen) {
      setGroupName('')
      setRequired(0)
      setMax(0)
      setNameError('')
      setPricingModes({}) // Reset pricing modes
      onCancel()
    }
    onOpenChange(nextOpen)
  }, [onCancel, onOpenChange])

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className='max-w-4xl lg:max-w-4xl'>
        <DialogHeader>
          <DialogTitle className='text-center'>Tạo nhóm</DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          <div className='space-y-2'>
            <Input
              placeholder='Tên nhóm *'
              value={groupName}
              onChange={handleNameChange}
              className={nameError ? 'border-red-500' : ''}
            />
            {nameError && <p className='text-sm text-red-500'>{nameError}</p>}
          </div>

          <div className='rounded-md border'>
            <table className='w-full text-sm'>
              <tbody>
                <tr className='border-b bg-gray-50'>
                  <td className='w-1/3 p-4 font-medium text-gray-700'>Yêu cầu</td>
                  <td className='p-4'>
                    <Input
                      type='number'
                      value={required}
                      onChange={e => setRequired(parseInt(e.target.value || '0', 10))}
                      className='w-full'
                    />
                  </td>
                </tr>
                <tr>
                  <td className='w-1/3 p-4 font-medium text-gray-700'>Tối đa</td>
                  <td className='p-4'>
                    <Input
                      type='number'
                      value={max}
                      onChange={e => setMax(parseInt(e.target.value || '0', 10))}
                      className='w-full'
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className='space-y-3'>
            <h3 className='text-lg font-medium'>Danh sách món</h3>
            <div className='max-h-96 overflow-auto rounded-md border'>
              <table className='w-full text-sm'>
                <thead className='sticky top-0 bg-gray-50'>
                  <tr className='text-gray-700'>
                    <th className='w-8 p-3'></th>
                    <th className='p-3 text-left font-medium'>Tên</th>
                    <th className='p-3 text-left font-medium'>Lựa chọn giá</th>
                    <th className='p-3 text-left font-medium'>Giá gốc</th>
                    <th className='p-3 text-left font-medium'>Giảm giá (%)</th>
                    <th className='p-3 text-left font-medium'>Giá sau điều chỉnh</th>
                    <th className='w-8 p-3'></th>
                  </tr>
                </thead>
                <tbody>
                  {selectedItems.length > 0 &&
                    selectedItems.map((id, index) => {
                      const found = items.find(i => i.id === id)
                      const basePrice = found?.ots_price ?? 0
                      const discountPercent = itemSettings[id]?.discountPercent ?? 0
                      const finalPrice = itemSettings[id]?.finalPrice ?? basePrice
                      const isDragging = draggedIndex === index
                      const currentMode = pricingModes[id] || 'custom'

                      return (
                        <tr
                          key={id}
                          draggable
                          onDragStart={e => handleDragStart(e, index)}
                          onDragOver={handleDragOver}
                          onDrop={e => handleDrop(e, index, selectedItems, setSelectedItems)}
                          className={`border-t transition-all duration-200 ${
                            isDragging
                              ? 'scale-105 opacity-50 bg-blue-50'
                              : 'cursor-move hover:bg-gray-50'
                          }`}
                        >
                          <td className='p-3 text-center'>
                            <div className='flex items-center justify-center'>
                              <GripVertical className='h-4 w-4 text-gray-400' />
                            </div>
                          </td>
                          <td className='p-3'>
                            {found?.item_name} {found?.item_id ? `(${found.item_id})` : ''}
                          </td>
                          <td className='p-3'>
                            <div className='flex items-center gap-2'>
                              <Button
                                type='button'
                                variant={currentMode === 'custom' ? 'default' : 'outline'}
                                className={currentMode === 'custom' ? 'bg-blue-600 text-white hover:bg-blue-700' : ''}
                                onClick={() => handlePricingModeChange(id, 'custom', basePrice)}
                              >
                                Sửa giá
                              </Button>
                              <Button
                                type='button'
                                variant={currentMode === 'base' ? 'default' : 'outline'}
                                className={currentMode === 'base' ? 'bg-blue-600 text-white hover:bg-blue-700' : ''}
                                onClick={() => handlePricingModeChange(id, 'base', basePrice)}
                              >
                                Giá gốc
                              </Button>
                            </div>
                          </td>
                          <td className='p-3'>{new Intl.NumberFormat('vi-VN').format(basePrice)} đ</td>
                          <td className='p-3'>
                            {currentMode === 'custom' ? (
                              <div className='flex items-center justify-center text-gray-400'>
                                <span className='text-sm'>—</span>
                              </div>
                            ) : (
                              <div className='flex items-center gap-1'>
                                <Input
                                  type='text'
                                  className='w-20'
                                  value={formatNumberDisplay(Math.round(discountPercent))}
                                  onChange={e => {
                                    const cleanValue = parseNumberInput(e.target.value)
                                    const percent = Math.min(100, Math.max(0, Number(cleanValue || 0)))
                                    handleDiscountChange(id, percent, basePrice, currentMode)
                                  }}
                                  placeholder='0'
                                />
                                <span className='text-sm text-gray-500'>%</span>
                              </div>
                            )}
                          </td>
                          <td className='p-3'>
                            <Input
                              type='text'
                              className='w-28'
                              value={Math.round(finalPrice) === 0 ? '' : formatNumberDisplay(Math.round(finalPrice))}
                              onChange={e => {
                                const cleanValue = parseNumberInput(e.target.value)
                                const numericValue = Number(cleanValue || 0)
                                handleFinalPriceChange(id, numericValue, basePrice, currentMode)
                              }}
                              placeholder='0'
                            />
                          </td>
                          <td className='p-3 text-right'>
                            <button
                              type='button'
                              aria-label='Remove'
                              onClick={() => handleRemoveItem(id)}
                              className='text-gray-500 hover:text-gray-800'
                            >
                              <CloseIcon className='h-4 w-4' />
                            </button>
                          </td>
                        </tr>
                      )
                    })}

                  <tr>
                    <td colSpan={7} className='p-4'>
                      <Button variant='ghost' className='text-blue-600' onClick={() => setItemDialogOpen(true)}>
                        Thêm món
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button type='button' variant='outline' onClick={() => handleClose(false)}>
            Hủy
          </Button>
          <Button type='button' onClick={handleConfirm} disabled={!groupName.trim()}>
            Xong
          </Button>
        </DialogFooter>

        {/* Item Selection Dialog */}
        <ItemSelectionDialog
          open={itemDialogOpen}
          onOpenChange={setItemDialogOpen}
          items={items}
          selectedItems={selectedItems}
          onConfirm={ids => {
            setSelectedItems(ids)
            // Initialize finalPrice for each added item to its base price
            setItemSettings(prev => {
              const next = { ...prev }
              ids.forEach(itemId => {
                if (next[itemId] === undefined) {
                  const found = items.find(i => i.id === itemId)
                  const base = found?.ots_price ?? 0
                  next[itemId] = { finalPrice: base, discountPercent: 0 }
                }
              })
              return next
            })
            setItemDialogOpen(false)
          }}
          onCancel={() => setItemDialogOpen(false)}
        />
      </DialogContent>
    </Dialog>
  )
}
