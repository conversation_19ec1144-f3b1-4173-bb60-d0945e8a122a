import { type SidebarData } from '../types'
import { businessNavItems } from './business-nav'
import { crmNavItems } from './crm-nav'
import { generalNavItems } from './general-nav'
import { menuNavItems } from './menu-nav'
import { reportsNavItems } from './reports-nav'
import { userData, fallbackTeamsData } from './user-teams'

export const sidebarData: SidebarData = {
  user: userData,
  teams: fallbackTeamsData, // This is now just fallback data
  navGroups: [
    {
      title: 'General',
      items: [...generalNavItems, ...menuNavItems, ...businessNavItems, ...reportsNavItems, ...crmNavItems]
    }
    // Hidden: Pages section
    // {
    //   title: 'Pages',
    //   items: pagesNavItems,
    // },
    // Hidden: Other section
    // {
    //   title: 'Other',
    //   items: settingsNavItems,
    // },
  ]
}
