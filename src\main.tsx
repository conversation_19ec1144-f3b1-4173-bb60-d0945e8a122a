import { StrictMode } from 'react'

import ReactDOM from 'react-dom/client'

import { AxiosError } from 'axios'

import { QueryCache, QueryClient, QueryClientProvider } from '@tanstack/react-query'

import { RouterProvider, createRouter } from '@tanstack/react-router'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'
import { initializePosStoreFromLocalStorage } from '@/stores/posStore'

import { handleServerError } from '@/utils/handle-server-error'

import { TooltipProvider } from '@/components/ui/tooltip'

import { FontProvider } from './context/font-context'
import { ThemeProvider } from './context/theme-context'
import './index.css'
import { routeTree } from './routeTree.gen'

if (import.meta.env.DEV) {
  import('@/utils/localStorage-debug')
}

initializePosStoreFromLocalStorage()

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        if (failureCount >= 0 && import.meta.env.DEV) return false
        if (failureCount > 3 && import.meta.env.PROD) return false
        return !(error instanceof AxiosError && [401, 403].includes(error.response?.status ?? 0))
      },
      refetchOnWindowFocus: import.meta.env.PROD,
      staleTime: 10 * 1000
    },
    mutations: {
      onError: error => {
        handleServerError(error)

        if (error instanceof AxiosError) {
          if (error.response?.status === 304) {
            toast.error('Content not modified!')
          }
        }
      }
    }
  },
  queryCache: new QueryCache({
    onError: error => {
      if (error instanceof AxiosError) {
        if (error.response?.status === 401) {
          toast.error('Session expired!')
          useAuthStore.getState().auth.reset()
          const redirect = `${router.history.location.href}`
          router.navigate({ to: '/sign-in', search: { redirect } })
        }
        if (error.response?.status === 500) {
          toast.error('Internal Server Error!')
          router.navigate({ to: '/500' })
        }
        if (error.response?.status === 403) {
        }
      }
    }
  })
})

const router = createRouter({
  routeTree,
  context: { queryClient },
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 0
})

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById('root')!
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme='light' storageKey='vite-ui-theme'>
          <FontProvider>
            <TooltipProvider>
              <RouterProvider router={router} />
            </TooltipProvider>
          </FontProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </StrictMode>
  )
}
