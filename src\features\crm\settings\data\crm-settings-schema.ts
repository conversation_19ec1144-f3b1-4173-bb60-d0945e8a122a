import { z } from 'zod'

export const crmSettingsSchema = z.object({
  // Cấu hình thương hiệu
  brandName: z.string().min(1, 'Tên thương hiệu là bắt buộc'),
  bannerImage: z.string().optional(),
  logo: z.string().optional(),
  hotline: z.string().min(1, 'Số điện thoại hotline là bắt buộc'),
  email: z.array(z.string().email('Email không hợp lệ')).min(1, 'Ít nhất một email là bắt buộc'),

  // C<PERSON><PERSON> hình đặt giao hàng
  representativeStore: z.string().min(1, 'Nhà hàng đại diện là bắt buộc'),
  serviceCharge: z.number().min(0, 'Phí dịch vụ phải lớn hơn hoặc bằng 0'),
  vat: z.number().min(0, 'VAT phải lớn hơn hoặc bằng 0'),

  // Chương trình thành viên
  enablePointAccumulation: z.boolean(),
  enableAutoMemberUpgrade: z.boolean(),
  autoUpgradeDays: z.number().optional(),
  resetPointsDays: z.number(),
  excludeInvoiceOrigins: z.array(z.string()),

  // Cảnh báo giao dịch bất thường
  enableAccountBalanceAlert: z.boolean(),
  balanceThreshold: z.number().optional(),
  enableAccountBalanceAlertVND: z.boolean(),
  balanceThresholdVND: z.number().optional(),
  transactionEmailType: z.enum(['regular', 'other']),
  transactionCustomEmails: z.array(z.string().email('Email không hợp lệ')).optional(),

  // Cảnh báo đơn hàng online
  onlineOrderEmailType: z.enum(['regular', 'point', 'other']),
  onlineOrderCustomEmails: z.array(z.string().email('Email không hợp lệ')).optional()
})

export const defaultCrmSettingsValues: z.infer<typeof crmSettingsSchema> = {
  brandName: 'Tutimi-Bình Lợi',
  bannerImage: 'https://image.foodbook.vn/upload/********/1753667987575_blob.jpeg',
  logo: 'https://image.foodbook.vn/upload/********/1755052729065_blob.jpeg',
  hotline: '33',
  email: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
  representativeStore: '',
  serviceCharge: 0.0102,
  vat: 0.01,
  enablePointAccumulation: true,
  enableAutoMemberUpgrade: true,
  autoUpgradeDays: 31,
  resetPointsDays: 30,
  excludeInvoiceOrigins: ['********', '********'],
  enableAccountBalanceAlert: true,
  balanceThreshold: 3,
  enableAccountBalanceAlertVND: true,
  balanceThresholdVND: 3,
  transactionEmailType: 'regular',
  transactionCustomEmails: [],
  onlineOrderEmailType: 'other',
  onlineOrderCustomEmails: ['<EMAIL>']
}
