import React, { useState } from 'react'

import { Camera, Plus, Trash, X } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'

import { MinimalTiptapEditor } from '@/components/minimal-tiptap'

import { BillTemplateConfig, ExtraBillTemplate3, ExtraBillTemplate4 } from '../types'
import { ImageSelectorDialog } from './image-selector-dialog'

interface BillConfigPanelProps {
  config: ExtraBillTemplate3 | ExtraBillTemplate4 | BillTemplateConfig
  onConfigChange: (config: any) => void
  onSave?: (logoFile?: File | null, isLogoRemoved?: boolean) => void
  isSaving?: boolean
  serverLogo?: string | null
  onLogoChange?: (logoUrl: string | null) => void
}

export function BillConfigPanel({
  config,
  onConfigChange,
  onSave,
  isSaving,
  serverLogo,
  onLogoChange
}: BillConfigPanelProps) {
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [localConfig, setLocalConfig] = useState<any>(config || {})
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false)
  const [logoUrl, setLogoUrl] = useState<string | null>(null)
  const [isLogoRemoved, setIsLogoRemoved] = useState(false)

  React.useEffect(() => {
    setLocalConfig(config || {})
  }, [config])

  React.useEffect(() => {
    if (serverLogo && !logoUrl) {
      setLogoUrl(serverLogo)
      onLogoChange?.(serverLogo)
    }
  }, [serverLogo])

  const handleConfigChange = (key: string, value: any) => {
    const nextConfig = {
      ...localConfig,
      [key]: value
    }
    setLocalConfig(nextConfig)
    onConfigChange(nextConfig)
  }

  const getFieldValue = (fieldName: string, defaultValue: any = '') => {
    return (localConfig as any)[fieldName] ?? defaultValue
  }

  const handleImageSelect = (file: File, useFullSize: boolean) => {
    setLogoFile(file)
    const newLogoUrl = URL.createObjectURL(file)
    setLogoUrl(newLogoUrl)
    setIsLogoRemoved(false)
    onLogoChange?.(newLogoUrl)

    const nextConfig = {
      ...localConfig,
      apply_new_logo_template: useFullSize
    }
    setLocalConfig(nextConfig)
    onConfigChange(nextConfig)
  }

  const handleCurrencyRateChange = (index: number, key: 'currency' | 'exchange', value: string | number) => {
    const currencyExchanges = getFieldValue('currency_exchanges', [])
    const next = [...currencyExchanges]
    next[index] = {
      ...next[index],
      [key]: key === 'exchange' ? Number(value) : (value as string)
    }
    handleConfigChange('currency_exchanges', next)
  }

  const handleAddCurrencyRate = () => {
    const currencyExchanges = getFieldValue('currency_exchanges', [])
    const next = [...currencyExchanges, { currency: 'VND-USD', exchange: 1 }]
    handleConfigChange('currency_exchanges', next)
  }

  const handleRemoveCurrencyRate = (index: number) => {
    const currencyExchanges = getFieldValue('currency_exchanges', [])
    const next = currencyExchanges.filter((_: any, i: number) => i !== index)
    handleConfigChange('currency_exchanges', next)
  }

  return (
    <div className='w-full space-y-6'>
      {/* Logo Section */}
      <div className='space-y-4'>
        <h3 className='text-lg font-semibold'>Logo</h3>
        <div className='rounded-lg border-2 border-dashed border-gray-300 p-8 text-center'>
          {(() => {
            const logoValue = logoUrl
            return logoValue ? (
              <div className='relative'>
                <img src={logoValue} alt='Logo' className='mx-auto h-32 w-32 rounded object-contain' />
                <Button
                  variant='ghost'
                  size='icon'
                  className='absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-500 text-white hover:bg-red-600'
                  onClick={() => {
                    const nextConfig = {
                      ...localConfig,
                      apply_new_logo_template: false
                    }
                    setLocalConfig(nextConfig)
                    onConfigChange(nextConfig)
                    setLogoFile(null)
                    setLogoUrl(null)
                    setIsLogoRemoved(true)
                    onLogoChange?.(null)
                  }}
                >
                  <X className='h-3 w-3' />
                </Button>
              </div>
            ) : (
              <div className='flex flex-col items-center space-y-2'>
                <div className='flex h-32 w-32 items-center justify-center rounded-lg bg-gray-100'>
                  <Camera className='h-8 w-8 text-gray-400' />
                </div>
                <Plus className='h-6 w-6 text-gray-400' />
              </div>
            )
          })()}
        </div>
        <Button variant='outline' onClick={() => setIsImageDialogOpen(true)} className='w-full'>
          Chọn ảnh
        </Button>
      </div>

      {/* Configuration Section */}
      <div className='space-y-6'>
        <h3 className='text-lg font-semibold'>Cấu hình hiển thị nội dung hoá đơn</h3>

        {/* Custom Text Fields */}
        <div className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='custom-text-1'>Văn bản tuỳ chỉnh 1</Label>
            <div className='min-h-[100px]'>
              <MinimalTiptapEditor
                throttleDelay={0}
                value={getFieldValue('custom_text_1') || ''}
                onChange={content => {
                  handleConfigChange('custom_text_1', content)
                }}
                placeholder='Nhập văn bản tuỳ chỉnh 1'
                className='h-full min-h-56 w-full min-w-0 rounded-xl'
                editorContentClassName='overflow-auto h-full flex grow'
                output='html'
                editable={true}
                editorClassName='focus:outline-hidden px-5 py-4 h-full grow'
              />
            </div>
          </div>

          <div className='space-y-2'>
            <Label htmlFor='custom-text-2'>Văn bản tuỳ chỉnh 2</Label>
            <Textarea
              id='custom-text-2'
              value={getFieldValue('custom_text_2')}
              onChange={e => handleConfigChange('custom_text_2', e.target.value)}
              placeholder='Cảm ơn Quý Khách'
              className='min-h-[80px]'
            />
          </div>
        </div>

        {/* Visibility Toggles */}
        <div className='space-y-4'>
          <div className='space-y-2'>
            <Label htmlFor='hotline'>Hiển thị số Hotline</Label>
            <Input
              id='hotline'
              placeholder='Nhập số Hotline'
              value={getFieldValue('hotline')}
              onChange={e => handleConfigChange('hotline', e.target.value)}
            />
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='enable_cash_change'
              checked={getFieldValue('enable_cash_change', false)}
              onCheckedChange={checked => handleConfigChange('enable_cash_change', checked)}
            />
            <Label htmlFor='enable_cash_change'>Hiển thị tiền nhận - tiền thừa</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='enable_topping'
              checked={getFieldValue('enable_topping', false)}
              onCheckedChange={checked => handleConfigChange('enable_topping', checked)}
            />
            <Label htmlFor='enable_topping'>Hiển thị topping</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show-voucher-gift'
              checked={getFieldValue('show_voucher_gift', false)}
              onCheckedChange={checked => handleConfigChange('show_voucher_gift', checked)}
            />
            <Label htmlFor='show-voucher-gift'>Hiển thị voucher gift</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='enable_discount'
              checked={getFieldValue('enable_discount', false)}
              onCheckedChange={checked => handleConfigChange('enable_discount', checked)}
            />
            <Label htmlFor='enable_discount'>Hiển thị giảm giá món</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='enable_vat_rate'
              checked={getFieldValue('enable_vat_rate', false)}
              onCheckedChange={checked => handleConfigChange('enable_vat_rate', checked)}
            />
            <Label htmlFor='enable_vat_rate'>Hiển thị % VAT</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='enable_vat_amount'
              checked={getFieldValue('enable_vat_amount', false)}
              onCheckedChange={checked => handleConfigChange('enable_vat_amount', checked)}
            />
            <Label htmlFor='enable_vat_amount'>Hiển thị tiền VAT</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show-payment-id'
              checked={getFieldValue('show_payment_id', false)}
              onCheckedChange={checked => handleConfigChange('show_payment_id', checked)}
            />
            <Label htmlFor='show-payment-id'>Hiển thị mã thanh toán bằng thẻ ngân hàng</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show-count-item-bill'
              checked={getFieldValue('show_count_item_bill', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_count_item_bill', checked ? 1 : 0)}
            />
            <Label htmlFor='show-count-item-bill'>Hiển thị số lượng món chính trên hóa đơn</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='enable_border_bill'
              checked={getFieldValue('enable_border_bill', 0) === 1}
              onCheckedChange={checked => handleConfigChange('enable_border_bill', checked ? 1 : 0)}
            />
            <Label htmlFor='enable_border_bill'>Hiển thị khung hóa đơn</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_customer_phone'
              checked={getFieldValue('show_customer_phone', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_customer_phone', checked ? 1 : 0)}
            />
            <Label htmlFor='show_customer_phone'>Bảo mật thông tin khách hàng</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='is_show_unit'
              checked={getFieldValue('is_show_unit', 0) === 1}
              onCheckedChange={checked => handleConfigChange('is_show_unit', checked ? 1 : 0)}
            />
            <Label htmlFor='is_show_unit'>Hiển thị ĐVT</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='is_group_source_and_tranno'
              checked={getFieldValue('is_group_source_and_tranno', 0) === 1}
              onCheckedChange={checked => handleConfigChange('is_group_source_and_tranno', checked ? 1 : 0)}
            />
            <Label htmlFor='is_group_source_and_tranno'>Gộp số HĐ và nguồn đơn hàng</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_points'
              checked={getFieldValue('show_points', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_points', checked ? 1 : 0)}
            />
            <Label htmlFor='show_points'>Hiển thị số điểm đã tích</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_vat_info'
              checked={getFieldValue('show_vat_info', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_vat_info', checked ? 1 : 0)}
            />
            <Label htmlFor='show_vat_info'>Hiển thị thông tin VAT</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_vat_reverse'
              checked={getFieldValue('show_vat_reverse', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_vat_reverse', checked ? 1 : 0)}
            />
            <Label htmlFor='show-vat-reverse'>Hiển thị tổng tiền bao gồm VAT (VAT ngược)</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show-qr-vat-info'
              checked={getFieldValue('show_qr_vat_info', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_qr_vat_info', checked ? 1 : 0)}
            />
            <Label htmlFor='show-qr-vat-info'>Hiển thị QR nhập thông tin VAT</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_item_class'
              checked={getFieldValue('show_item_class', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_item_class', checked ? 1 : 0)}
            />
            <Label htmlFor='show_item_class'>Hiển thị loại món</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_total_item_class_amount'
              checked={getFieldValue('show_total_item_class_amount', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_total_item_class_amount', checked ? 1 : 0)}
            />
            <Label htmlFor='show_total_item_class_amount'>Hiển thị tổng tiền loại món</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='hide_note_sale_change'
              checked={getFieldValue('hide_note_sale_change', 0) === 1}
              onCheckedChange={checked => handleConfigChange('hide_note_sale_change', checked ? 1 : 0)}
            />
            <Label htmlFor='hide_note_sale_change'>Ẩn ghi chú sửa hoá đơn</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_start_end_item_service'
              checked={getFieldValue('show_start_end_item_service', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_start_end_item_service', checked ? 1 : 0)}
            />
            <Label htmlFor='show_start_end_item_service'>Hiển thị giờ vào giờ ra của món dịch vụ</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='display_debt_amount'
              checked={getFieldValue('display_debt_amount', 0) === 1}
              onCheckedChange={checked => handleConfigChange('display_debt_amount', checked ? 1 : 0)}
            />
            <Label htmlFor='display_debt_amount'>Hiển thị thông tin nợ</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='show_item_note'
              checked={getFieldValue('show_item_note', 0) === 1}
              onCheckedChange={checked => handleConfigChange('show_item_note', checked ? 1 : 0)}
            />
            <Label htmlFor='show_item_note'>Hiển thị ghi chú món ăn</Label>
          </div>

          <div className='flex items-center space-x-2'>
            <Checkbox
              id='is_show_payment_fee'
              checked={getFieldValue('is_show_payment_fee', false)}
              onCheckedChange={checked => handleConfigChange('is_show_payment_fee', checked)}
            />
            <Label htmlFor='is_show_payment_fee'>Hiển thị phí cà thẻ</Label>
          </div>

          {/* Hiển thị QR AIO MOMO khi in tạm tính */}
          <div className='space-y-3'>
            <div className='grid grid-cols-[200px_1fr] rounded-md border'>
              <Label className='col-span-1 bg-gray-100 p-2 font-semibold'>Hiển thị QR AIO MOMO khi in tạm tính</Label>
              <div className='col-span-1 p-2'>
                <RadioGroup
                  value={getFieldValue('momo_qr_aio')}
                  onValueChange={val => handleConfigChange('momo_qr_aio', val)}
                  className='flex flex-row gap-8'
                >
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='1' id='momo_qr_aio0' />
                    <Label htmlFor='momo_qr_aio0' className='cursor-pointer'>
                      Hiển thị mã QR MoMo Đa Năng
                    </Label>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <RadioGroupItem value='2' id='momo_qr_aio1' />
                    <Label htmlFor='momo_qr_aio1' className='cursor-pointer'>
                      Hiển thị mã VietQr
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </div>

          {/* Font size slider */}
          <div className='space-y-2'>
            <Label htmlFor='font-size'>Kích thước chữ: {getFieldValue('font_size', 100)}%</Label>
            <input
              id='font-size'
              type='range'
              min='100'
              max='180'
              step='10'
              value={getFieldValue('font_size', 100)}
              onChange={e => handleConfigChange('font_size', Number(e.target.value))}
              className='h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200'
            />
          </div>

          {/* Currency Conversion Configuration */}
          <div className='space-y-3'>
            <h4 className='text-base font-semibold'>Cấu hình quy đổi tiền tệ</h4>
            <div className='grid grid-cols-2 gap-2 rounded-md border'>
              <div className='col-span-1 bg-gray-100 p-2 font-semibold'>Đơn vị</div>
              <div className='col-span-1 bg-gray-100 p-2 font-semibold'>Tỉ giá quy đổi</div>
              {getFieldValue('currency_exchanges', []).map((row: any, index: number) => (
                <div key={index} className='contents'>
                  <div className='p-2'>
                    <div className='flex items-center gap-2'>
                      <span className='whitespace-nowrap'>1 VND</span>
                    </div>
                  </div>
                  <div className='p-2'>
                    <div className='flex items-center gap-2'>
                      <Input
                        type='number'
                        className='w-24'
                        value={row.exchange}
                        onChange={e => handleCurrencyRateChange(index, 'exchange', e.target.value)}
                      />
                      <Select
                        value={row.currency}
                        onValueChange={val => handleCurrencyRateChange(index, 'currency', val)}
                      >
                        <SelectTrigger className='w-28'>
                          <SelectValue placeholder='Chọn' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='VND-USD'>USD</SelectItem>
                          <SelectItem value='VND-JPY'>JPY</SelectItem>
                          <SelectItem value='VND-EUR'>EUR</SelectItem>
                          <SelectItem value='VND-CNY'>CNY</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button variant='ghost' size='icon' onClick={() => handleRemoveCurrencyRate(index)}>
                        <Trash className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <Button variant='link' className='px-0' onClick={handleAddCurrencyRate}>
              Thêm cấu hình
            </Button>
          </div>

          {/* Scan QR Configuration */}
          <div className='space-y-3'>
            <h4 className='text-base font-semibold'>Cấu hình Scan QR</h4>
            <div className='grid grid-cols-[200px_1fr] rounded-md border'>
              <div className='col-span-1 bg-gray-100 p-2 font-semibold'>Hiển thị QR code</div>
              <div className='col-span-1 p-2'>
                <Checkbox
                  id='show-scan-qr-code'
                  checked={getFieldValue('enable_qr_code', false)}
                  onCheckedChange={checked => handleConfigChange('enable_qr_code', checked)}
                />
              </div>
              {getFieldValue('enable_qr_code', false) && (
                <>
                  <div className='col-span-1 bg-gray-100 p-2 font-semibold'>Tiêu đề</div>
                  <div className='col-span-1 p-2'>
                    <div className='flex items-center gap-2'>
                      <Input
                        placeholder='Nhập tiêu đề'
                        value={getFieldValue('qr_title')}
                        onChange={e => handleConfigChange('qr_title', e.target.value)}
                      />
                      <span className='text-sm text-gray-500'>{(getFieldValue('qr_title') || '').length}/200</span>
                    </div>
                  </div>
                  <div className='col-span-1 bg-gray-100 p-2 font-semibold'>Nội dung</div>
                  <div className='col-span-1 p-2'>
                    <div className='flex items-center gap-2'>
                      <Textarea
                        placeholder='Nội dung có thể là đường dẫn hoặc chuỗi kí tự'
                        value={getFieldValue('qr_content')}
                        onChange={e => handleConfigChange('qr_content', e.target.value)}
                      />
                      <span className='text-sm text-gray-500'>{(getFieldValue('qr_content') || '').length}/200</span>
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className='flex justify-end gap-2'>
              <Button
                type='button'
                onClick={() => {
                  onConfigChange(localConfig)
                  onSave?.(logoFile, isLogoRemoved)
                }}
                disabled={isSaving}
              >
                {isSaving ? 'Đang lưu...' : 'Lưu cấu hình'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Image Selector Dialog */}
      <ImageSelectorDialog
        isOpen={isImageDialogOpen}
        onClose={() => setIsImageDialogOpen(false)}
        onImageSelect={handleImageSelect}
        selectedImage={getFieldValue('logo')}
      />
    </div>
  )
}
