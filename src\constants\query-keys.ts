export const QUERY_KEYS = {
  DEVICES: 'devices',
  DEVICES_LIST: 'devices-list',
  DEVICES_DETAIL: 'devices-detail',

  STORES: 'stores',
  STORES_LIST: 'stores-list',
  STORES_DETAIL: 'stores-detail',
  STORES_STATS: 'stores-stats',
  STORES_WITH_AREAS: 'stores-with-areas',

  SALES: 'sales',
  SALES_LIST: 'sales-list',
  SALES_DETAIL: 'sales-detail',
  SALES_STATS: 'sales-stats',

  USERS: 'users',
  USERS_LIST: 'users-list',
  USERS_DETAIL: 'users-detail',
  USERS_PROFILE: 'users-profile',

  ROLES: 'roles',
  ROLES_LIST: 'roles-list',
  ROLES_DETAIL: 'roles-detail',

  REPORTS: 'reports',
  REPORTS_REVENUE: 'reports-revenue',
  REPORTS_INVENTORY: 'reports-inventory',
  REPORTS_PAYMENT_METHOD_REVENUE: 'reports-payment-method-revenue',
  REPORTS_PAYMENT_METHOD_DETAILS: 'reports-payment-method-details',
  REPORTS_SALE_BY_DATE: 'reports-sale-by-date',
  REPORTS_SALE_BY_DATE_SUMMARY: 'reports-sale-by-date-summary',
  REPORTS_SALE_SUMMARY_OVERVIEW: 'reports-sale-summary-overview',
  REPORTS_SOURCES: 'reports-sources',
  REPORTS_STORES: 'reports-stores',
  REPORTS_WEEKDAYS: 'reports-weekdays',

  // KTV Reports
  KTV_REPORTS: 'ktv-reports',

  ITEM_CLASSES: 'item-classes',
  ITEM_CLASSES_LIST: 'item-classes-list',
  ITEM_CLASSES_DETAIL: 'item-classes-detail',

  ITEM_TYPES: 'item-types',
  ITEM_TYPES_LIST: 'item-types-list',
  ITEM_TYPES_DETAIL: 'item-types-detail',

  UNITS: 'units',
  UNITS_LIST: 'units-list',
  UNITS_DETAIL: 'units-detail',

  ITEMS: 'items',
  ITEMS_LIST: 'items-list',
  ITEMS_DETAIL: 'items-detail',
  ITEMS_STATS: 'items-stats',
  ITEMS_IN_CITY: 'items-in-city',
  ITEMS_IN_CITY_LIST: 'items-in-city-list',
  ITEMS_IN_CITY_DETAIL: 'items-in-city-detail',

  ITEMS_IN_STORE: 'items-in-store',
  ITEMS_IN_STORE_LIST: 'items-in-store-list',
  ITEMS_IN_STORE_DETAIL: 'items-in-store-detail',
  ITEMS_IN_STORE_BY_LIST_ID: 'items-in-store-by-list-id',
  ITEMS_IN_STORE_FOR_TABLE: 'items-in-store-for-table', // For table data

  REMOVED_ITEMS: 'removed-items',
  REMOVED_ITEMS_LIST: 'removed-items-list',

  ITEM_CATEGORIES: 'item-categories',
  ITEM_CATEGORIES_LIST: 'item-categories-list',

  CUSTOMIZATIONS: 'customizations',
  CUSTOMIZATIONS_DETAIL: 'customizations-detail',
  CUSTOMIZATIONS_LIST: 'customizations-list',

  CITIES_LOCAL: 'cities-local',
  CITIES: 'cities',

  PROMOTIONS: 'promotions',
  PROMOTIONS_LIST: 'promotions-list',
  PROMOTIONS_DETAIL: 'promotions-detail',

  QUANTITY_DAYS: 'quantity-days',
  QUANTITY_DAYS_LIST: 'quantity-days-list',
  QUANTITY_DAYS_DETAIL: 'quantity-days-detail',
  QUANTITY_DAYS_STATS: 'quantity-days-stats',

  CHANNELS: 'channels',
  CHANNELS_LIST: 'channels-list',
  CHANNELS_DETAIL: 'channels-detail',

  TABLES: 'tables',
  TABLES_LIST: 'tables-list',
  TABLES_DETAIL: 'tables-detail',
  TABLE_LAYOUT: 'table-layout',

  SOURCES: 'sources',
  SOURCES_LIST: 'sources-list',
  SOURCES_DETAIL: 'sources-detail',

  PAYMENT_METHODS: 'payment-methods',
  PAYMENT_METHODS_LIST: 'payment-methods-list',
  PAYMENT_METHODS_DETAIL: 'payment-methods-detail',

  // Areas
  AREAS: 'areas',
  AREAS_LIST: 'areas-list',
  AREAS_DETAIL: 'areas-detail',

  // Combos
  COMBOS_LIST: 'combos-list',
  COMBO_PROMOTIONS_LIST: 'combo-promotions-list',
  COMBO_DETAIL: 'combo-detail',

  // Printer Positions
  PRINTER_POSITIONS: 'printer-positions',
  PRINTER_POSITIONS_IN_STORE: 'printer-positions-in-store',

  // VietQR
  VIETQR_BANKS: 'vietqr-banks',

  // Printers
  PRINTERS: 'printers',

  // Discount Payment
  DISCOUNT_PAYMENT: 'discount-payment',
  DISCOUNT_PAYMENT_LIST: 'discount-payment-list',
  DISCOUNT_PAYMENT_DETAIL: 'discount-payment-detail',

  // Service Charge
  SERVICE_CHARGE: 'service-charge',
  SERVICE_CHARGE_LIST: 'service-charge-list',
  SERVICE_CHARGE_DETAIL: 'service-charge-detail',

  PACKAGES: 'packages',
  PACKAGES_LIST: 'packages-list',
  PACKAGES_DETAIL: 'packages-detail',

  // Unified Discounts System
  DISCOUNTS: 'discounts',

  // Order Logs
  ORDER_LOGS: 'order-logs',

  // Bill Template
  BILL_TEMPLATE: 'bill-template',
  BILL_TEMPLATE_LIST: 'bill-template-list',
  BILL_TEMPLATE_DETAIL: 'bill-template-detail'
} as const

export type QueryKeys = typeof QUERY_KEYS
