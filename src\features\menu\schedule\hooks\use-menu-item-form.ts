import { useEffect } from 'react'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { menuItemFormSchema, type MenuItemFormData } from '../schemas/menu-item-form-schema'
import type { MenuItem } from '../types/menu-schedule-api'
import { generateItemCode } from '../utils/item-code-generator'
import { mapItemDataToForm } from '../utils/item-data-mapper'
import { convertArrayToDateWeek, convertObjectToHourDay } from '../utils/time-conversion'

interface UseMenuItemFormProps {
  selectedItem?: MenuItem | null
  open: boolean
}

export const useMenuItemForm = ({ selectedItem, open }: UseMenuItemFormProps) => {
  const form = useForm<MenuItemFormData>({
    resolver: zodResolver(menuItemFormSchema),
    defaultValues: {}
  })

  // Populate form when selectedItem changes
  useEffect(() => {
    if (selectedItem && open) {
      const data = selectedItem.originalItem || selectedItem
      const fieldMappings = mapItemDataToForm({
        ...data,
        item_name: selectedItem.name,
        item_id: selectedItem.code,
        is_service:
          typeof data.is_service === 'boolean' ? (data.is_service ? 1 : 0) : data.is_service
      })

      Object.entries(fieldMappings).forEach(([field, value]) => {
        form.setValue(field as keyof MenuItemFormData, value)
      })
    } else if (open) {
      form.reset()
    }
  }, [selectedItem, open, form])

  const createMenuItem = (
    data: MenuItemFormData,
    action: string,
    selectedItemId: string
  ): MenuItem => {
    return {
      id: action === 'UPDATE' ? selectedItemId : Date.now().toString(),
      code: data.item_id_barcode || data.item_id || generateItemCode(),
      name: data.item_name || 'Món mới',
      action: action as MenuItem['action'],
      originalItem: action === 'UPDATE' ? selectedItem?.originalItem : undefined,
      item_class_uid: data.item_class_uid,
      item_type_uid: data.item_type_uid,
      unit_uid: data.unit_uid,
      unit_secondary_uid: data.unit_secondary_uid,
      ots_price: data.ots_price,
      customization_uid: data.customization,
      vat_rate: data.vat_rate,
      cooking_time: data.cooking_time,
      is_service: data.is_service,
      display_order: data.display_order,
      time_sale_date_week: data.selected_days ? convertArrayToDateWeek(data.selected_days) : 0,
      time_sale_hour_day: data.selected_hours ? convertObjectToHourDay(data.selected_hours) : 0
    }
  }

  return {
    form,
    createMenuItem
  }
}
