import { useState } from 'react'

import { Search, ChevronDown } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'

import { useItemsInStoreForTable } from '../../hooks'
import type { ItemsInStoreTable } from '../../data'

interface MenuItemSelectionModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  sourceStoreUid: string
  selectedItems: string[]
  onItemsChange: (items: string[]) => void
  onSave: () => void
}

export function MenuItemSelectionModal({
  open,
  onOpenChange,
  sourceStoreUid,
  selectedItems,
  onItemsChange,
  onSave
}: MenuItemSelectionModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [groupFilter, setGroupFilter] = useState('all')
  const [selectedItemsOpen, setSelectedItemsOpen] = useState(false)

  // Fetch menu items from source store
  const { data: menuItems = [], isLoading } = useItemsInStoreForTable({
    params: {
      store_uid: sourceStoreUid
    },
    enabled: open && !!sourceStoreUid
  })

  // Filter items based on search and group
  const filteredItems = menuItems.filter((item: ItemsInStoreTable) => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesGroup = groupFilter === 'all' || item.originalData.item_type === groupFilter
    return matchesSearch && matchesGroup
  })

  // Get unique item types for filter
  const itemTypes = Array.from(
    new Set(menuItems.map((item: ItemsInStoreTable) => item.originalData.item_type))
  ).map(typeUid => {
    const item = menuItems.find((item: ItemsInStoreTable) => item.originalData.item_type === typeUid)
    return {
      uid: typeUid,
      name: item?.itemType || typeUid
    }
  })

  const handleSelectAll = () => {
    const activeItems = filteredItems.filter((item: ItemsInStoreTable) => item.originalData.active === 1)
    
    if (selectedItems.length === activeItems.length) {
      onItemsChange([])
    } else {
      onItemsChange(activeItems.map((item: ItemsInStoreTable) => item.code))
    }
  }

  const handleItemToggle = (itemCode: string) => {
    if (selectedItems.includes(itemCode)) {
      onItemsChange(selectedItems.filter(code => code !== itemCode))
    } else {
      onItemsChange([...selectedItems, itemCode])
    }
  }

  const handleSave = () => {
    onSave()
    onOpenChange(false)
  }

  // Get selected menu items details
  const selectedMenuItems = menuItems.filter((item: ItemsInStoreTable) =>
    selectedItems.includes(item.code)
  )

  // Only count active items for select all functionality
  const activeItems = filteredItems.filter((item: ItemsInStoreTable) => item.originalData.active === 1)
  const isAllSelected = activeItems.length > 0 && selectedItems.length === activeItems.length

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Chọn món ăn</DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex flex-col space-y-4 min-h-0">
          {/* Search and Filter */}
          <div className="flex gap-4 flex-shrink-0">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={groupFilter} onValueChange={setGroupFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Tất cả nhóm món" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả nhóm món</SelectItem>
                {itemTypes.map((type) => (
                  <SelectItem key={type.uid} value={type.uid}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Select All */}
          <div className="flex-shrink-0">
            <Collapsible open={selectedItemsOpen} onOpenChange={setSelectedItemsOpen}>
              <div className="flex items-center space-x-2 p-2 bg-muted/50 rounded">
                <Checkbox
                  id="select-all"
                  checked={isAllSelected}
                  onCheckedChange={handleSelectAll}
                />
                <CollapsibleTrigger asChild>
                  <button className="flex items-center space-x-2 text-sm font-medium hover:text-primary">
                    <span>Đã chọn {selectedItems.length}</span>
                    {selectedItems.length > 0 && (
                      <ChevronDown className={`h-4 w-4 transition-transform ${selectedItemsOpen ? 'rotate-180' : ''}`} />
                    )}
                  </button>
                </CollapsibleTrigger>
              </div>

              {selectedItems.length > 0 && (
                <CollapsibleContent className="mt-2">
                  <div className="bg-muted/30 rounded-md p-2 max-h-32 overflow-y-auto">
                    <div className="space-y-1">
                      {selectedMenuItems.map((item: ItemsInStoreTable) => (
                        <div key={item.id} className="flex items-center space-x-2 text-sm">
                          <Checkbox
                            checked={true}
                            onCheckedChange={() => handleItemToggle(item.code)}
                            className="h-3 w-3"
                          />
                          <span className="flex-1 truncate">{item.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CollapsibleContent>
              )}
            </Collapsible>
          </div>

          {/* Items List */}
          <div className="flex-1 min-h-0">
            <ScrollArea className="h-full">
              <div className="space-y-2 pr-4">
                {isLoading ? (
                  <div className="text-center py-8">Đang tải...</div>
                ) : filteredItems.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Không tìm thấy món ăn nào
                  </div>
                ) : (
                  filteredItems.map((item: ItemsInStoreTable) => {
                    const isActive = item.originalData.active === 1
                    
                    return (
                      <div
                        key={item.id}
                        className={`flex items-center space-x-3 p-3 border rounded ${
                          isActive ? 'hover:bg-muted/50' : 'bg-muted/20'
                        }`}
                      >
                        <Checkbox
                          id={item.id}
                          checked={selectedItems.includes(item.code)}
                          onCheckedChange={() => handleItemToggle(item.code)}
                          disabled={!isActive}
                          className={!isActive ? 'opacity-50' : ''}
                        />
                        <div className="flex-1 min-w-0">
                          <label 
                            htmlFor={item.id} 
                            className={`text-sm font-medium block truncate ${
                              isActive ? 'cursor-pointer' : 'cursor-not-allowed text-muted-foreground'
                            }`}
                          >
                            {item.name}
                          </label>
                          <p className="text-xs text-muted-foreground mt-1 truncate">
                            {item.itemType}
                          </p>
                        </div>
                        {!isActive && (
                          <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                            Deactive
                          </div>
                        )}
                      </div>
                    )
                  })
                )}
              </div>
            </ScrollArea>
          </div>
        </div>

        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Hủy
          </Button>
          <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700">
            Xong
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
