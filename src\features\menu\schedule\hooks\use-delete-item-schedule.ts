import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useCurrentUser } from '@/stores/posStore'

import { menuScheduleApi } from '@/lib/menu-schedule-api'

import type { MenuItem } from '../types/menu-schedule-api'
import { convertMenuItemToUpdatePayload } from '../utils/payload-converter'

interface DeleteItemScheduleParams {
  menuItems: MenuItem[]
  scheduleData: {
    company_uid: string
    brand_uid: string
    store_uid: string
    city_uid: string
    time: number
    end_time?: number | null
    schedule_id?: string
  }
}

export const useDeleteItemSchedule = () => {
  const queryClient = useQueryClient()
  const { user } = useCurrentUser()

  return useMutation({
    mutationFn: async ({ menuItems, scheduleData }: DeleteItemScheduleParams) => {
      const userEmail = user?.email || '<EMAIL>'

      const payload = menuItems.map(menuItem => {
        const deletePayload = convertMenuItemToUpdatePayload(menuItem, scheduleData, userEmail)
        return {
          ...deletePayload,
          action: 'DELETE' as const,
          changed_data: {
            ...deletePayload.changed_data,
            deleted: true
          }
        }
      })

      return menuScheduleApi.deleteItemSchedule(payload)
    },
    onSuccess: () => {
      toast.success('Xóa menu schedule thành công!')

      queryClient.invalidateQueries({ queryKey: ['menu-schedules'] })
      queryClient.invalidateQueries({ queryKey: ['menu-schedule-by-params'] })
    },
    onError: () => {
      toast.error('Có lỗi xảy ra khi xóa menu schedule')
    }
  })
}
