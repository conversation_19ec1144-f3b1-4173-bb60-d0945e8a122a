import { createContext, useContext, ReactNode } from 'react'

import {
  useCustomizationForm,
  useGroupManagement,
  useMenuItemSelection,
  useDishSelection,
  useModalState
} from './hooks'

interface FormContextType {
  customizationForm: ReturnType<typeof useCustomizationForm>
  groupManagement: ReturnType<typeof useGroupManagement>
  menuItemSelection: ReturnType<typeof useMenuItemSelection>
  dishSelection: ReturnType<typeof useDishSelection>
  modalState: ReturnType<typeof useModalState>
  handlers: {
    handleCreateGroup: () => void
    handleEditGroup: (groupId: string) => void
    handleCloseModal: () => void
    handleCloseAddItemModal: () => void
    handleConfirmMenuItems: (finalItems: any[]) => void
    handleAddMenuItem: (selectedCityId: string) => void
    handleSaveGroup: (finalItems: any[]) => boolean
  }
}

const FormContext = createContext<FormContextType | undefined>(undefined)

interface FormProviderProps {
  children: ReactNode
  customizationId?: string
}

export function FormProvider({ children, customizationId }: FormProviderProps) {
  const customizationForm = useCustomizationForm({
    isEdit: !!customizationId,
    customizationId: customizationId || ''
  })
  const groupManagement = useGroupManagement()
  const menuItemSelection = useMenuItemSelection({
    onConfirm: newItems => groupManagement.setMenuItems(newItems)
  })
  const dishSelection = useDishSelection()
  const modalState = useModalState()

  // Handler functions
  const handleCreateGroup = () => {
    groupManagement.handleCreateGroup()
    modalState.setCreateGroupModalOpen(true)
  }

  const handleEditGroup = (groupId: string) => {
    groupManagement.handleEditGroup(groupId)
    modalState.setCreateGroupModalOpen(true)
  }

  const handleCloseModal = () => {
    modalState.handleCloseModal()
    groupManagement.resetGroupForm()
    menuItemSelection.resetSelection()
  }

  const handleCloseAddItemModal = () => {
    modalState.handleCloseAddItemModal()
    menuItemSelection.resetSelection()
  }

  const handleConfirmMenuItems = (finalItems: any[]) => {
    menuItemSelection.handleConfirmMenuItems(finalItems, groupManagement.menuItems)
    modalState.handleCloseAddItemModal()
  }

  const handleAddMenuItem = (selectedCityId: string) => {
    // Sync selection with current group menu items before opening modal
    menuItemSelection.syncWithCurrentMenuItems(groupManagement.menuItems)
    modalState.handleAddMenuItem(selectedCityId)
  }

  const handleSaveGroup = (finalItems: any[]) => {
    const success = groupManagement.handleSaveGroup(finalItems)
    if (success) {
      handleCloseModal()
    }
    return success
  }

  const value: FormContextType = {
    customizationForm,
    groupManagement,
    menuItemSelection,
    dishSelection,
    modalState,
    handlers: {
      handleCreateGroup,
      handleEditGroup,
      handleCloseModal,
      handleCloseAddItemModal,
      handleConfirmMenuItems,
      handleAddMenuItem,
      handleSaveGroup
    }
  }

  return <FormContext.Provider value={value}>{children}</FormContext.Provider>
}

export function useFormContext(): FormContextType {
  const context = useContext(FormContext)
  if (context === undefined) {
    throw new Error('useFormContext must be used within a FormProvider')
  }
  return context
}
