import type { UseFormReturn } from 'react-hook-form'

import { CityData } from '@/types'
import type { ItemClass } from '@/types/item-class'

import type { ItemType } from '@/lib/item-types-api'
import type { Unit } from '@/lib/units-api'

import type { ItemsInCity } from '../data'
import { ItemBasicInfo } from './item-basic-info'
import { ItemConfiguration } from './item-configuration'

export { ItemBasicInfo } from './item-basic-info'
export { ItemConfiguration } from './item-configuration'

export type { ItemClass } from '@/types/item-class'
export type { ItemType } from '@/lib/item-types-api'
export type { Unit } from '@/lib/units-api'

interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturn<any>
  itemTypes: ItemType[]
  itemClasses: ItemClass[]
  units: Unit[]
  cities: CityData[]
  imageFile?: File | null
  onImageChange: (event: React.ChangeEvent<HTMLInputElement>) => void
  imagePreview?: string | null
  onImageRemove?: () => void
  currentRow?: ItemsInCity
}

export function ItemFormSections({
  form,
  itemTypes,
  itemClasses,
  units,
  cities,
  imageFile,
  onImageChange,
  imagePreview,
  onImageRemove,
  currentRow
}: Props) {
  return (
    <div className='space-y-6'>
      <ItemBasicInfo
        form={form}
        itemTypes={itemTypes}
        itemClasses={itemClasses}
        units={units}
        cities={cities}
        imageFile={imageFile}
        onImageChange={onImageChange}
        imagePreview={imagePreview}
        onImageRemove={onImageRemove}
      />

      <ItemConfiguration form={form} currentRow={currentRow} />
    </div>
  )
}
