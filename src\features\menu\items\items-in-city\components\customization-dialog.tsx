'use client'

import { useEffect, useMemo } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import type { Customization } from '@/types/customizations'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { ItemsInCity } from '../data'
import { useUpdateItemCustomization } from '../hooks'

const customizationFormSchema = z.object({
  customization_uid: z.string()
})

type CustomizationFormValues = z.infer<typeof customizationFormSchema>

interface CustomizationDialogProps {
  menuItem: ItemsInCity | null
  menuItems: ItemsInCity[]
  customizations: Customization[]
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function CustomizationDialog({
  menuItem,
  menuItems,
  customizations,
  open,
  onOpenChange
}: CustomizationDialogProps) {
  const updateItemCustomizationMutation = useUpdateItemCustomization()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const currentCustomizationUid = useMemo(() => {
    if (!menuItems.length || !menuItem?.id) return null
    const currentItem = menuItems.find(item => item.id === menuItem.id)
    return currentItem?.originalData?.customization_uid || null
  }, [menuItems, menuItem?.id])

  const customizationOptions = useMemo(() => {
    const noneOption = {
      id: 'none',
      name: 'Không có',
      value: 'none'
    }

    if (!customizations.length) return [noneOption]

    const options = customizations.map(customization => ({
      id: customization.id,
      name: customization.name || customization.id,
      value: customization.id
    }))

    return [noneOption, ...options]
  }, [customizations])

  const form = useForm<CustomizationFormValues>({
    resolver: zodResolver(customizationFormSchema),
    defaultValues: {
      customization_uid: 'none'
    }
  })

  useEffect(() => {
    if (open && menuItem) {
      try {
        if (currentCustomizationUid) {
          form.reset({
            customization_uid: currentCustomizationUid
          })
        } else {
          form.reset({ customization_uid: 'none' })
        }
      } catch (_error) {
        toast.error('Lỗi khi load customization data')
      }
    }
  }, [open, menuItem, form, customizationOptions, currentCustomizationUid, menuItems])

  const onSubmit = async (values: CustomizationFormValues) => {
    try {
      if (!menuItem?.id || !company?.id || !selectedBrand?.id || !menuItems.length) {
        throw new Error('Required data is missing')
      }

      const customizationUid = values.customization_uid === 'none' ? null : values.customization_uid

      const currentItem = menuItems.find(item => item.id === menuItem.id)

      if (!currentItem) {
        throw new Error('Item not found in current data')
      }

      await updateItemCustomizationMutation.mutateAsync({
        menuItem: currentItem,
        customization_uid: customizationUid,
        company_uid: company.id,
        brand_uid: selectedBrand.id
      })

      onOpenChange(false)
    } catch (error) {
      toast.error('Lỗi khi cập nhật customization')
    }
  }

  if (!menuItem) return null

  return (
    <Dialog
      open={open}
      onOpenChange={v => {
        onOpenChange(v)
        form.reset()
      }}
    >
      <DialogContent className='top-[20%] w-full max-w-4xl translate-y-[-50%]'>
        <DialogHeader>
          <DialogTitle className='text-center'>Cấu hình customization</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='customization_uid'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customization áp dụng cho món *</FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='Chọn customization...' />
                      </SelectTrigger>
                      <SelectContent>
                        {customizationOptions.map(option => (
                          <SelectItem key={option.id} value={option.value}>
                            {option.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <DialogClose asChild>
                <Button variant='outline' type='button'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='submit' disabled={form.formState.isSubmitting}>
                {form.formState.isSubmitting ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
