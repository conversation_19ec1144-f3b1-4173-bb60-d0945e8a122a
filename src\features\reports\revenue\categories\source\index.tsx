import React from 'react'

import { ActionBar } from './components/action-bar'
import { SourceChart } from './components/source-chart'
import { SourceTable } from './components/source-table'
import { SourceProvider } from './context'

const SourceRevenueReport: React.FC = () => {
  return (
    <SourceProvider>
      <div className='space-y-6 p-4'>
        <ActionBar />
        <SourceChart />
        <SourceTable />
      </div>
    </SourceProvider>
  )
}

export default SourceRevenueReport
