import ExcelJS from 'exceljs'
import { toast } from 'sonner'

import type { TableLayoutItem } from '../data/table-layout-types'
import { useSaveTablePositions } from './'
import { useEditTableHandlers } from './use-edit-table-handlers'
import { useImportTables } from './use-import-tables'
import { useSortHandlers } from './use-sort-handlers'
import { useTableActions } from './use-table-actions'

interface UseTableLayoutHandlersProps {
  selectedStoreId: string
  selectedAreaId: string
  localTables: TableLayoutItem[]
  importedTableData: any[]
  setSelectedAreaId: (areaId: string) => void
  setSelectedStoreId: (storeId: string) => void
  setSelectedTable: (table: TableLayoutItem | null) => void
  setLocalTables: React.Dispatch<React.SetStateAction<TableLayoutItem[]>>
  setIsSortModalOpen: (open: boolean) => void
  setIsImportModalOpen: (open: boolean) => void
  setIsPreviewModalOpen: (open: boolean) => void
  setIsEditTableModalOpen: (open: boolean) => void
  setIsConfigureTableModalOpen: (open: boolean) => void
  setImportedTableData: (data: any[]) => void
}

export const useTableLayoutHandlers = ({
  selectedStoreId,
  selectedAreaId,
  localTables,
  importedTableData,
  setSelectedAreaId,
  setSelectedStoreId,
  setSelectedTable,
  setLocalTables,
  setIsSortModalOpen,
  setIsImportModalOpen,
  setIsPreviewModalOpen,
  setIsEditTableModalOpen,
  setIsConfigureTableModalOpen,
  setImportedTableData
}: UseTableLayoutHandlersProps) => {
  const { saveTablePositions, isSaving } = useSaveTablePositions()

  // Use specialized hooks
  const tableActions = useTableActions({
    selectedStoreId,
    selectedAreaId,
    setSelectedAreaId,
    setSelectedStoreId,
    setSelectedTable,
    setLocalTables,
    setIsSortModalOpen,
    setIsConfigureTableModalOpen
  })

  const editTableHandlers = useEditTableHandlers({
    localTables,
    setIsEditTableModalOpen
  })

  const sortHandlers = useSortHandlers({
    selectedAreaId,
    localTables,
    setLocalTables,
    setIsSortModalOpen
  })

  const importHandlers = useImportTables()

  // Additional handlers for modals
  const handleImportTables = () => {
    if (!selectedStoreId) {
      toast.error('Vui lòng chọn cửa hàng trước')
      return
    }
    setIsImportModalOpen(true)
  }

  const handleDownloadTemplate = () => {
    const link = document.createElement('a')
    link.href = '/files/setting/table/table-import-template.xlsx'
    link.download = 'create-table.xlsx'
    link.click()
  }

  const handleUploadFile = () => {
    // Create file input for upload
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.xlsx,.xls,.csv'
    input.onchange = async event => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (file) {
        try {
          toast.info('Đang đọc file...')
          const data = await parseImportFile(file)
          setImportedTableData(data)
          setIsPreviewModalOpen(true)
          toast.success(`Đã đọc ${data.length} bàn từ file`)
        } catch (error) {
          console.error('Error parsing file:', error)
          toast.error('Không thể đọc file. Vui lòng kiểm tra định dạng file.')
        }
      }
    }
    input.click()
  }

  const handlePreviewSave = () => {
    if (importedTableData.length === 0) {
      toast.error('Không có dữ liệu để import')
      return
    }

    // Process imported data and add to local tables
    const newTables = importedTableData.map((item, index) => ({
      id: `imported-${Date.now()}-${index}`,
      table_name: item.tenBan,
      table_code: item.tenBan,
      description: item.moTa,
      area_uid: selectedAreaId,
      store_uid: selectedStoreId,
      company_uid: '',
      brand_uid: '',
      sort: index + 1,
      active: 1,
      position: { x: 100 + (index % 5) * 120, y: 100 + Math.floor(index / 5) * 80 },
      size: { width: 100, height: 60 },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }))

    // Add to local tables
    setLocalTables(prev => [...prev, ...newTables])

    setIsPreviewModalOpen(false)
    setIsImportModalOpen(false)
    setImportedTableData([])
    toast.success(`Đã import ${newTables.length} bàn thành công`)
  }

  const handlePreviewCancel = () => {
    setIsPreviewModalOpen(false)
    setImportedTableData([])
  }

  const handleSaveLayout = () => {
    if (!selectedStoreId || localTables.length === 0) {
      toast.error('Không có dữ liệu để lưu')
      return
    }

    saveTablePositions({
      tables: localTables,
      storeUid: selectedStoreId
    })
  }

  return {
    // Table actions
    ...tableActions,

    // Edit table handlers
    ...editTableHandlers,

    // Sort handlers
    ...sortHandlers,

    // Import handlers
    ...importHandlers,
    handleImportTables,
    handleDownloadTemplate,
    handleUploadFile,
    handlePreviewSave,
    handlePreviewCancel,

    // Save layout
    handleSaveLayout,

    // Modal setters
    setIsSortModalOpen,
    setIsImportModalOpen,
    setIsPreviewModalOpen,
    setIsEditTableModalOpen,
    setIsConfigureTableModalOpen,

    // Loading states
    isSaving,
    isUpdatingSort: sortHandlers.isUpdatingSort
  }
}

// Helper function to parse import file
const parseImportFile = async (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = async e => {
      try {
        const data = e.target?.result
        const workbook = new ExcelJS.Workbook()
        await workbook.xlsx.load(data as ArrayBuffer)

        const worksheet = workbook.getWorksheet(1)
        if (!worksheet) {
          throw new Error('Không thể đọc worksheet từ file Excel')
        }

        const tableData: any[] = []

        // Skip header row (row 1) and start from row 2
        worksheet.eachRow((row, rowNumber) => {
          if (rowNumber > 1) {
            const rowData = {
              tenBan: row.getCell(1).value?.toString() || '',
              khuVuc: row.getCell(2).value?.toString() || '',
              nguon: row.getCell(3).value?.toString() || '',
              monDatTruoc: row.getCell(4).value?.toString() || '',
              moTa: row.getCell(5).value?.toString() || ''
            }

            // Only add row if it has table name (required)
            if (rowData.tenBan.trim()) {
              tableData.push(rowData)
            }
          }
        })

        resolve(tableData)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsArrayBuffer(file)
  })
}
