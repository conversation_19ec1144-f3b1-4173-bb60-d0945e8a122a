import { useState } from 'react'

import { format } from 'date-fns'

import { vi } from 'date-fns/locale'
import { CalendarIcon } from 'lucide-react'
import { toast } from 'sonner'

import { cn } from '@/lib/utils'

import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

interface DateRangePickerProps {
  startDate?: Date
  endDate?: Date
  onStartDateChange?: (date: Date | undefined) => void
  onEndDateChange?: (date: Date | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function DateRangePicker({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  placeholder = 'Chọn khoảng thời gian',
  disabled = false,
  className
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeInput, setActiveInput] = useState<'start' | 'end'>('start')

  const formatDateRange = () => {
    if (startDate && endDate) {
      return `${format(startDate, 'dd/MM/yyyy', { locale: vi })} - ${format(endDate, 'dd/MM/yyyy', { locale: vi })}`
    }
    if (startDate) {
      return `${format(startDate, 'dd/MM/yyyy', { locale: vi })} - ...`
    }
    return placeholder
  }

  const handleDateSelect = (date: Date | undefined) => {
    if (!date) return

    if (activeInput === 'start') {
      if (endDate && date > endDate) {
        onEndDateChange?.(undefined)
        toast.warning('Ngày kết thúc đã được reset vì nhỏ hơn ngày bắt đầu mới')
      }
      onStartDateChange?.(date)
      setActiveInput('end')
    } else {
      if (startDate && date < startDate) {
        toast.error('Ngày kết thúc phải lớn hơn hoặc bằng ngày bắt đầu')
        return
      }
      onEndDateChange?.(date)
      // Auto close after selecting end date
      setTimeout(() => setIsOpen(false), 100)
    }
  }

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (open) {
      setActiveInput('start')
    }
  }

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          className={cn(
            'w-full justify-start text-left font-normal',
            !startDate && !endDate && 'text-muted-foreground',
            className
          )}
          disabled={disabled}
        >
          <CalendarIcon className='mr-2 h-4 w-4' />
          {formatDateRange()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className='w-auto p-0' align='start'>
        <div className='p-3'>
          <div className='mb-3 text-center text-sm font-medium'>
            {activeInput === 'start' ? 'Chọn ngày bắt đầu' : 'Chọn ngày kết thúc'}
          </div>
          <Calendar
            mode='single'
            selected={activeInput === 'start' ? startDate : endDate}
            onSelect={handleDateSelect}
            locale={vi}
            autoFocus
          />
          <div className='mt-3 flex justify-between'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setActiveInput(activeInput === 'start' ? 'end' : 'start')}
            >
              {activeInput === 'start' ? 'Chọn ngày kết thúc' : 'Chọn ngày bắt đầu'}
            </Button>
            <Button variant='outline' size='sm' onClick={() => setIsOpen(false)}>
              Đóng
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
