import React, { createContext, useContext, useMemo, useState } from 'react'

import { format } from 'date-fns'

import { usePosStores } from '@/stores'
import type { PromotionRevenueData } from '@/types/api/revenue-promotion'
import { vi } from 'date-fns/locale'

import { usePromotionRevenue } from '@/hooks/api'
import { usePosData } from '@/hooks/use-pos-data'

type PromotionPoint = {
  date_label: string
  total_amount: number
  total_bill: number
  discount_amount: number
}

interface PromotionContextType {
  dateRange: { from: Date; to: Date }
  setDateRange: React.Dispatch<React.SetStateAction<{ from: Date; to: Date }>>

  selectedCities: string[]
  selectedStores: string[]
  setSelectedCities: React.Dispatch<React.SetStateAction<string[]>>
  setSelectedStores: React.Dispatch<React.SetStateAction<string[]>>

  compareCities: string[]
  compareStores: string[]
  setCompareCities: React.Dispatch<React.SetStateAction<string[]>>
  setCompareStores: React.Dispatch<React.SetStateAction<string[]>>

  selectedStoreIds: string[]
  finalStoreIds: string[]
  compareStoreIds: string[]

  startDate: number
  endDate: number

  data: PromotionPoint[]
  compareData: PromotionPoint[]
  rawData: PromotionRevenueData[]
  compareRawData: PromotionRevenueData[]
  isLoading: boolean
  isCompareLoading: boolean
  tableRows: Array<{
    promotion_id: string
    promotion_name: string
    total_bill: number
    revenue_net: number
    revenue_gross: number
    commission_amount: number
    discount_amount: number
    deduct_tax_amount: number
  }>

  handleUpdateDateRange: () => void
  handleExport: (mode: 'selected' | 'all') => void
}

const PromotionContext = createContext<PromotionContextType | undefined>(undefined)

export const PromotionProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>(() => {
    const today = new Date()
    return { from: today, to: today }
  })

  const [apiTrigger, setApiTrigger] = useState(0)
  const [selectedCities, setSelectedCities] = useState<string[]>([])
  const [selectedStores, setSelectedStores] = useState<string[]>([])

  const [compareCities, setCompareCities] = useState<string[]>([])
  const [compareStores, setCompareStores] = useState<string[]>([])

  const { company, activeBrands } = usePosData()
  const companyUid = company?.id || ''
  const brandUid = activeBrands[0]?.id || ''

  const { currentBrandStores } = usePosStores()
  const allBrandStoreIds = currentBrandStores.map(store => store.id || store.store_id)

  const getSelectedStoreIds = () => {
    const storeIds: string[] = []
    selectedCities.forEach(cityId => {
      const cityStores = currentBrandStores.filter(store => store.city_uid === cityId)
      cityStores.forEach(store => {
        const storeId = store.id || store.store_id
        if (storeId && !storeIds.includes(storeId)) storeIds.push(storeId)
      })
    })
    selectedStores.forEach(storeId => {
      const store = currentBrandStores.find(s => (s.id || s.store_id) === storeId)
      if (store && !storeIds.includes(storeId)) storeIds.push(storeId)
    })
    return storeIds
  }

  const selectedStoreIds = getSelectedStoreIds()
  const finalStoreIds = selectedStoreIds.length > 0 ? selectedStoreIds : allBrandStoreIds

  const getCompareStoreIds = () => {
    const storeIds: string[] = []
    compareCities.forEach(cityId => {
      const cityStores = currentBrandStores.filter(store => store.city_uid === cityId)
      cityStores.forEach(store => {
        const storeId = store.id || store.store_id
        if (storeId && !storeIds.includes(storeId)) storeIds.push(storeId)
      })
    })
    compareStores.forEach(storeId => {
      const store = currentBrandStores.find(s => (s.id || s.store_id) === storeId)
      if (store && !storeIds.includes(storeId)) storeIds.push(storeId)
    })
    return storeIds
  }

  const compareStoreIds = getCompareStoreIds()

  const { startDate, endDate } = useMemo(() => {
    const start = new Date(dateRange.from)
    start.setHours(0, 0, 0, 0)
    const end = new Date(dateRange.to)
    end.setHours(23, 59, 59, 999)
    return { startDate: start.getTime(), endDate: end.getTime() }
  }, [dateRange.from, dateRange.to, apiTrigger])

  const handleUpdateDateRange = () => setApiTrigger(prev => prev + 1)

  const handleSetSelectedCities = (cities: string[] | ((prev: string[]) => string[])) => {
    const newCities = typeof cities === 'function' ? cities(selectedCities) : cities
    const newStoreIds = [...selectedStores] // Keep current stores

    const wouldBeEmpty = newCities.length === 0 && newStoreIds.length === 0

    if (wouldBeEmpty) {
      setSelectedCities([])
      setSelectedStores([])
      setCompareCities([])
      setCompareStores([])
      setApiTrigger(prev => prev + 1)
    } else {
      setSelectedCities(newCities)
    }
  }

  const handleSetSelectedStores = (stores: string[] | ((prev: string[]) => string[])) => {
    const newStores = typeof stores === 'function' ? stores(selectedStores) : stores
    const newCities = [...selectedCities]

    const wouldBeEmpty = newCities.length === 0 && newStores.length === 0

    if (wouldBeEmpty) {
      setSelectedCities([])
      setSelectedStores([])
      setCompareCities([])
      setCompareStores([])
      setApiTrigger(prev => prev + 1)
    } else {
      setSelectedStores(newStores)
    }
  }

  const { data: rawData, isLoading } = usePromotionRevenue({
    startDate,
    endDate,
    selectedStoreIds: finalStoreIds,
    companyUid,
    brandUid,
    enabled: true
  })

  const { data: compareRawData, isLoading: isCompareLoading } = usePromotionRevenue({
    startDate,
    endDate,
    selectedStoreIds: compareStoreIds,
    companyUid,
    brandUid,
    enabled: compareStoreIds.length > 0
  })

  const safeFormatDateLabel = (input: string): string => {
    try {
      const d = new Date(input)
      if (!isNaN(d.getTime())) {
        return format(d, 'dd/MM', { locale: vi })
      }
      if (/^\d{2}\/\d{2}$/.test(input)) return input
      const m = input.match(/^(\d{4})-(\d{2})-(\d{2})$/)
      if (m) return `${m[3]}/${m[2]}`
      return input
    } catch {
      return input
    }
  }

  const data: PromotionPoint[] = React.useMemo(() => {
    const dateMap = new Map<string, { total_amount: number; total_bill: number; discount_amount: number }>()
    ;(rawData || []).forEach(p => {
      ;(p.list_data || []).forEach(d => {
        const prev = dateMap.get(d.date) || { total_amount: 0, total_bill: 0, discount_amount: 0 }
        dateMap.set(d.date, {
          total_amount: prev.total_amount + (d.revenue_net ?? d.total_amount ?? 0),
          total_bill: prev.total_bill + (d.total_bill ?? 0),
          discount_amount: prev.discount_amount + (d.discount_amount ?? 0)
        })
      })
    })

    return Array.from(dateMap.entries())
      .sort(([a], [b]) => (a < b ? -1 : a > b ? 1 : 0))
      .map(([date, v]) => ({
        date_label: safeFormatDateLabel(date),
        total_amount: v.total_amount,
        total_bill: v.total_bill,
        discount_amount: v.discount_amount
      }))
  }, [rawData])

  const compareData: PromotionPoint[] = React.useMemo(() => {
    const dateMap = new Map<string, { total_amount: number; total_bill: number; discount_amount: number }>()
    ;(compareRawData || []).forEach(p => {
      ;(p.list_data || []).forEach(d => {
        const prev = dateMap.get(d.date) || { total_amount: 0, total_bill: 0, discount_amount: 0 }
        dateMap.set(d.date, {
          total_amount: prev.total_amount + (d.revenue_net ?? d.total_amount ?? 0),
          total_bill: prev.total_bill + (d.total_bill ?? 0),
          discount_amount: prev.discount_amount + (d.discount_amount ?? 0)
        })
      })
    })

    return Array.from(dateMap.entries())
      .sort(([a], [b]) => (a < b ? -1 : a > b ? 1 : 0))
      .map(([date, v]) => ({
        date_label: safeFormatDateLabel(date),
        total_amount: v.total_amount,
        total_bill: v.total_bill,
        discount_amount: v.discount_amount
      }))
  }, [compareRawData])

  const tableRows = React.useMemo(() => {
    return (rawData || []).map(item => ({
      promotion_id: (item as any).promotion_id || '',
      promotion_name: item.promotion_name as any,
      total_bill: (item as any).total_bill || 0,
      revenue_net: (item as any).revenue_net || 0,
      revenue_gross: (item as any).revenue_gross || 0,
      commission_amount: (item as any).commission_amount || 0,
      discount_amount: (item as any).discount_amount || 0,
      deduct_tax_amount: (item as any).deduct_tax_amount || 0
    }))
  }, [rawData])

  const handleExport = async (mode: 'selected' | 'all') => {
    try {
      const exportStoreIds = mode === 'selected' ? finalStoreIds : allBrandStoreIds

      const { getPromotionRevenue } = await import('@/lib/api/pos/promotion-revenue-api')

      const response = await getPromotionRevenue({
        brand_uid: brandUid,
        company_uid: companyUid,
        start_date: startDate,
        end_date: endDate,
        list_store_uid: exportStoreIds.join(','),
        store_open_at: 0,
        by_days: 1
      })

      const exportData = response.data || []

      if (exportData.length === 0) {
        console.warn('No data to export')
        return
      }

      const storeNames = exportStoreIds
        .map(id => currentBrandStores.find(s => (s.id || s.store_id) === id)?.store_name)
        .filter((name): name is string => Boolean(name))
        .slice(0, 3)

      const { exportPromotionRevenueToExcel } = await import('../utils')

      await exportPromotionRevenueToExcel(exportData, {
        dateRange,
        selectedStoreNames: storeNames,
        filename: 'top-promotions.xlsx'
      })
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const value: PromotionContextType = {
    dateRange,
    setDateRange,
    selectedCities,
    selectedStores,
    setSelectedCities: handleSetSelectedCities,
    setSelectedStores: handleSetSelectedStores,
    compareCities,
    compareStores,
    setCompareCities,
    setCompareStores,
    selectedStoreIds,
    finalStoreIds,
    compareStoreIds,
    startDate,
    endDate,
    data,
    compareData,
    rawData,
    compareRawData,
    isLoading,
    isCompareLoading,
    tableRows,
    handleUpdateDateRange,
    handleExport
  }

  return <PromotionContext.Provider value={value}>{children}</PromotionContext.Provider>
}

export const usePromotionContext = () => {
  const ctx = useContext(PromotionContext)
  if (!ctx) throw new Error('usePromotionContext must be used within PromotionProvider')
  return ctx
}
