import { useState, useMemo, useEffect } from 'react'

import { ChevronDown, ChevronRight } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { Checkbox } from '@/components/ui/checkbox'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Input } from '@/components/ui/input'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

import { PosModal } from '@/components/pos'

import type { ItemsInCity } from '../data'
import { useUpdateItemBuffetConfig, itemsInCityApiService, useItemsInCityData } from '../hooks'

interface BuffetConfigModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  menuItem: ItemsInCity | null
  menuItems: ItemsInCity[]
  isDetailMode?: boolean // Add prop to determine if in detail mode
}

export function BuffetConfigModal({
  open,
  onOpenChange,
  menuItem,
  menuItems,
  isDetailMode = false
}: BuffetConfigModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [isBuffetItem, setIsBuffetItem] = useState(false)
  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(false)

  const updateBuffetConfigMutation = useUpdateItemBuffetConfig()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  // Get additional items data when not in detail mode
  const { data: additionalItemsData = [] } = useItemsInCityData({
    params: {
      city_uid: menuItem?.city || 'all',
      skip_limit: true,
      active: 1
    },
    enabled: open && !isDetailMode && !!menuItem?.city
  })

  // Convert additional items to ItemsInCity format
  const additionalMenuItems: ItemsInCity[] = additionalItemsData.map(item => ({
    id: item.id || '',
    code: item.item_id || '',
    name: item.item_name || '',
    price: item.ots_price || 0,
    vatPercent: 0,
    categoryGroup: '',
    itemType: '',
    itemClass: '',
    unit: '',
    sideItems: '',
    city: '',
    buffetConfig: '',
    customization: '',
    isActive: item.active || false,
    createdAt: new Date(typeof item.created_at === 'number' ? item.created_at : Date.now()),
    originalData: {
      item_id: item.item_id || '',
      item_code: item.item_id || '',
      item_name: item.item_name || '',
      price: item.ots_price || 0,
      vat_percent: 0,
      category_group: '',
      item_type: '',
      unit: '',
      side_items: '',
      city_name: '',
      buffet_config: '',
      customization_uid: '',
      active: item.active ? 1 : 0,
      created_at: typeof item.created_at === 'number' ? item.created_at : Date.now(),
      store_uid: '',
      store_name: ''
    }
  }))

  // Use additional items if not in detail mode and menuItems is empty
  const effectiveMenuItems = !isDetailMode && menuItems.length === 0 ? additionalMenuItems : menuItems

  useEffect(() => {
    const fetchItemData = async () => {
      if (open && menuItem && menuItem.id && menuItem.id.trim() !== '' && company?.id && selectedBrand?.id) {
        try {
          const apiResponse = await itemsInCityApiService.getItemById({
            id: menuItem.id,
            company_uid: company.id,
            brand_uid: selectedBrand.id
          })

          const realItemData = apiResponse.data as any

          const currentIsBuffetItem = realItemData?.extra_data?.is_buffet_item === 1
          const currentExcludeItemIds = realItemData?.extra_data?.exclude_items_buffet || []

          const excludeItemUuids = new Set<string>()
          currentExcludeItemIds.forEach((itemId: string) => {
            const foundItem = effectiveMenuItems.find(item => item.code === itemId)
            if (foundItem) {
              excludeItemUuids.add(foundItem.id)
            }
          })
          setIsBuffetItem(isDetailMode ? true : currentIsBuffetItem)
          setSelectedItems(excludeItemUuids)
        } catch (error) {
          toast.error('Lỗi khi tải dữ liệu món ăn')
        }
      } else if (open && menuItem) {
        setIsBuffetItem(isDetailMode ? true : false)
        setSelectedItems(new Set<string>())
      } else if (open) {
        setIsBuffetItem(false)
        setSelectedItems(new Set<string>())
      }
    }

    fetchItemData()
  }, [open, menuItem, company?.id, selectedBrand?.id, isDetailMode, effectiveMenuItems.length])

  const baseFilteredItems = useMemo(() => {
    return effectiveMenuItems.filter(item => item.isActive && item.id !== menuItem?.id)
  }, [effectiveMenuItems, menuItem?.id])

  const selectedItemsList = useMemo(() => {
    return baseFilteredItems.filter(item => selectedItems.has(item.id))
  }, [baseFilteredItems, selectedItems])

  const remainingItemsList = useMemo(() => {
    let items = baseFilteredItems.filter(item => !selectedItems.has(item.id))

    if (searchTerm) {
      items = items.filter(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()))
    }

    return items
  }, [baseFilteredItems, selectedItems, searchTerm])

  const handleItemToggle = (itemId: string) => {
    const newSelectedItems = new Set(selectedItems)
    if (newSelectedItems.has(itemId)) {
      newSelectedItems.delete(itemId)
    } else {
      newSelectedItems.add(itemId)
    }
    setSelectedItems(newSelectedItems)
  }

  const handleCancel = () => {
    onOpenChange(false)
    setSearchTerm('')
    setSelectedItems(new Set())
    setIsBuffetItem(false)
  }

  const handleConfirm = async () => {
    try {
      if (!menuItem) {
        toast.error('Không có dữ liệu món ăn')
        return
      }

      if (!menuItem.id || menuItem.id.trim() === '') {
        toast.error('Không thể cập nhật cấu hình buffet cho món ăn mới. Vui lòng lưu món ăn trước.')
        return
      }

      const excludeItemIds = Array.from(selectedItems)
        .map(uuid => {
          const foundItem = effectiveMenuItems.find(item => item.id === uuid)
          if (foundItem) {
            return foundItem.code
          }
          return uuid
        })
        .filter(Boolean)

      await updateBuffetConfigMutation.mutateAsync({
        menuItem,
        isBuffetItem,
        excludeItems: excludeItemIds
      })

      onOpenChange(false)
    } catch (error) {}
  }

  return (
    <PosModal
      title={`Cấu hình buffet cho món ${menuItem?.name}`}
      centerTitle
      open={open}
      onOpenChange={onOpenChange}
      onCancel={handleCancel}
      onConfirm={handleConfirm}
      confirmText='Lưu'
      cancelText='Hủy'
      maxWidth='sm:max-w-2xl'
      confirmDisabled={!menuItem?.id || menuItem.id.trim() === ''}
    >
      <div className='space-y-4'>
        {!isDetailMode && (
          <div className='flex items-center space-x-2'>
            <Checkbox
              id='buffet-item-checkbox'
              checked={isBuffetItem}
              onCheckedChange={checked => setIsBuffetItem(!!checked)}
            />
            <label className='text-sm font-medium' htmlFor='buffet-item-checkbox'>
              Cấu hình món ăn là về buffet
            </label>
          </div>
        )}

        {(isBuffetItem || isDetailMode) && (
          <>
            {isDetailMode ? (
              <div className='space-y-4'>
                <h3 className='text-center text-lg font-medium'>Chọn danh sách món không đi kèm vé buffet</h3>
                <Input
                  placeholder='Tìm kiếm'
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className='w-full'
                />

                <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
                  <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                    <span className='font-medium'>Đã chọn {selectedItemsList.length}</span>
                    {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                  </CollapsibleTrigger>
                  <CollapsibleContent className='mt-2'>
                    <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                      {selectedItemsList.length === 0 ? (
                        <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>
                      ) : (
                        selectedItemsList.map(item => (
                          <label
                            key={item.id}
                            className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                          >
                            <Checkbox
                              checked={selectedItems.has(item.id)}
                              onCheckedChange={() => handleItemToggle(item.id)}
                            />
                            <div className='flex-1'>
                              <p className='text-sm font-medium'>
                                {item.name} - {item.code}
                              </p>
                            </div>
                          </label>
                        ))
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>

                <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
                  <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                    <span className='font-medium'>Còn lại {remainingItemsList.length}</span>
                    {remainingSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                  </CollapsibleTrigger>
                  <CollapsibleContent className='mt-2'>
                    <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                      {remainingItemsList.length === 0 ? (
                        <p className='text-sm text-gray-500'>Không có món nào</p>
                      ) : (
                        remainingItemsList.map(item => (
                          <label
                            key={item.id}
                            className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                          >
                            <Checkbox
                              checked={selectedItems.has(item.id)}
                              onCheckedChange={() => handleItemToggle(item.id)}
                            />
                            <div className='flex-1'>
                              <p className='text-sm font-medium'>
                                {item.name} - {item.code}
                              </p>
                            </div>
                          </label>
                        ))
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            ) : (
              // List mode: Show tabs
              <Tabs defaultValue='exclude' className='w-full'>
                <TabsList className='grid w-full grid-cols-2'>
                  <TabsTrigger value='exclude'>Danh sách món không đi kèm về buffet</TabsTrigger>
                  <TabsTrigger value='upsize'>Danh sách về buffet được upsize</TabsTrigger>
                </TabsList>

                <TabsContent value='exclude' className='space-y-4'>
                  <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
                    <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                      <span className='font-medium'>Đã chọn {selectedItemsList.length}</span>
                      {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                    </CollapsibleTrigger>
                    <CollapsibleContent className='mt-2'>
                      <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                        {selectedItemsList.length === 0 ? (
                          <p className='text-sm text-gray-500'>Chưa có món nào được chọn</p>
                        ) : (
                          selectedItemsList.map(item => (
                            <label
                              key={item.id}
                              className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                            >
                              <Checkbox
                                checked={selectedItems.has(item.id)}
                                onCheckedChange={() => handleItemToggle(item.id)}
                              />
                              <div className='flex-1'>
                                <p className='text-sm font-medium'>
                                  {item.name} - {item.code}
                                </p>
                              </div>
                            </label>
                          ))
                        )}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>

                  <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
                    <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                      <span className='font-medium'>Còn lại {remainingItemsList.length}</span>
                      {remainingSectionOpen ? (
                        <ChevronDown className='h-4 w-4' />
                      ) : (
                        <ChevronRight className='h-4 w-4' />
                      )}
                    </CollapsibleTrigger>
                    <CollapsibleContent className='mt-2'>
                      <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                        {remainingItemsList.length === 0 ? (
                          <p className='text-sm text-gray-500'>Không có món nào</p>
                        ) : (
                          remainingItemsList.map(item => (
                            <label
                              key={item.id}
                              className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                            >
                              <Checkbox
                                checked={selectedItems.has(item.id)}
                                onCheckedChange={() => handleItemToggle(item.id)}
                              />
                              <div className='flex-1'>
                                <p className='text-sm font-medium'>
                                  {item.name} - {item.code}
                                </p>
                              </div>
                            </label>
                          ))
                        )}
                      </div>
                    </CollapsibleContent>
                  </Collapsible>
                </TabsContent>

                <TabsContent value='upsize' className='space-y-4'>
                  <div className='py-8 text-center text-gray-500'>
                    <p>Tính năng upsize sẽ được phát triển trong tương lai</p>
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </>
        )}
      </div>
    </PosModal>
  )
}
