import { useState } from 'react'

import { Trash2, Plus } from 'lucide-react'

import type { Area } from '@/lib/api/pos/areas-api'

import { useDeleteArea } from '@/hooks/api/use-areas'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { TableCell, TableRow } from '@/components/ui/table'

import { ConfirmModal } from '@/components/pos'

interface AreasTableRowProps {
  area: Area
  index: number
  storeUid: string
  isSelected: boolean
  onSelect: (checked: boolean) => void
}

export function AreasTableRow({ area, index, storeUid, isSelected, onSelect }: AreasTableRowProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const { deleteArea, isDeleting } = useDeleteArea()

  const handleDelete = () => {
    deleteArea({ id: area.id, storeUid })
    setDeleteDialogOpen(false)
  }

  const handleAddTables = () => {
    // TODO: Implement add tables functionality
    console.log('Add tables for area:', area.id)
  }

  const formatTableCount = (tableIds: string[]) => {
    const count = tableIds?.length || 0
    return `${count} bàn`
  }

  return (
    <TableRow className='hover:bg-gray-50'>
      <TableCell>
        <Checkbox checked={isSelected} onCheckedChange={onSelect} />
      </TableCell>
      <TableCell className='text-center font-medium'>{index + 1}</TableCell>
      <TableCell>
        <span className='font-medium'>{area.area_name}</span>
      </TableCell>
      <TableCell className='font-mono text-sm'>{area.area_id}</TableCell>
      <TableCell className='text-center'>
        <span className='text-sm'>1 điểm</span>
      </TableCell>
      <TableCell className='text-center'>
        <div className='flex items-center justify-center gap-2'>
          <span className='text-sm'>{formatTableCount(area.list_table_id)}</span>
          <Button
            variant='ghost'
            size='sm'
            className='h-6 w-6 p-0'
            onClick={e => {
              e.stopPropagation()
              handleAddTables()
            }}
          >
            <Plus className='h-4 w-4' />
          </Button>
        </div>
      </TableCell>
      <TableCell className='text-center'>
        <div className='flex items-center justify-center gap-2'>
          <Badge variant={area.active === 1 ? 'default' : 'secondary'}>
            {area.active === 1 ? 'Hoạt động' : 'Không hoạt động'}
          </Badge>
          <Button
            variant='ghost'
            size='sm'
            className='h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700'
            onClick={e => {
              e.stopPropagation()
              setDeleteDialogOpen(true)
            }}
            disabled={isDeleting}
          >
            <Trash2 className='h-4 w-4' />
          </Button>
        </div>

        {/* Confirm Delete Modal */}
        <ConfirmModal
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          title='Xác nhận xóa khu vực'
          content={`Bạn có chắc chắn muốn xóa khu vực "${area.area_name}"? Hành động này không thể hoàn tác.`}
          confirmText='Xóa'
          cancelText='Hủy'
          onConfirm={handleDelete}
        />
      </TableCell>
    </TableRow>
  )
}
