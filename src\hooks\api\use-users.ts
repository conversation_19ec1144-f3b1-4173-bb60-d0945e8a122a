import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

import type { GetUsersParams, CreateUserParams, UpdateUserParams, UpdateUserProfileParams, User } from '@/types'
import type { Store } from '@/types/auth'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { usersApi } from '@/lib/users-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface ApiError extends Error {
  response?: {
    data?: {
      message?: string
    }
  }
}

export interface UseUsersDataParams extends GetUsersParams {
  enabled?: boolean
}

export function useUsersData(params: UseUsersDataParams = {}) {
  const { enabled = true, ...apiParams } = params
  const { company } = useAuthStore(state => state.auth)

  const finalParams: GetUsersParams = {
    company_uid: company?.id,
    ...apiParams
  }

  const hasRequiredAuth = !!(company?.id || finalParams.company_uid)

  return useQuery({
    queryKey: [QUERY_KEYS.USERS_LIST, finalParams],
    queryFn: async () => {
      const response = await usersApi.getUsers(finalParams)
      return response.data || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()
  const { company, stores } = useAuthStore(state => state.auth)

  return useMutation({
    mutationFn: async (params: CreateUserParams & { brand_access?: string[] }) => {
      const companyUid = company?.id || params.company_uid

      if (!companyUid) {
        throw new Error('Company UID is required')
      }

      let permissions = undefined
      if (params.brand_access && params.brand_access.length > 0) {
        permissions = {
          company_uid: companyUid,
          tables: {} as Record<string, string[]>,
          stores: {} as Record<string, Record<string, string[]>>
        }

        const storeData = stores || []

        params.brand_access.forEach(access => {
          if (access.startsWith('store:')) {
            const storeId = access.replace('store:', '')
            const store = storeData.find((s: Store) => s.id === storeId)

            if (store) {
              const brandId = store.brand_uid
              const cityId = store.city_uid

              if (!permissions!.stores[brandId]) {
                permissions!.stores[brandId] = {}
              }
              if (!permissions!.stores[brandId][cityId]) {
                permissions!.stores[brandId][cityId] = []
              }
              permissions!.stores[brandId][cityId].push(storeId)
            }
          }
        })
      }

      const finalParams = {
        email: params.email,
        full_name: params.full_name,
        phone: params.phone,
        role_uid: params.role_uid,
        password: params.password,
        confirm_password: params.confirm_password,
        company_uid: companyUid,
        ...(permissions && { permissions })
      }

      return await usersApi.createUser(finalParams)
    },
    onSuccess: () => {
      toast.success('Tạo nhân viên thành công!')
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.USERS_LIST]
      })
    },
    onError: (error: ApiError) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi tạo nhân viên'
      toast.error(errorMessage)
      throw error
    }
  })
}

export function useUserData(userId: string, enabled: boolean = true) {
  return useQuery({
    queryKey: [QUERY_KEYS.USERS_LIST, userId],
    queryFn: async () => {
      return await usersApi.getUser(userId)
    },
    enabled: enabled && !!userId,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000
  })
}

export function useUpdateUser() {
  const queryClient = useQueryClient()
  const { company, stores } = useAuthStore(state => state.auth)

  return useMutation({
    mutationFn: async ({
      userId,
      params,
      userData
    }: {
      userId: string
      params: UpdateUserParams & { brand_access?: string[] }
      userData?: User
    }) => {
      const permissions = {
        company_uid: company?.id || '',
        tables: {} as Record<string, string[]>,
        stores: {} as Record<string, Record<string, string[]>>
      }

      if (params.brand_access && params.brand_access.length > 0) {
        permissions.stores = {}

        const storeData = stores || userData?.storeDetails || []

        params.brand_access.forEach(access => {
          if (access.startsWith('store:')) {
            const storeId = access.replace('store:', '')
            const store = storeData.find((s: Store) => s.id === storeId)

            if (store) {
              const brandId = store.brand_uid
              const cityId = store.city_uid

              if (!permissions.stores[brandId]) {
                permissions.stores[brandId] = {}
              }
              if (!permissions.stores[brandId][cityId]) {
                permissions.stores[brandId][cityId] = []
              }
              permissions.stores[brandId][cityId].push(storeId)
            }
          }
        })
      } else if (userData?.user_permissions?.stores) {
        permissions.stores = userData.user_permissions.stores as Record<string, Record<string, string[]>>
        permissions.tables = (userData.user_permissions.tables as Record<string, string[]>) || {}
      }

      const profileParams: UpdateUserProfileParams = {
        id: userId,
        phone: params.phone,
        full_name: params.full_name,
        profile_image_path: null,
        role_uid: params.role_uid,
        last_login_at: null,
        is_fabi: 1,
        permissions
      }

      return await usersApi.updateUserProfile(profileParams)
    },
    onSuccess: () => {
      toast.success('Cập nhật nhân viên thành công!')
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.USERS_LIST]
      })
    },
    onError: (error: ApiError) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi cập nhật nhân viên'
      toast.error(errorMessage)
      throw error
    }
  })
}

export function useDeactivateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (userId: string) => {
      return await usersApi.deactivateUserNew(userId)
    },
    onSuccess: () => {
      toast.success('Hủy kích hoạt nhân viên thành công!')
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.USERS_LIST]
      })
    },
    onError: (error: ApiError) => {
      const errorMessage =
        error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi hủy kích hoạt nhân viên'
      toast.error(errorMessage)
      throw error
    }
  })
}

export function useActivateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (userId: string) => {
      return await usersApi.activateUser(userId)
    },
    onSuccess: () => {
      toast.success('Kích hoạt nhân viên thành công!')
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.USERS_LIST]
      })
    },
    onError: (error: ApiError) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi kích hoạt nhân viên'
      toast.error(errorMessage)
      throw error
    }
  })
}

export function useChangePassword() {
  return useMutation({
    mutationFn: async (params: { user_uid: string; new_password: string; confirm_password: string }) => {
      return await usersApi.changePassword(params)
    },
    onError: (error: ApiError) => {
      const errorMessage = error?.response?.data?.message || error?.message || 'Có lỗi xảy ra khi đổi mật khẩu'
      throw new Error(errorMessage)
    }
  })
}
