import { useState } from 'react'

interface UseGroupItemsDragDropResult {
  draggedIndex: number | null
  handleDragStart: (e: React.DragEvent, index: number) => void
  handleDragOver: (e: React.DragEvent) => void
  handleDrop: (e: React.DragEvent, dropIndex: number, items: string[], setItems: (items: string[]) => void) => void
}

export const useGroupItemsDragDrop = (): UseGroupItemsDragDropResult => {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null)

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index)
    e.dataTransfer.effectAllowed = 'move'
    e.dataTransfer.setData('text/html', index.toString())
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (
    e: React.DragEvent, 
    dropIndex: number, 
    items: string[], 
    setItems: (items: string[]) => void
  ) => {
    e.preventDefault()

    if (draggedIndex === null || draggedIndex === dropIndex) {
      setDraggedIndex(null)
      return
    }

    const newItems = [...items]
    const draggedItem = newItems[draggedIndex]

    newItems.splice(draggedIndex, 1)
    newItems.splice(dropIndex, 0, draggedItem)

    setItems(newItems)
    setDraggedIndex(null)
  }

  return {
    draggedIndex,
    handleDragStart,
    handleDragOver,
    handleDrop
  }
}
