import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui'

interface BillingDetailItem {
  id: string
  program_code: string
  program_name: string
  start_time: string
  end_time: string
  status: string
}

interface BillingDetailTableProps {
  data: BillingDetailItem[]
  isLoading: boolean
}

export function BillingDetailTable({ data, isLoading }: BillingDetailTableProps) {
  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch {
      return dateTimeString
    }
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string; className: string }> = {
      'ACTIVE': { label: 'Hoạt động', className: 'bg-green-100 text-green-800' },
      'INACTIVE': { label: 'Không hoạt động', className: 'bg-red-100 text-red-800' },
      'PENDING': { label: 'Chờ xử lý', className: 'bg-yellow-100 text-yellow-800' }
    }
    
    const statusInfo = statusMap[status] || { label: status, className: 'bg-gray-100 text-gray-800' }
    
    return (
      <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${statusInfo.className}`}>
        {statusInfo.label}
      </span>
    )
  }

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-8'>
        <div className='text-muted-foreground'>Đang tải...</div>
      </div>
    )
  }

  return (
    <div className='rounded-md border'>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className='w-12'>#</TableHead>
            <TableHead>ID</TableHead>
            <TableHead>Mã chương trình</TableHead>
            <TableHead>Tên chương trình</TableHead>
            <TableHead>Thời gian bắt đầu</TableHead>
            <TableHead>Thời gian kết thúc</TableHead>
            <TableHead>Trạng thái</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className='text-center py-8 text-muted-foreground'>
                Không có dữ liệu
              </TableCell>
            </TableRow>
          ) : (
            data.map((item, index) => (
              <TableRow key={item.id}>
                <TableCell className='font-medium'>{index + 1}</TableCell>
                <TableCell>{item.id}</TableCell>
                <TableCell>{item.program_code}</TableCell>
                <TableCell>{item.program_name}</TableCell>
                <TableCell>{formatDateTime(item.start_time)}</TableCell>
                <TableCell>{formatDateTime(item.end_time)}</TableCell>
                <TableCell>{getStatusBadge(item.status)}</TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
