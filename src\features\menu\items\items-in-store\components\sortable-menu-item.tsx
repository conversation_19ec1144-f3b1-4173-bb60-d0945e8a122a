import {
  useSortable
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'

import { Card, CardContent } from '@/components/ui/card'

import type { ItemsInStore } from '../data'

interface SortableMenuItemProps {
  item: ItemsInStore
}

export function SortableMenuItem({ item }: SortableMenuItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: item.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 1
  }

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <Card className="cursor-move hover:shadow-md transition-shadow">
        <CardContent className="p-3">
          <div className="aspect-square bg-gray-200 rounded-md mb-2 flex items-center justify-center">
            {item.image_path ? (
              <img
                src={item.image_path}
                alt={item.item_name}
                className="w-full h-full object-cover rounded-md"
              />
            ) : (
              <span className="text-gray-400 text-2xl">🍽️</span>
            )}
          </div>
          <h4 className="font-medium text-sm mb-1 overflow-hidden text-ellipsis" style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {item.item_name}
          </h4>
          <p className="text-sm text-gray-600">
            {item.ots_price?.toLocaleString('vi-VN')} đ
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
