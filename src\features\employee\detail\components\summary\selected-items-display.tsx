import type { Brand, City, Store } from '@/types/auth'

import { useAuthStore } from '@/stores/authStore'

interface BrandWithCities extends Brand {
  cities: (City & { stores: Store[] })[]
}

interface SelectedItemsDisplayProps {
  selectedItems: string[]
}

export function SelectedItemsDisplay({ selectedItems }: SelectedItemsDisplayProps) {
  const { brands, cities, stores } = useAuthStore(state => state.auth)

  const getHierarchicalData = (): BrandWithCities[] => {
    if (!brands || !cities || !stores) return []

    return brands
      .map((brand): BrandWithCities => {
        const brandCities = cities
          .filter(city => city.active === 1)
          .map(city => ({
            ...city,
            stores: stores.filter(
              store => store.active === 1 && store.brand_uid === brand.id && store.city_uid === city.id
            )
          }))
          .filter(city => city.stores.length > 0)

        return {
          ...brand,
          cities: brandCities
        }
      })
      .filter(brand => brand.cities.length > 0)
  }

  const getSelectedSummary = () => {
    const hierarchicalData = getHierarchicalData()
    const selectedSet = new Set(selectedItems)
    const summary: Array<{
      brandName: string
      accessLevel: string
      type: 'brand' | 'city' | 'store'
    }> = []

    hierarchicalData.forEach(brand => {
      if (selectedSet.has(`brand:${brand.id}`)) {
        summary.push({
          brandName: brand.brand_name,
          accessLevel: 'Truy cập toàn thương hiệu',
          type: 'brand'
        })
      } else {
        const selectedCities: string[] = []
        const selectedStores: Array<{ cityName: string; storeName: string }> = []

        brand.cities.forEach(city => {
          if (selectedSet.has(`city:${city.id}`)) {
            selectedCities.push(city.city_name)
          } else {
            city.stores.forEach(store => {
              if (selectedSet.has(`store:${store.id}`)) {
                selectedStores.push({
                  cityName: city.city_name,
                  storeName: store.store_name
                })
              }
            })
          }
        })

        selectedCities.forEach(cityName => {
          summary.push({
            brandName: brand.brand_name,
            accessLevel: `${cityName} - Truy cập toàn thành phố`,
            type: 'city'
          })
        })

        const storesByCity = selectedStores.reduce(
          (acc, store) => {
            if (!acc[store.cityName]) {
              acc[store.cityName] = []
            }
            acc[store.cityName].push(store.storeName)
            return acc
          },
          {} as Record<string, string[]>
        )

        Object.entries(storesByCity).forEach(([cityName, storeNames]) => {
          summary.push({
            brandName: brand.brand_name,
            accessLevel: `${cityName} - ${storeNames.join(', ')}`,
            type: 'store'
          })
        })
      }
    })

    return summary
  }

  const selectedSummary = getSelectedSummary()

  if (selectedSummary.length === 0) {
    return (
      <div className='rounded-md border border-dashed border-gray-300 bg-gray-50 p-6'>
        <div className='text-center text-sm text-gray-500'></div>
      </div>
    )
  }

  return (
    <div className='space-y-3'>
      <div className='rounded-md border bg-gray-50'>
        {selectedSummary.map((item, index) => (
          <div key={index} className='grid grid-cols-2 border-b bg-white last:border-b-0 hover:bg-gray-50'>
            <div className='p-3 text-sm font-medium text-gray-700'>{item.brandName}</div>
            <div className='p-3 text-sm text-gray-600'>{item.accessLevel}</div>
          </div>
        ))}
      </div>
    </div>
  )
}
