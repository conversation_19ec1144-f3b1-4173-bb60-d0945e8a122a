import { useState } from 'react'
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { format, addMonths, subMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isWithinInterval } from 'date-fns'

interface DateRangePickerProps {
  startDate: Date | null
  endDate: Date | null
  onDateChange: (start: Date | null, end: Date | null) => void
  dateRange: string
  onDateRangeChange: (value: string) => void
}

export function DateRangePicker({
  startDate,
  endDate,
  onDateChange,
  dateRange,
  onDateRangeChange
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [tempStartDate, setTempStartDate] = useState<Date | null>(null)
  const [hoverDate, setHoverDate] = useState<Date | null>(null)
  const [currentMonth, setCurrentMonth] = useState(new Date())

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (!open) {
      setTempStartDate(null)
      setHoverDate(null)
    }
  }

  const handleDateSelect = (date: Date) => {
    if (!tempStartDate || (tempStartDate && endDate)) {
      setTempStartDate(date)
      setHoverDate(null)
      onDateChange(date, null)
    } else {
      if (date >= tempStartDate) {
        onDateChange(tempStartDate, date)
      } else {
        onDateChange(date, tempStartDate)
      }
      setTempStartDate(null)
      setHoverDate(null)
      setIsOpen(false)
    }
  }

  const generateCalendarDays = () => {
    const monthStart = startOfMonth(currentMonth)
    const monthEnd = endOfMonth(currentMonth)
    const startDate = new Date(monthStart)
    startDate.setDate(startDate.getDate() - monthStart.getDay())

    const endDate = new Date(monthEnd)
    endDate.setDate(endDate.getDate() + (6 - monthEnd.getDay()))

    return eachDayOfInterval({ start: startDate, end: endDate })
  }

  const isDateInRange = (date: Date) => {
    if (startDate && endDate) {
      return isWithinInterval(date, { start: startDate, end: endDate })
    }

    if (tempStartDate && hoverDate) {
      const rangeStart = tempStartDate < hoverDate ? tempStartDate : hoverDate
      const rangeEnd = tempStartDate < hoverDate ? hoverDate : tempStartDate
      return isWithinInterval(date, { start: rangeStart, end: rangeEnd })
    }

    return false
  }

  const isDateSelected = (date: Date) => {
    if (startDate && isSameDay(date, startDate)) return 'start'
    if (endDate && isSameDay(date, endDate)) return 'end'
    if (tempStartDate && isSameDay(date, tempStartDate)) return 'temp'
    return false
  }

  const getDayClassName = (date: Date) => {
    const selected = isDateSelected(date)
    const inRange = isDateInRange(date)
    const isCurrentMonth = isSameMonth(date, currentMonth)
    const isToday = isSameDay(date, new Date())
    const isHovering = hoverDate && isSameDay(date, hoverDate)

    let className = 'h-8 w-8 p-0 text-xs font-normal transition-colors '

    if (!isCurrentMonth) {
      className += 'text-gray-400 '
    } else {
      className += 'text-gray-900 '
    }

    if (selected === 'start' || selected === 'end') {
      className += 'bg-blue-500 text-white hover:bg-blue-600 '
    } else if (selected === 'temp') {
      className += 'bg-blue-400 text-white hover:bg-blue-500 '
    } else if (isHovering && tempStartDate) {
      className += 'bg-blue-400 text-white '
    } else if (inRange) {
      className += 'bg-blue-100 text-blue-900 hover:bg-blue-200 '
    } else {
      className += 'hover:bg-gray-100 '
    }

    if (isToday && !selected && !inRange && !isHovering) {
      className += 'text-red-500 font-bold '
    }

    return className
  }

  return (
    <div className="flex items-center gap-2">
      <Input
        value={dateRange}
        onChange={(e) => onDateRangeChange(e.target.value)}
        placeholder="dd/mm/yyyy - dd/mm/yyyy"
        className="w-64 text-xs"
      />
      <Popover open={isOpen} onOpenChange={handleOpenChange}>
        <PopoverTrigger asChild>
          <CalendarIcon className="h-4 w-4 text-gray-500 cursor-pointer hover:text-gray-700" />
        </PopoverTrigger>
        <PopoverContent className="w-auto p-4" align="end">
          <div className="space-y-4">
            {startDate && endDate && (
              <div className="text-center text-sm font-medium text-blue-600 bg-blue-50 p-2 rounded">
                {format(startDate, 'dd/MM/yyyy')} - {format(endDate, 'dd/MM/yyyy')}
              </div>
            )}

            <div className="flex items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMonth(subMonths(currentMonth, 1))}
                className="h-7 w-7 p-0"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <div className="text-sm font-medium">
                {format(currentMonth, 'MMMM yyyy')}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMonth(addMonths(currentMonth, 1))}
                className="h-7 w-7 p-0"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <div className="grid grid-cols-7 gap-1">
              {['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'].map(day => (
                <div key={day} className="text-center text-xs font-medium text-gray-500 p-2">
                  {day}
                </div>
              ))}

              {generateCalendarDays().map((date, index) => (
                <div key={index} className="text-center">
                  <Button
                    variant="ghost"
                    className={getDayClassName(date)}
                    onClick={() => handleDateSelect(date)}
                    onMouseEnter={() => {
                      if (tempStartDate && !endDate && isSameMonth(date, currentMonth)) {
                        setHoverDate(date)
                      }
                    }}
                    onMouseLeave={() => {
                      if (tempStartDate && !endDate) {
                        setHoverDate(null)
                      }
                    }}
                  >
                    {date.getDate()}
                  </Button>
                </div>
              ))}
            </div>

            <div className="text-xs text-gray-500 text-center border-t pt-3">
              {tempStartDate && hoverDate ? (
                <span className="text-blue-600 font-medium">
                  {format(tempStartDate < hoverDate ? tempStartDate : hoverDate, 'dd/MM/yyyy')} - {format(tempStartDate < hoverDate ? hoverDate : tempStartDate, 'dd/MM/yyyy')}
                </span>
              ) : tempStartDate ? (
                'Chọn ngày kết thúc'
              ) : (
                'Chọn ngày bắt đầu'
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
