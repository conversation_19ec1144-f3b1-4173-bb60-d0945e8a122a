import { useQuery } from '@tanstack/react-query'

import type { PromotionSaleSummaryParams, PromotionSaleSummaryResponse } from '@/types/api/revenue-promotion'

import { getPromotionSaleSummary } from '@/lib/api/pos/promotion-sale-summary-api'

import { QUERY_KEYS } from '@/constants/query-keys'

interface UsePromotionSaleSummaryOptions {
  params: PromotionSaleSummaryParams
  enabled?: boolean
}

export const usePromotionSaleSummary = ({ params, enabled = true }: UsePromotionSaleSummaryOptions) => {
  const queryKey = [
    QUERY_KEYS.PROMOTIONS,
    'sale-summary-top',
    params.company_uid,
    params.brand_uid,
    params.start_date,
    params.end_date,
    params.list_store_uid,
    params.limit
  ]

  const queryFn = async (): Promise<PromotionSaleSummaryResponse> => {
    if (!params.list_store_uid) {
      return { data: [], message: 'No stores selected', track_id: '' }
    }

    return getPromotionSaleSummary(params)
  }

  const query = useQuery({
    queryKey,
    queryFn,
    enabled: enabled && !!params.list_store_uid,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false
  })

  return {
    data: query.data?.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasData: (query.data?.data || []).length > 0,
    message: query.data?.message || '',
    trackId: query.data?.track_id || ''
  }
}
