.minimal-tiptap-editor .ProseMirror .heading-node {
  @apply relative font-semibold;
}

.minimal-tiptap-editor .ProseMirror .heading-node:first-child {
  @apply mt-0;
}

.minimal-tiptap-editor .ProseMirror h1 {
  @apply mt-[46px] mb-4 text-[1.375rem] leading-7 tracking-[-0.004375rem];
}

.minimal-tiptap-editor .ProseMirror h2 {
  @apply mt-8 mb-3.5 text-[1.1875rem] leading-7 tracking-[0.003125rem];
}

.minimal-tiptap-editor .ProseMirror h3 {
  @apply mt-6 mb-3 text-[1.0625rem] leading-6 tracking-[0.00625rem];
}

.minimal-tiptap-editor .ProseMirror h4 {
  @apply mt-4 mb-2 text-[0.9375rem] leading-6;
}

.minimal-tiptap-editor .ProseMirror h5 {
  @apply mt-4 mb-2 text-sm;
}

.minimal-tiptap-editor .ProseMirror h5 {
  @apply mt-4 mb-2 text-sm;
}

.minimal-tiptap-editor .ProseMirror a.link {
  @apply text-primary underline;
}

.minimal-tiptap-editor .ProseMirror a.link:hover {
  @apply underline;
}
