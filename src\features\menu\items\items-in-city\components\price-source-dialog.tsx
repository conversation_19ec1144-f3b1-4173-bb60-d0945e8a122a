import { useState } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { useSourcesData } from '@/hooks/api/use-sources'

import { Button } from '@/components/ui/button'
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

const priceSourceSchema = z.object({
  source: z.string().min(1, 'Vui lòng chọn nguồn đơn'),
  amount: z.coerce.number().min(0, 'Số tiền phải lớn hơn hoặc bằng 0')
})

type PriceSourceFormValues = z.infer<typeof priceSourceSchema>

const formatNumber = (value: string): string => {
  const numericValue = value.replace(/\D/g, '')
  return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const parseNumber = (value: string): number => {
  return parseInt(value.replace(/,/g, ''), 10) || 0
}

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm?: (data: PriceSourceFormValues) => void
  cityUid: string
}

export function PriceSourceDialog({ open, onOpenChange, onConfirm, cityUid }: Props) {
  const [formattedAmount, setFormattedAmount] = useState('')

  const form = useForm<PriceSourceFormValues>({
    resolver: zodResolver(priceSourceSchema),
    defaultValues: {
      source: '',
      amount: 0
    }
  })

  const onSubmit = (data: PriceSourceFormValues) => {
    const selectedSource = sources.find(s => s.id === data.source)
    const dataWithSourceName = {
      ...data,
      sourceName: selectedSource?.name || data.source
    }
    onConfirm?.(dataWithSourceName)
    onOpenChange(false)
    form.reset()
    setFormattedAmount('')
  }

  const handleFormSubmit = (event: React.FormEvent) => {
    event.preventDefault()
    event.stopPropagation()
    form.handleSubmit(onSubmit)(event)
  }

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen)
    if (!newOpen) {
      form.reset()
      setFormattedAmount('')
    }
  }

  const { data: sourcesData = [] } = useSourcesData({
    enabled: open && !!cityUid,
    skip_limit: true,
    city_uid: cityUid
  })

  const sources = sourcesData.map((source: any) => ({
    id: source.id,
    name: source.sourceName || source.source_name || source.name
  }))

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle>Cấu hình giá theo nguồn</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleFormSubmit} className='space-y-4'>
            <div className='grid grid-cols-[120px_1fr] items-center gap-4'>
              {/* Nguồn đơn */}
              <FormLabel className='text-sm font-medium text-gray-600'>Nguồn đơn</FormLabel>
              <FormField
                control={form.control}
                name='source'
                render={({ field }) => (
                  <FormItem>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className='w-full'>
                          <SelectValue placeholder='Chọn nguồn đơn' />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {sources.map(source => (
                          <SelectItem key={source.id} value={source.id}>
                            {source.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className='grid grid-cols-[120px_1fr] items-center gap-4'>
              {/* Số tiền */}
              <FormLabel className='text-sm font-medium text-gray-600'>Số tiền</FormLabel>
              <FormField
                control={form.control}
                name='amount'
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        type='text'
                        placeholder='0'
                        value={formattedAmount}
                        onChange={e => {
                          const inputValue = e.target.value
                          const formatted = formatNumber(inputValue)
                          setFormattedAmount(formatted)

                          const numericValue = parseNumber(formatted)
                          field.onChange(numericValue)
                        }}
                        onKeyDown={e => {
                          if (
                            !/[0-9]/.test(e.key) &&
                            !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)
                          ) {
                            e.preventDefault()
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className='pt-4'>
              <DialogClose asChild>
                <Button type='button' variant='outline'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='submit'>Xác nhận</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
