import { useMemo } from 'react'

import type { User } from '@/types'

import type { BrandWithCities, EmployeeData, UserData } from '../types'

interface SummaryItem {
  brandName: string
  accessLevel: string
  type: 'brand' | 'city' | 'store'
}

interface UseSelectedItemsSummaryProps {
  hierarchicalData?: BrandWithCities[]
  localSelectedItems?: Set<string>
  employeeData?: EmployeeData
  userData?: User | UserData
}

export function useSelectedItemsSummary({
  hierarchicalData,
  localSelectedItems,
  employeeData,
  userData
}: UseSelectedItemsSummaryProps) {
  const employeeDataSummary = useMemo((): SummaryItem[] => {
    if (!employeeData) return []

    const { brands, cities, stores, user_permissions } = employeeData
    const summary: SummaryItem[] = []

    if (!user_permissions.stores || Object.keys(user_permissions.stores).length === 0) {
      return summary
    }

    Object.entries(user_permissions.stores).forEach(([brandUid, cityStores]) => {
      const brand = brands.find(b => b.id === brandUid)
      if (!brand) return

      if (!cityStores || typeof cityStores !== 'object') return

      Object.entries(cityStores as Record<string, unknown[]>).forEach(([cityUid, storeAccess]) => {
        const city = cities.find(c => c.id === cityUid)
        if (!city) return

        if (Array.isArray(storeAccess) && storeAccess.length === 0) {
          summary.push({
            brandName: brand.brand_name,
            accessLevel: `${city.city_name} - Truy cập toàn thành phố`,
            type: 'city'
          })
        } else if (Array.isArray(storeAccess) && storeAccess.length > 0) {
          const storeNames = storeAccess
            .map(storeId => {
              const store = stores.find(s => s.id === storeId)
              return store?.store_name
            })
            .filter(Boolean)

          if (storeNames.length > 0) {
            summary.push({
              brandName: brand.brand_name,
              accessLevel: `${city.city_name} - ${storeNames.join(', ')}`,
              type: 'store'
            })
          }
        }
      })
    })

    return summary
  }, [employeeData])

  const userDataSummary = useMemo((): SummaryItem[] => {
    if (!userData) return []

    const summary: SummaryItem[] = []

    if (!userData.brand_access || userData.brand_access.length === 0) {
      return summary
    }

    userData.brand_access.forEach(access => {
      if (access.startsWith('brand:')) {
        summary.push({
          brandName: 'Thương hiệu',
          accessLevel: 'Truy cập toàn thương hiệu',
          type: 'brand'
        })
      } else if (access.startsWith('city:')) {
        summary.push({
          brandName: 'Thương hiệu',
          accessLevel: 'Truy cập toàn thành phố',
          type: 'city'
        })
      } else if (access.startsWith('store:')) {
        summary.push({
          brandName: 'Thương hiệu',
          accessLevel: 'Truy cập cửa hàng cụ thể',
          type: 'store'
        })
      }
    })

    return summary
  }, [userData])

  const selectedSummary = useMemo((): SummaryItem[] => {
    if (!hierarchicalData || !localSelectedItems) return []

    const summary: SummaryItem[] = []

    const isCityFullySelected = (city: any): boolean => {
      return city.stores.every((store: any) => localSelectedItems.has(`store:${store.id}`))
    }

    const isBrandFullySelected = (brand: any): boolean => {
      return brand.cities.every((city: any) => isCityFullySelected(city))
    }

    hierarchicalData.forEach(brand => {
      if (isBrandFullySelected(brand)) {
        summary.push({
          brandName: brand.brand_name,
          accessLevel: 'Truy cập toàn thương hiệu',
          type: 'brand'
        })
      } else {
        const fullySelectedCities: string[] = []
        const partiallySelectedStores: Array<{ cityName: string; storeName: string }> = []

        brand.cities.forEach(city => {
          if (isCityFullySelected(city)) {
            fullySelectedCities.push(city.city_name)
          } else {
            city.stores.forEach((store: any) => {
              if (localSelectedItems.has(`store:${store.id}`)) {
                partiallySelectedStores.push({
                  cityName: city.city_name,
                  storeName: store.store_name
                })
              }
            })
          }
        })

        fullySelectedCities.forEach(cityName => {
          summary.push({
            brandName: brand.brand_name,
            accessLevel: `${cityName} - Truy cập toàn thành phố`,
            type: 'city'
          })
        })

        const storesByCity = partiallySelectedStores.reduce(
          (acc, store) => {
            if (!acc[store.cityName]) {
              acc[store.cityName] = []
            }
            acc[store.cityName].push(store.storeName)
            return acc
          },
          {} as Record<string, string[]>
        )

        Object.entries(storesByCity).forEach(([cityName, storeNames]) => {
          summary.push({
            brandName: brand.brand_name,
            accessLevel: `${cityName} - ${storeNames.join(', ')}`,
            type: 'store'
          })
        })
      }
    })

    return summary
  }, [hierarchicalData, localSelectedItems])

  const getSelectedStoreCount = useMemo((): number => {
    if (!hierarchicalData || !localSelectedItems || employeeData || userData) {
      return 0
    }

    let count = 0
    hierarchicalData.forEach(brand => {
      brand.cities.forEach(city => {
        city.stores.forEach(store => {
          if (localSelectedItems.has(`store:${store.id}`)) {
            count++
          }
        })
      })
    })
    return count
  }, [hierarchicalData, localSelectedItems, employeeData, userData])

  const finalSummary = employeeData ? employeeDataSummary : userData ? userDataSummary : selectedSummary

  const summaryType = employeeData || userData ? 'current' : 'selected'

  return {
    summary: finalSummary,
    summaryType,
    isEmpty: finalSummary.length === 0,
    selectedStoreCount: getSelectedStoreCount
  }
}
