name: Continuous Integration

on:
  push:
    branches:
      - main
      
  pull_request:
    branches:
      - main

jobs:
  install-lint-build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install pnpm
        run: npm install -g pnpm  

      - name: Install dependencies
        run: pnpm install

      - name: Lint the code
        run: pnpm lint

      # - name: Analyze unused files and dependencies
      #   run: pnpm knip

      - name: Run Prettier check
        run: pnpm format:check

      - name: Build the project
        run: pnpm build
