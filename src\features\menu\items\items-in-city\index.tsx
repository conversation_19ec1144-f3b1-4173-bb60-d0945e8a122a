import { useState, useMemo } from 'react'

import { useSearch } from '@tanstack/react-router'

import { useCustomizationsData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'

import {
  createColumns,
  ItemsInCityButtons,
  ItemsInCityTableSkeleton,
  ItemsInCityDialogs,
  ItemsInCityDataTable,
  CustomizationDialog,
  BuffetConfigModal
} from './components'
import MenuItemsProvider, { useItemsInCity } from './context'
import { ItemsInCity } from './data'
import { useItemsInCityForTable, useUpdateItemInCityStatus } from './hooks'

function ItemsInCityContent() {
  const { open, setOpen, setCurrentRow } = useItemsInCity()
  const search = useSearch({ strict: false }) as any
  const [isCustomizationDialogOpen, setIsCustomizationDialogOpen] = useState(false)
  const [selectedMenuItem, setSelectedMenuItem] = useState<ItemsInCity | null>(null)
  const [isBuffetConfigModalOpen, setIsBuffetConfigModalOpen] = useState(false)
  const [selectedBuffetMenuItem, setSelectedBuffetMenuItem] = useState<ItemsInCity | null>(null)
  const [selectedItemTypeUid, setSelectedItemTypeUid] = useState<string>('all')
  const [selectedCityUid, setSelectedCityUid] = useState<string>('all')
  const [selectedDaysOfWeek, setSelectedDaysOfWeek] = useState<string[]>([])
  const [selectedStatus, setSelectedStatus] = useState<string>('all')

  const updateStatusMutation = useUpdateItemInCityStatus()

  // Get current page from URL search params
  const currentPage = useMemo(() => {
    const page = search?.page
    return typeof page === 'number' && page > 0 ? page : 1
  }, [search?.page])

  const filterParams = useMemo(() => {
    const params: Record<string, string | number> = {}
    if (selectedItemTypeUid !== 'all') {
      params.item_type_uid = selectedItemTypeUid
    }
    if (selectedCityUid !== 'all') {
      params.city_uid = selectedCityUid
    }
    if (selectedDaysOfWeek.length > 0) {
      params.time_sale_date_week = selectedDaysOfWeek.join(',')
    }

    if (selectedStatus !== 'all') {
      params.active = parseInt(selectedStatus, 10)
    }
    return params
  }, [selectedItemTypeUid, selectedCityUid, selectedDaysOfWeek, selectedStatus, currentPage])

  const {
    data: menuItems = [],
    isLoading: itemsLoading,
    error: itemsError
  } = useItemsInCityForTable({
    params: filterParams
  })

  const getAllCityUids = (): string[] => {
    try {
      const citiesData = localStorage.getItem('pos_cities_data')
      if (citiesData) {
        const cities: Array<{ id: string }> = JSON.parse(citiesData)
        return cities.map(city => city.id)
      }
    } catch {}
    return []
  }

  const customizationCityUids = selectedCityUid !== 'all' ? [selectedCityUid] : getAllCityUids()

  const { data: customizations = [] } = useCustomizationsData({
    skip_limit: true,
    list_city_uid: customizationCityUids.length > 0 ? customizationCityUids : undefined
  })

  const isLoading = itemsLoading
  const error = itemsError

  const handleCustomizationClick = (menuItem: ItemsInCity) => {
    setSelectedMenuItem(menuItem)
    setIsCustomizationDialogOpen(true)
  }

  const handleBuffetConfigClick = (menuItem: ItemsInCity) => {
    setSelectedBuffetMenuItem(menuItem)
    setIsBuffetConfigModalOpen(true)
  }

  const handleCopyClick = (menuItem: ItemsInCity) => {
    setCurrentRow(menuItem)
    setOpen('copy')
  }

  const handleDeleteClick = (menuItem: ItemsInCity) => {
    setCurrentRow(menuItem)
    setOpen('delete')
  }

  const handleRowClick = (menuItem: ItemsInCity) => {
    setCurrentRow(menuItem)
    setOpen('update')
  }

  const handleToggleStatus = async (menuItem: ItemsInCity) => {
    const newStatus = menuItem.isActive ? 0 : 1
    await updateStatusMutation.mutateAsync({
      id: menuItem.id,
      active: newStatus
    })
  }

  const columns = createColumns({ onBuffetConfigClick: handleBuffetConfigClick })

  if (error) {
    return (
      <div className='flex items-center justify-center p-8'>
        <div className='text-center'>
          <p className='text-muted-foreground mb-2 text-sm'>Có lỗi xảy ra khi tải dữ liệu</p>
          <p className='text-muted-foreground text-xs'>
            {itemsError && `Món ăn: ${itemsError?.message || 'Lỗi không xác định'}`}
          </p>
        </div>
      </div>
    )
  }

  return (
    <>
      {!open && (
        <>
          <Header>
            <div className='ml-auto flex items-center space-x-4'>
              <Search />
              <ThemeSwitch />
              <ProfileDropdown />
            </div>
          </Header>

          <Main>
            <div className='mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4'>
              <div>
                <h2 className='text-2xl font-bold tracking-tight'>Món ăn tại thành phố</h2>
              </div>
              <ItemsInCityButtons />
            </div>
            <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
              {isLoading && <ItemsInCityTableSkeleton />}
              {!isLoading && (
                <ItemsInCityDataTable
                  columns={columns}
                  data={menuItems}
                  onCustomizationClick={handleCustomizationClick}
                  onCopyClick={handleCopyClick}
                  onToggleStatus={handleToggleStatus}
                  onRowClick={handleRowClick}
                  onDeleteClick={handleDeleteClick}
                  customizations={customizations}
                  selectedItemTypeUid={selectedItemTypeUid}
                  onItemTypeChange={setSelectedItemTypeUid}
                  selectedCityUid={selectedCityUid}
                  onCityChange={setSelectedCityUid}
                  selectedDaysOfWeek={selectedDaysOfWeek}
                  onDaysOfWeekChange={setSelectedDaysOfWeek}
                  selectedStatus={selectedStatus}
                  onStatusChange={setSelectedStatus}
                />
              )}
            </div>
          </Main>
        </>
      )}

      <ItemsInCityDialogs />

      {isCustomizationDialogOpen && selectedMenuItem && (
        <CustomizationDialog
          open={isCustomizationDialogOpen}
          onOpenChange={setIsCustomizationDialogOpen}
          menuItem={selectedMenuItem}
          menuItems={menuItems}
          customizations={customizations}
        />
      )}

      {isBuffetConfigModalOpen && selectedBuffetMenuItem && (
        <BuffetConfigModal
          open={isBuffetConfigModalOpen}
          onOpenChange={setIsBuffetConfigModalOpen}
          menuItem={selectedBuffetMenuItem}
          menuItems={menuItems}
          isDetailMode={false}
        />
      )}
    </>
  )
}

export default function ItemsInCityPage() {
  return (
    <MenuItemsProvider>
      <ItemsInCityContent />
    </MenuItemsProvider>
  )
}
