import { useQuery } from '@tanstack/react-query'

import { api } from '@/lib/api'

interface InvoiceSourceDetailParams {
  company_uid: string
  brand_uid: string
  start_date: number
  end_date: number
  source_id: string
  list_store_uid: string | string[]
  page?: number
  results_per_page?: number
}

interface InvoiceSourceDetailResponse {
  data: Array<{
    id: string
    tran_id: string
    tran_no: string
    table_name: string
    source_deli: string
    total_amount: number
    tran_date: number
    employee_name: string
    sale_detail: Array<{
      item_name: string
      quantity: number
      price: number
    }>
    sale_payment_method: Array<{
      payment_method_name: string
      trace_no: string
    }>
    extra_data: {
      tran_no_partner: string
    }
  }>
  track_id?: string
}

export const useInvoiceSourceDetail = (params: InvoiceSourceDetailParams, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['invoice-source-detail', params],
    queryFn: async (): Promise<InvoiceSourceDetailResponse> => {
      const storeUids = Array.isArray(params.list_store_uid) ? params.list_store_uid.join(',') : params.list_store_uid

      const searchParams = new URLSearchParams({
        company_uid: params.company_uid,
        brand_uid: params.brand_uid,
        start_date: params.start_date.toString(),
        end_date: params.end_date.toString(),
        source_id: params.source_id,
        list_store_uid: storeUids,
        page: (params.page || 1).toString(),
        results_per_page: (params.results_per_page || 20).toString()
      })

      const response = await api.get(`/v3/pos-cms/report/sale-by-source?${searchParams.toString()}`)
      return response.data as InvoiceSourceDetailResponse
    },
    enabled: enabled && !!params.source_id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false
  })
}
