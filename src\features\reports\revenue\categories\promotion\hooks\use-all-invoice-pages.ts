import { useMemo } from 'react'

import { useQueries } from '@tanstack/react-query'

import { api } from '@/lib/api'

interface ApiInvoiceData {
  id?: string
  tran_id?: string
  tran_no?: string
  table_name?: string
  source_deli?: string
  total_amount?: number
  tran_date?: number
  employee_name?: string
  sale_detail?: Array<{
    item_name?: string
    name?: string
    quantity?: number
    price?: number
    toppings?: Array<{ name?: string; item_name?: string }>
    change_data?: { sale_detail: Array<{ name?: string; item_name?: string }> }
  }>
  sale_payment_method?: Array<{
    payment_method_name?: string
    trace_no?: string
  }>
  extra_data?: {
    tran_no_partner?: string
  }
}

interface InvoiceEntry {
  id: string
  tran_id: string
  invoiceCode: string
  partnerInvoiceNumber: string
  branchName: string
  sourceType: string
  amount: number
  dateTime: string
  items?: Array<{
    name: string
    item_name?: string
    quantity: number
    price: number
    toppings?: Array<{
      name?: string
      item_name?: string
    }>
    change_data?: {
      sale_detail: Array<{
        name?: string
        item_name?: string
      }>
    }
  }>
  paymentMethod?: string
  paymentReference?: string
  employeeName?: string
  tranDate?: number
}

interface UseAllInvoicePagesParams {
  companyUid?: string
  brandUid?: string
  startDate?: number
  endDate?: number
  promotionId?: string
  storeUids?: string[]
  fallbackStartDate: number
  fallbackEndDate: number
  shouldFetchApi: boolean
}

export const useAllInvoicePages = ({
  companyUid,
  brandUid,
  startDate,
  endDate,
  promotionId,
  storeUids,
  fallbackStartDate,
  fallbackEndDate,
  shouldFetchApi
}: UseAllInvoicePagesParams) => {
  const finalStart = startDate || fallbackStartDate
  const finalEnd = endDate || fallbackEndDate

  // Create queries for each store individually
  const storeQueries = useMemo(() => {
    if (!shouldFetchApi || !companyUid || !brandUid || !promotionId || !storeUids?.length) {
      return []
    }

    return storeUids.map(storeUid => ({
      queryKey: [
        'invoice-source-detail',
        {
          company_uid: companyUid,
          brand_uid: brandUid,
          start_date: finalStart,
          end_date: finalEnd,
          promotion_id: promotionId,
          list_store_uid: storeUid,
          page: 1,
          results_per_page: 20
        }
      ],
      queryFn: async () => {
        const sp = new URLSearchParams({
          company_uid: companyUid,
          brand_uid: brandUid,
          start_date: String(finalStart),
          end_date: String(finalEnd),
          promotion_id: String(promotionId),
          list_store_uid: storeUid,
          page: '1',
          results_per_page: '20',
          by_days: '1',
          is_sales: '1'
        })
        const resp = await api.get(`/v3/pos-cms/report/sale_by_promotion?${sp.toString()}`)
        return resp.data as { data: ApiInvoiceData[]; total?: number }
      },
      enabled: shouldFetchApi && !!companyUid && !!brandUid && !!promotionId,
      staleTime: 5 * 60 * 1000,
      gcTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false
    }))
  }, [shouldFetchApi, companyUid, brandUid, promotionId, storeUids, finalStart, finalEnd])

  const queryResults = useQueries({
    queries: storeQueries
  })

  // Combine data from all queries
  const allData = useMemo(() => {
    const combined: unknown[] = []
    queryResults.forEach(result => {
      if (result.data?.data) {
        combined.push(...result.data.data)
      }
    })
    return combined
  }, [queryResults.map(r => r.data)])

  const isLoading = queryResults.some(r => r.isLoading)
  const error = queryResults.find(r => r.error)?.error || null
  const data = allData

  const invoices: InvoiceEntry[] = useMemo(() => {
    const list = (data || []) as ApiInvoiceData[]
    const mapped = list.map((invoice: ApiInvoiceData) => {
      const items = (invoice.sale_detail || []).map(
        (item: {
          item_name?: string
          name?: string
          quantity?: number
          price?: number
          toppings?: Array<{ name?: string; item_name?: string }>
          change_data?: { sale_detail: Array<{ name?: string; item_name?: string }> }
        }) => ({
          name: item.item_name || item.name || 'N/A',
          item_name: item.item_name || item.name || 'N/A',
          quantity: item.quantity || 0,
          price: item.price || 0,
          toppings: item.toppings || [],
          change_data: item.change_data || { sale_detail: [] }
        })
      )

      const paymentMethod = invoice.sale_payment_method?.[0]?.payment_method_name || 'N/A'
      const paymentReference = invoice.sale_payment_method?.[0]?.trace_no || ''
      const partnerInvoiceNumber = invoice.extra_data?.tran_no_partner || invoice.tran_no || 'N/A'

      const formatDate = (timestamp: number) => {
        if (!timestamp) return new Date().toISOString()
        const date = new Date(timestamp)
        return date.toLocaleString('vi-VN', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      return {
        id: invoice.id || `invoice-${Math.random()}`,
        tran_id: invoice.tran_id || '',
        invoiceCode: invoice.tran_no || 'N/A',
        partnerInvoiceNumber: partnerInvoiceNumber,
        branchName: invoice.table_name || 'N/A',
        sourceType: invoice.source_deli || 'N/A',
        amount: invoice.total_amount || 0,
        dateTime: formatDate(invoice.tran_date || 0),
        items: items,
        paymentMethod: paymentMethod,
        paymentReference: paymentReference,
        employeeName: invoice.employee_name || 'Ca 1 Sekai Bà Hạt',
        tranDate: invoice.tran_date
      }
    }) as InvoiceEntry[]
    return mapped
  }, [data])

  return {
    invoices,
    isLoading,
    error,
    hasMorePages: false,
    currentPage: 1,
    totalInvoices: invoices.length
  }
}

export type { InvoiceEntry }
