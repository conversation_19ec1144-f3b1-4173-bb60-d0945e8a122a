import * as XLSX from 'xlsx'
import { downloadExcel } from '@/utils/excel-util'

export const createQuantityDayTemplate = (): void => {
  const templateData = [
    ['Món áp dụng', '<PERSON><PERSON> lượng', '<PERSON><PERSON><PERSON> bắt đầu', '<PERSON><PERSON><PERSON> kết thúc', '<PERSON>ọ<PERSON> ngày']
  ]

  const worksheet = XLSX.utils.aoa_to_sheet(templateData)

  worksheet['!cols'] = [
    { width: 25 },
    { width: 12 },
    { width: 18 },
    { width: 18 },
    { width: 15 }
  ]

  const headerStyle = {
    fill: { fgColor: { rgb: "4F81BD" } },
    font: { color: { rgb: "FFFFFF" }, bold: true },
    alignment: { horizontal: "center", vertical: "center" }
  }

  const headerCells = ['A1', 'B1', 'C1', 'D1', 'E1']
  headerCells.forEach(cell => {
    if (!worksheet[cell]) worksheet[cell] = { t: 's', v: '' }
    worksheet[cell].s = headerStyle
  })

  const exampleData = [
    ['Đ<PERSON>y là sheet mẫu để tham khảo. Vui lòng quay lại sheet 1 để nhập dữ liệu.'],
    [''],
    ['Món áp dụng', 'Số lượng', 'Ngày bắt đầu', 'Ngày kết thúc', 'Chọn ngày'],
    ['ITEM-DF47,ITEM-OTA2,ITEM-2ML3', '12', '01/02/2023', '01/03/2023', '38'],
    ['ITEM_AK47,ITEM-RNEA,ITEM-NJK2', '20', '01/03/2023', '01/04/2023', '64'],
    [''],
    ['BẢNG THAM CHIẾU NGÀY', '', 'BẢNG THAM CHIẾU GIỜ'],
    ['Thời gian', 'Giá trị', 'Thời gian', 'Giá trị'],
    ['Chủ nhật', '2', '0h', '1'],
    ['Thứ 2', '4', '1h', '2'],
    ['Thứ 3', '8', '2h', '4'],
    ['Thứ 4', '16', '3h', '8'],
    ['Thứ 5', '32', '4h', '16'],
    ['Thứ 6', '64', '5h', '32'],
    ['Thứ 7', '128', '6h', '64'],
    ['Ví dụ: CN, T2, T5 = 2 + 4 + 32', '38', '7h', '128'],
    ['', '', '8h', '256'],
    ['', '', '9h', '512'],
    ['', '', '10h', '1024'],
    ['', '', '11h', '2048'],
    ['', '', '12h', '4096'],
    ['', '', '13h', '8192'],
    ['', '', '14h', '16384'],
    ['', '', '15h', '32768'],
    ['', '', '16h', '65536'],
    ['', '', '17h', '131072'],
    ['', '', '18h', '262144'],
    ['', '', '19h', '524288'],
    ['', '', '20h', '1048576'],
    ['', '', '21h', '2097152'],
    ['', '', '22h', '4194304'],
    ['', '', '23h', '8388608'],
    ['', '', 'Ví dụ: 0h, 1h, 3h = 1 + 2 + 8', '11']
  ]

  const exampleWorksheet = XLSX.utils.aoa_to_sheet(exampleData)

  exampleWorksheet['!cols'] = [
    { width: 35 },
    { width: 12 },
    { width: 25 },
    { width: 12 }
  ]

  const infoStyle = {
    fill: { fgColor: { rgb: "4F81BD" } },
    font: { color: { rgb: "FFFFFF" }, bold: true },
    alignment: { horizontal: "center", vertical: "center" }
  }

  const exampleHeaderStyle = {
    fill: { fgColor: { rgb: "D9E2F3" } },
    font: { bold: true },
    alignment: { horizontal: "center", vertical: "center" }
  }

  const tableHeaderStyle = {
    fill: { fgColor: { rgb: "4F81BD" } },
    font: { color: { rgb: "FFFFFF" }, bold: true },
    alignment: { horizontal: "center", vertical: "center" }
  }

  const exampleDataStyle = {
    fill: { fgColor: { rgb: "E7F3FF" } },
    alignment: { horizontal: "center", vertical: "center" }
  }

  if (!exampleWorksheet['A1']) exampleWorksheet['A1'] = { t: 's', v: '' }
  exampleWorksheet['A1'].s = infoStyle

  const exampleHeaders = ['A3', 'B3', 'C3', 'D3', 'E3']
  exampleHeaders.forEach(cell => {
    if (!exampleWorksheet[cell]) exampleWorksheet[cell] = { t: 's', v: '' }
    exampleWorksheet[cell].s = exampleHeaderStyle
  })

  const exampleDataCells = ['A4', 'B4', 'C4', 'D4', 'E4', 'A5', 'B5', 'C5', 'D5', 'E5']
  exampleDataCells.forEach(cell => {
    if (!exampleWorksheet[cell]) exampleWorksheet[cell] = { t: 's', v: '' }
    exampleWorksheet[cell].s = exampleDataStyle
  })

  const tableHeaders = ['A7', 'C7', 'A8', 'B8', 'C8', 'D8']
  tableHeaders.forEach(cell => {
    if (!exampleWorksheet[cell]) exampleWorksheet[cell] = { t: 's', v: '' }
    exampleWorksheet[cell].s = tableHeaderStyle
  })

  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Template')
  XLSX.utils.book_append_sheet(workbook, exampleWorksheet, 'Example')

  workbook.Props = {
    Title: 'Template cấu hình số lượng món trong ngày',
    Subject: 'POS System - Quantity Day Configuration',
    Author: 'POS System',
    CreatedDate: new Date()
  }

  downloadExcel(workbook, 'template_cau_hinh_so_luong_mon')
}
