import { useQuery } from '@tanstack/react-query'

import { menuScheduleApi } from '@/lib/menu-schedule-api'

interface UseMenuScheduleDataOptions {
  params?: Record<string, string>
  enabled?: boolean
}

export const useMenuScheduleData = (options: UseMenuScheduleDataOptions = {}) => {
  const { params = {}, enabled = true } = options

  return useQuery({
    queryKey: ['menu-schedule', params],
    queryFn: () => menuScheduleApi.getMenuSchedules(params),
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000 // 10 minutes
  })
}
