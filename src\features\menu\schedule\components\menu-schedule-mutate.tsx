import { useState, useEffect } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { X } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { createMenuSchedulePayload } from '@/lib/menu-schedule-api'

import { useCitiesData } from '@/hooks/api/use-removed-items'
import { useStoresData } from '@/hooks/api/use-stores'

import { Button } from '@/components/ui/button'
import { Form } from '@/components/ui/form'
import { Input } from '@/components/ui/input'

import { useCreateMenuSchedule, useUpdateItemSchedule } from '@/features/menu/schedule/hooks'

import { MenuSchedule } from '../data/schema'
import type { MenuItem } from '../types/menu-schedule-api'
import { MenuItemScheduleDialog } from './menu-item-schedule-dialog'
import { MenuItemsTable } from './menu-items-table'
import { MenuScheduleFormFields } from './menu-schedule-form-fields'

const formSchema = z.object({
  start_date: z.date({ required_error: 'Ngày bắt đầu là bắt buộc' }),
  end_date: z.date().optional(),
  city_uid: z.string().min(1, 'Thành phố là bắt buộc'),
  store_uid: z.string().optional()
})

type FormData = z.infer<typeof formSchema>

interface MenuScheduleMutateProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: MenuSchedule & {
    menuItems?: MenuItem[]
    id?: string
    company_uid?: string
    brand_uid?: string
    store_uid?: string
    city_uid?: string
    time?: number
    end_time?: number | null
    status?: string
  }
}

export function MenuScheduleMutate({ open, onOpenChange, currentRow }: MenuScheduleMutateProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<MenuItem | null>(null)

  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const createMenuScheduleMutation = useCreateMenuSchedule()
  const updateItemScheduleMutation = useUpdateItemSchedule()

  const isUpdateMode = !!currentRow?.menuItems

  const { data: citiesData = [] } = useCitiesData()
  const { data: storesData = [] } = useStoresData({
    enabled: open
  })

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      start_date: new Date(new Date().setDate(new Date().getDate() + 1)),
      end_date: undefined,
      city_uid: '',
      store_uid: ''
    }
  })

  useEffect(() => {
    if (open && currentRow) {
      setMenuItems(currentRow.menuItems || [])

      const formData = {
        start_date: currentRow.time ? new Date(currentRow.time * 1000) : new Date(),
        end_date: currentRow.end_time ? new Date(currentRow.end_time * 1000) : undefined,
        city_uid: currentRow.city_uid || '',
        store_uid: currentRow.store_uid || ''
      }

      form.reset(formData)
    } else if (open && !currentRow) {
      setMenuItems([])

      form.reset({
        start_date: new Date(new Date().setDate(new Date().getDate() + 1)),
        end_date: undefined,
        city_uid: '',
        store_uid: ''
      })
    }
  }, [currentRow, open, form])

  const selectedCityUid = form.watch('city_uid')
  const selectedStoreUid = form.watch('store_uid')

  const filteredStores = selectedCityUid
    ? storesData.filter(store => {
        const selectedCity = citiesData.find(city => city.id === selectedCityUid)
        return selectedCity && store.cityId === selectedCity.city_id
      })
    : []

  const handleAddMenuItem = (e?: React.MouseEvent) => {
    e?.preventDefault()
    e?.stopPropagation()
    setIsDialogOpen(true)
  }

  const handleDialogConfirm = (items: MenuItem[]) => {
    if (selectedItem) {
      const updatedItems = menuItems.map(item => (item.id === selectedItem.id ? items[0] : item))
      setMenuItems(updatedItems)
    } else {
      setMenuItems([...menuItems, ...items])
    }
    setIsDialogOpen(false)
    setSelectedItem(null)
  }

  const handleRowClick = (item: MenuItem) => {
    setSelectedItem(item)
    setIsDialogOpen(true)
  }

  const handleRemoveMenuItem = (id: string) => {
    setMenuItems(menuItems.filter(item => item.id !== id))
  }

  const handleSubmit = async (data: FormData) => {
    if (menuItems.length === 0) {
      toast.error('Vui lòng thêm ít nhất một món')
      return
    }

    if (isUpdateMode) {
      const getScheduleId = () => {
        const allItems = [...menuItems, ...(currentRow?.menuItems || [])]
        return allItems.find(item => item.scheduleId)?.scheduleId
      }

      const scheduleData = {
        company_uid: currentRow?.company_uid || company?.id || '',
        brand_uid: currentRow?.brand_uid || selectedBrand?.id || '',
        store_uid: currentRow?.store_uid || data.store_uid || '',
        city_uid: currentRow?.city_uid || data.city_uid,
        time: currentRow?.time || data.start_date.getTime(),
        end_time: currentRow?.end_time || data.end_date?.getTime() || null,
        status: currentRow?.status || 'PENDING',
        schedule_id: getScheduleId()
      }

      updateItemScheduleMutation.mutate(
        { menuItems, scheduleData },
        {
          onSuccess: () => {
            onOpenChange(false)
          }
        }
      )
    } else {
      const payload = createMenuSchedulePayload(
        {
          company_uid: company?.id || '',
          brand_uid: selectedBrand?.id || '',
          city_uid: data.city_uid,
          store_uid: data.store_uid || '',
          start_date: data.start_date,
          end_date: data.end_date
        },
        menuItems
      )

      createMenuScheduleMutation.mutate(payload, {
        onSuccess: () => {
          onOpenChange(false)
        }
      })
    }
  }

  if (!open) return null

  const handleClose = () => {
    onOpenChange(false)
    form.reset()
    setMenuItems([])
    setSearchQuery('')
    setSelectedItem(null)
  }

  const isFormValid = form.watch('start_date') && form.watch('city_uid')

  const getScheduleStatus = () => {
    if (!currentRow) return null

    // Giống logic filter: PENDING, PROCESS, DONE
    const status = currentRow.status || 'PENDING'

    switch (status) {
      case 'PENDING':
        return {
          text: 'Lịch sắp diễn ra',
          className: 'bg-blue-500 text-white'
        }
      case 'PROCESS':
        return {
          text: 'Lịch đang diễn ra',
          className: 'bg-orange-500 text-white'
        }
      case 'DONE':
        return {
          text: 'Lịch đã hoàn thành',
          className: 'bg-green-500 text-white'
        }
      default:
        return {
          text: 'Lịch sắp diễn ra',
          className: 'bg-blue-500 text-white'
        }
    }
  }

  const scheduleStatus = getScheduleStatus()

  return (
    <>
      {!isDialogOpen && (
        <div className='container mx-auto px-4 py-8'>
          {/* Header */}
          <div className='mb-8'>
            <div className='mb-4 flex items-center justify-between'>
              <Button variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
                <X className='h-4 w-4' />
              </Button>
              <Button
                type='button'
                disabled={createMenuScheduleMutation.isPending || updateItemScheduleMutation.isPending || !isFormValid}
                className='min-w-[100px]'
                onClick={() => form.handleSubmit(handleSubmit)()}
              >
                {createMenuScheduleMutation.isPending || updateItemScheduleMutation.isPending ? 'Đang lưu...' : 'Lưu'}
              </Button>
            </div>

            <div className='text-center'>
              <h1 className='mb-2 text-3xl font-bold'>
                {isUpdateMode ? 'Cập nhật lịch thay đổi' : 'Tạo lịch thay đổi'}
              </h1>
              {scheduleStatus && (
                <div className='mt-4 w-full'>
                  <div
                    className={`flex w-full items-center justify-center rounded-lg px-4 py-3 text-sm font-medium ${scheduleStatus.className}`}
                  >
                    {scheduleStatus.text}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Form Content */}
          <div className='mx-auto max-w-4xl'>
            <div className='space-y-6'>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
                  <div className='rounded-lg border bg-white p-6 shadow-sm'>
                    <div className='space-y-6'>
                      <div>
                        <h2 className='text-lg font-medium text-gray-900'>Thông tin cơ bản</h2>
                      </div>

                      <MenuScheduleFormFields
                        form={form}
                        citiesData={citiesData.map(city => ({ id: city.id, name: city.city_name }))}
                        storesData={filteredStores.map(store => ({
                          id: store.id,
                          name: store.name
                        }))}
                        isUpdateMode={isUpdateMode}
                      />

                      <div>
                        <p className='text-muted-foreground text-sm'>Thức đơn thay đổi theo cửa hàng</p>
                      </div>
                    </div>
                  </div>

                  <div className='rounded-lg border bg-white p-6 shadow-sm'>
                    <div className='space-y-6'>
                      <div>
                        <h2 className='text-lg font-medium text-gray-900'>Món lên lịch thay đổi</h2>
                      </div>

                      <div className='flex gap-2'>
                        <div className='flex-1'>
                          <Input
                            placeholder={
                              selectedCityUid ? 'Nhập tên món hoặc thao tác' : 'Vui lòng chọn thành phố trước'
                            }
                            value={searchQuery}
                            onChange={e => setSearchQuery(e.target.value)}
                            onKeyDown={e => e.key === 'Enter' && selectedCityUid && handleAddMenuItem()}
                            disabled={!selectedCityUid}
                          />
                        </div>
                        <Button type='button' onClick={handleAddMenuItem} size='sm' disabled={!selectedCityUid}>
                          Thêm món
                        </Button>
                      </div>

                      <MenuItemsTable
                        menuItems={menuItems}
                        onRowClick={handleRowClick}
                        onRemoveItem={handleRemoveMenuItem}
                      />
                    </div>
                  </div>
                </form>
              </Form>
            </div>
          </div>
        </div>
      )}

      {isDialogOpen && (
        <MenuItemScheduleDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          onConfirm={handleDialogConfirm}
          cityUid={selectedCityUid}
          storeUid={selectedStoreUid}
          selectedItem={selectedItem}
        />
      )}
    </>
  )
}
