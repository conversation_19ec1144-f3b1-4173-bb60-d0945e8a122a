import type { 
  PromotionSaleSummaryParams, 
  PromotionSaleSummaryResponse 
} from '@/types/api/revenue-promotion'

import { apiClient } from './pos-api'

export const getPromotionSaleSummary = async (
  params: PromotionSaleSummaryParams
): Promise<PromotionSaleSummaryResponse> => {
  const response = await apiClient.get<PromotionSaleSummaryResponse>(
    '/v1/reports/sale-summary/promotions',
    { params }
  )
  return response.data
}
