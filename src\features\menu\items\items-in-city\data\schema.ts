import { z } from 'zod'

export const itemsInCitySchema = z.object({
  id: z.string(),
  code: z.string(),
  name: z.string(),
  price: z.number(),
  vatPercent: z.number(),
  categoryGroup: z.string(),
  itemType: z.string(),
  itemClass: z.string().optional(),
  unit: z.string(),
  sideItems: z.string().optional(),
  city: z.string(),
  buffetConfig: z.string(),
  customization: z.string().optional(),
  isActive: z.boolean(),
  createdAt: z.date(),
  originalData: z.object({
    item_id: z.string(),
    item_code: z.string(),
    item_name: z.string(),
    price: z.number(),
    vat_percent: z.number(),
    category_group: z.string(),
    item_type: z.string(),
    unit: z.string(),
    side_items: z.string().optional(),
    city_name: z.string(),
    buffet_config: z.string(),
    customization_uid: z.string().optional(),
    active: z.number(),
    created_at: z.number(),
    store_uid: z.string(),
    store_name: z.string()
  })
})

export type ItemsInCity = z.infer<typeof itemsInCitySchema>
