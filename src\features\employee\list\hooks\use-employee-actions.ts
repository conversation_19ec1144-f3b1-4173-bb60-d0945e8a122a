import { useNavigate } from '@tanstack/react-router'

import type { User } from '@/types/user'

import { useActivateUser, useDeactivateUser } from '@/hooks/api'

export function useEmployeeActions() {
  const navigate = useNavigate()
  const activateUserMutation = useActivateUser()
  const deactivateUserMutation = useDeactivateUser()

  const handleInviteEmployee = () => {
    navigate({ to: '/employee/detail' })
  }

  const handleEmployeeClick = (userId: string) => {
    navigate({ to: '/employee/detail/$userId', params: { userId } })
  }

  const handleToggleEmployeeStatus = async (user: User) => {
    try {
      const isCurrentlyActive = user.active === 1

      if (isCurrentlyActive) {
        await deactivateUserMutation.mutateAsync(user.id)
      } else {
        await activateUserMutation.mutateAsync(user.id)
      }
    } catch (error) {
      // Error is already handled by the mutation hooks
      console.error('Toggle user status error:', error)
    }
  }

  return {
    handleInviteEmployee,
    handleEmployeeClick,
    handleToggleEmployeeStatus
  }
}
