import React, { useState } from 'react'

import { DateRangePicker } from '@/components/ui'

import { useDashboardContext } from '../context'
import { StoreSelectionDropdown } from './store-selection-dropdown'

export const ActionBar: React.FC = () => {
  const { dateRange, setDateRange, selectedStores, setSelectedStores } = useDashboardContext()

  const [selectedCities, setSelectedCities] = useState<string[]>([])

  const handleUpdateDateRange = () => {
    // Date range updated
  }

  return (
    <div className='mb-6 flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between'>
      <div className='flex flex-col items-center gap-4 md:flex-row md:gap-6'>
        <DateRangePicker
          initialDateFrom={dateRange.from}
          initialDateTo={dateRange.to}
          onUpdate={({ range }) => {
            if (range?.from && range?.to) {
              setDateRange({ from: range.from, to: range.to })
              handleUpdateDateRange()
            }
          }}
          align='start'
          locale='vi-VN'
        />

        <StoreSelectionDropdown
          selectedCities={selectedCities}
          selectedStores={selectedStores}
          onCitiesChange={setSelectedCities}
          onStoresChange={setSelectedStores}
          className='w-[200px]'
        />
      </div>
    </div>
  )
}
