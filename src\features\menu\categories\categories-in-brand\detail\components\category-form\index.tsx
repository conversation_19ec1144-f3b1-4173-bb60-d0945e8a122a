import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import { X } from 'lucide-react'
import { toast } from 'sonner'

import type { CreatedItemCategory } from '@/lib/item-categories-api'
import { getSelectedItemsDisplay, getSelectedPrintersDisplay } from '@/lib/utils'

import { getErrorMessage } from '@/utils/error-utils'

import {
  useCreateItemCategory,
  useItemCategoryData,
  useItemsData,
  useCitiesData,
  usePrinterPositionsData,
  useUpdateItemTypeStatus,
  useUpdateItemType
} from '@/hooks/api'

import { PosModal } from '@/components/pos'
import { Button, Checkbox, Input, Label } from '@/components/ui'

import { ItemAssignmentSection } from './item-assignment-section'
import { ItemSelectionModal } from './item-selection-modal'
import { PrinterPositionSection } from './printer-position-section'
import { PrinterSelectionModal } from './printer-selection-modal'

function CategoryForm({ id }: { id?: string }) {
  const navigate = useNavigate()
  const isEditMode = !!id

  const { mutate: createCategory, isPending: isCreating } = useCreateItemCategory()
  const { mutate: updateItemTypeStatus, isPending: isUpdatingStatus } = useUpdateItemTypeStatus()
  const { mutate: updateItemType, isPending: isUpdatingItemType } = useUpdateItemType()
  const { data: categoryData, isLoading: isLoadingCategory } = useItemCategoryData(
    id || '',
    isEditMode
  )
  const { data: cities = [] } = useCitiesData()

  const { data: items = [] } = useItemsData({
    params: {
      skip_limit: true,
      list_city_uid: cities.map(city => city.id).join(',')
    },
    enabled: cities.length > 0
  })

  const { data: printerPositionsData, isLoading: isPrinterPositionsLoading, error: printerPositionsError } = usePrinterPositionsData({
    enabled: true // Enable the query
  })
  const printerPositions = Array.isArray(printerPositionsData) ? printerPositionsData : []

  // Debug logging
  console.log('Category form printer positions debug:', {
    printerPositionsData,
    printerPositions,
    isPrinterPositionsLoading,
    printerPositionsError,
    length: printerPositions.length
  })

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    followCategoryCreation: false,
    displayOrder: undefined as number | undefined,
    selectedItems: [] as string[],
    selectedPrinters: [] as string[]
  })

  const [showItemModal, setShowItemModal] = useState(false)
  const [showPrinterModal, setShowPrinterModal] = useState(false)
  const [showPrinterPositionModal, setShowPrinterPositionModal] = useState(false)
  const [showPrinterSection, setShowPrinterSection] = useState(false)
  const [newlyCreatedCategory, setNewlyCreatedCategory] = useState<CreatedItemCategory | null>(null)

  useEffect(() => {
    if (isEditMode && categoryData && printerPositions.length > 0) {
      const selectedPrinterIds = printerPositions
        .filter((printer: any) => {
          const categoryIds = printer.list_item_type_id
            .split(',')
            .map((id: string) => id.trim())
            .filter(Boolean)
          return categoryIds.includes(categoryData.item_type_id)
        })
        .map((printer: any) => printer.printer_position_id)

      setFormData({
        name: categoryData.item_type_name,
        code: categoryData.item_type_id,
        followCategoryCreation: false,
        displayOrder: categoryData.sort || undefined,
        selectedItems: categoryData.list_item || [],
        selectedPrinters: selectedPrinterIds
      })
      setShowPrinterSection(true)
    }
  }, [isEditMode, categoryData, printerPositions])

  useEffect(() => {
    if (newlyCreatedCategory && !isEditMode) {
      setFormData(prev => ({
        ...prev,
        code: newlyCreatedCategory.item_type_id,
        followCategoryCreation: true
      }))
    }
  }, [newlyCreatedCategory, isEditMode])

  const isFieldsDisabledAfterCreation = !isEditMode && !!newlyCreatedCategory

  const handleBack = () => {
    navigate({ to: '/menu/categories/categories-in-brand' })
  }

  const handleSave = async () => {
    if (!isFormValid) return

    // Navigate back to list if category was already created
    if (newlyCreatedCategory && !isEditMode) {
      navigate({ to: '/menu/categories/categories-in-brand' })
      return
    }

    if (isEditMode && categoryData) {
      const updatedItemType = {
        ...categoryData,
        item_type_name: formData.name,
        sort: formData.displayOrder || categoryData.sort,
        extra_data: categoryData.extra_data || {},
        list_item: formData.selectedItems || []
      }

      updateItemType(updatedItemType, {
        onSuccess: () => {
          if (formData.selectedPrinters.length === 0) {
            setShowPrinterPositionModal(true)
          } else {
            toast.success('Đã cập nhật nhóm món thành công')
            navigate({ to: '/menu/categories/categories-in-brand' })
          }
        }
      })
    } else {
      createCategory(
        {
          name: formData.name,
          code: formData.followCategoryCreation && formData.code.trim() ? formData.code : undefined,
          sort: formData.displayOrder,
          selectedItems: formData.selectedItems
        },
        {
          onSuccess: createdCategory => {
            setNewlyCreatedCategory(createdCategory)
            setShowPrinterPositionModal(true)
          }
        }
      )
    }
  }

  const handleToggleStatus = async () => {
    if (!isEditMode || !categoryData) return

    const updatedCategory = {
      ...categoryData,
      active: categoryData.active === 1 ? 0 : 1,
      extra_data: categoryData.extra_data || {}
    }

    updateItemTypeStatus(updatedCategory, {
      onSuccess: () => {
        const statusText = updatedCategory.active === 1 ? 'kích hoạt' : 'vô hiệu hóa'
        toast.success(`Đã ${statusText} nhóm món "${categoryData.item_type_name}"`)
      },
      onError: error => {
        const errorMessage = getErrorMessage(error)
        toast.error(errorMessage)
      }
    })
  }

  const isFormValid = formData.name.trim() !== ''
  const isLoading = isCreating || isLoadingCategory || isUpdatingStatus || isUpdatingItemType

  const handleItemSelection = () => {
    setShowItemModal(true)
  }

  const handleItemsSelected = (selectedItemIds: string[]) => {
    setFormData({ ...formData, selectedItems: selectedItemIds })
    setShowItemModal(false)
  }

  const handlePrinterSelection = () => {
    setShowPrinterModal(true)
  }

  const handlePrintersSelected = (selectedPrinterIds: string[]) => {
    setFormData({ ...formData, selectedPrinters: selectedPrinterIds })
    setShowPrinterModal(false)
  }

  const handlePrinterPositionModalCancel = () => {
    setShowPrinterPositionModal(false)
    navigate({ to: '/menu/categories/categories-in-brand' })
  }

  const handlePrinterPositionModalConfirm = () => {
    setShowPrinterPositionModal(false)
    setShowPrinterSection(true)
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <div className='flex items-center gap-2'>
            {isEditMode && categoryData && (
              <Button
                type='button'
                variant={categoryData.active === 1 ? 'destructive' : 'default'}
                disabled={isLoading}
                className='min-w-[100px]'
                onClick={handleToggleStatus}
              >
                {categoryData.active === 1 ? 'Deactivate' : 'Activate'}
              </Button>
            )}
            {(!isEditMode || categoryData?.active === 1) && (
              <Button
                type='button'
                disabled={isLoading || !isFormValid}
                className='min-w-[100px]'
                onClick={handleSave}
              >
                {isLoading ? (isEditMode ? 'Đang cập nhật...' : 'Đang tạo...') : 'Lưu'}
              </Button>
            )}
          </div>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>
            {isEditMode ? 'Chỉnh sửa nhóm món' : 'Tạo nhóm món'}
          </h1>
        </div>
      </div>

      <div className='mx-auto max-w-4xl'>
        <div className='p-6'>
          <div className='space-y-6'>
            <div className='flex items-center gap-4'>
              <Label htmlFor='category-name' className='min-w-[200px] text-sm font-medium'>
                Tên nhóm món <span className='text-red-500'>*</span>
              </Label>
              <Input
                id='category-name'
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                placeholder='Nhập tên nhóm món'
                className='flex-1'
                disabled={isFieldsDisabledAfterCreation}
              />
            </div>

            <div className='flex items-center gap-4'>
              <Label htmlFor='category-code' className='min-w-[200px] text-sm font-medium'>
                Mã nhóm món
              </Label>
              <Input
                id='category-code'
                value={formData.code}
                onChange={e => setFormData({ ...formData, code: e.target.value })}
                placeholder='Nếu để trống, hệ thống sẽ tự động tạo một mã nhóm món'
                disabled={
                  !formData.followCategoryCreation || isEditMode || isFieldsDisabledAfterCreation
                }
                className='flex-1'
              />
              {!isEditMode && !isFieldsDisabledAfterCreation && (
                <Checkbox
                  id='follow-category-creation'
                  checked={formData.followCategoryCreation}
                  onCheckedChange={checked =>
                    setFormData({
                      ...formData,
                      followCategoryCreation: checked as boolean,
                      code: !checked ? '' : formData.code
                    })
                  }
                />
              )}
            </div>

            <ItemAssignmentSection
              selectedItems={formData.selectedItems}
              onItemSelection={handleItemSelection}
              getSelectedItemsDisplay={() => getSelectedItemsDisplay(formData.selectedItems)}
              isFieldsDisabledAfterCreation={isFieldsDisabledAfterCreation}
            />

            <PrinterPositionSection
              showPrinterSection={showPrinterSection}
              selectedPrinters={formData.selectedPrinters}
              onPrinterSelection={handlePrinterSelection}
              getSelectedPrintersDisplay={() =>
                getSelectedPrintersDisplay(formData.selectedPrinters)
              }
              newlyCreatedCategory={newlyCreatedCategory}
            />

            <div className='flex items-center gap-4'>
              <Label htmlFor='display-order' className='min-w-[200px] text-sm font-medium'>
                Thứ tự hiển thị
              </Label>
              <Input
                id='display-order'
                type='number'
                value={formData.displayOrder || ''}
                onChange={e =>
                  setFormData({
                    ...formData,
                    displayOrder: e.target.value ? Number(e.target.value) : undefined
                  })
                }
                placeholder='Nhập số thứ tự hiển thị'
                className='flex-1'
              />
            </div>
          </div>
        </div>
      </div>

      <ItemSelectionModal
        open={showItemModal}
        onOpenChange={setShowItemModal}
        items={items}
        selectedItems={formData.selectedItems}
        onItemsSelected={handleItemsSelected}
      />

      <PrinterSelectionModal
        open={showPrinterModal}
        onOpenChange={setShowPrinterModal}
        printerPositions={printerPositions}
        selectedPrinters={formData.selectedPrinters}
        onPrintersSelected={handlePrintersSelected}
        newlyCreatedCategory={newlyCreatedCategory}
        currentCategory={isEditMode ? categoryData : null}
      />

      <PosModal
        open={showPrinterPositionModal}
        onOpenChange={setShowPrinterPositionModal}
        title='Chưa có vị trí máy in nào áp dụng nhóm món, bạn có muốn chọn vị trí máy in?'
        cancelText='Rời khỏi'
        confirmText='Chọn thêm'
        onCancel={handlePrinterPositionModalCancel}
        onConfirm={handlePrinterPositionModalConfirm}
      >
        <div className='py-4'>
          <p className='text-sm text-gray-600'>
            Bạn có thể chọn các vị trí máy in để áp dụng cho nhóm món này.
          </p>
        </div>
      </PosModal>
    </div>
  )
}

export default CategoryForm
