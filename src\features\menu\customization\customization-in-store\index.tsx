import { useNavigate } from '@tanstack/react-router'

import { Customization } from '@/types/customizations'

import { useCustomizationsData } from '@/hooks/api'

import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Button,
  Input,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui'

import {
  customizationColumns,
  CustomizationDataTable,
  DeleteCustomizationModal,
  CopyCustomizationModal,
  ExportCustomizationModal,
  ImportCustomizationModal
} from './components'
import {
  useCustomizationSearch,
  useCustomizationModals,
  useCustomizationCopy,
  useCustomizationDelete,
  useCustomizationExport,
  useCustomizationImport
} from './hooks'

export default function CustomizationInStorePage() {
  const navigate = useNavigate()

  const search = useCustomizationSearch()
  const modals = useCustomizationModals()
  const copy = useCustomizationCopy()
  const deleteHook = useCustomizationDelete()
  const exportHook = useCustomizationExport()
  const importHook = useCustomizationImport(search.selectedStoreId)

  const {
    data: customizations,
    isLoading,
    error
  } = useCustomizationsData({
    searchTerm: search.searchTerm || undefined,
    store_uid: search.selectedStoreId
  })

  const customizationsWithStoreName =
    customizations?.map(customization => {
      const selectedStore = search.stores.find(s => s.id === search.selectedStoreId)
      const storeName = selectedStore?.store_name || 'N/A'

      return {
        ...customization,
        storeName
      }
    }) || []

  const handleCopyCustomization = (customization: Customization) => {
    copy.initializeCopyName(customization)
    modals.openCopyModal(customization)
  }

  const handleDeleteCustomization = (customization: Customization) => {
    modals.openDeleteModal(customization)
  }

  const handleConfirmCopyCustomization = async () => {
    if (!modals.selectedCustomization) return

    const targetStoreUid = search.selectedStoreId
    const success = await copy.handleCopyCustomization(modals.selectedCustomization, targetStoreUid)
    if (success) modals.closeCopyModal()
  }

  const handleConfirmDeleteCustomization = async () => {
    if (!modals.selectedCustomization) return

    const success = await deleteHook.handleDeleteCustomization(modals.selectedCustomization)
    if (success) modals.closeDeleteModal()
  }

  const handleCancelCopyModal = () => {
    copy.resetCopyName()
    modals.closeCopyModal()
  }

  const handleExportCustomizations = () => {
    modals.openExportModal()
  }

  const handleImportCustomizations = () => {
    importHook.resetImportState()
    modals.openImportModal()
  }

  const handleCloseExportModal = () => {
    exportHook.resetExportState()
    modals.closeExportModal()
  }

  const handleSaveImportedData = async () => {
    const success = await exportHook.handleSaveImportedData()
    if (success) modals.closeExportModal()
  }

  const handleCloseImportModal = () => {
    importHook.resetImportState()
    modals.closeImportModal()
  }

  const handleSaveImportedCustomizations = async () => {
    const success = await importHook.handleSaveImportedCustomizations()
    if (success) modals.closeImportModal()
  }

  const handleCreateCustomization = () => {
    navigate({ to: '/menu/customization/customization-in-store/detail' })
  }

  const handleEditCustomization = (customization: Customization) => {
    navigate({
      to: '/menu/customization/customization-in-store/detail/$customizationId',
      params: { customizationId: customization.id }
    })
  }

  const columns = customizationColumns({
    onCopyCustomization: handleCopyCustomization,
    onDeleteCustomization: handleDeleteCustomization
  })

  return (
    <>
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='container mx-auto px-4 py-8'>
          <div className='mb-6 flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <h2 className='text-xl font-semibold'>Customization</h2>
              <Input
                placeholder='Tìm kiếm customization'
                className='w-64'
                value={search.searchQuery}
                onChange={e => search.setSearchQuery(e.target.value)}
                onKeyDown={search.handleSearchKeyDown}
              />
              <Select value={search.selectedStoreId} onValueChange={search.setSelectedStoreId}>
                <SelectTrigger className='w-48'>
                  <SelectValue placeholder='Chọn cửa hàng' />
                </SelectTrigger>
                <SelectContent>
                  {search.stores.map(store => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.store_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className='flex items-center gap-2'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant='outline'>Tiện ích</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={handleExportCustomizations}>Xuất, sửa customization</DropdownMenuItem>
                  <DropdownMenuItem onClick={handleImportCustomizations}>Thêm customization từ file</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button onClick={handleCreateCustomization}>Tạo customization</Button>
            </div>
          </div>

          {error && (
            <div className='mb-4 rounded-md bg-red-50 p-4 text-red-700'>
              Có lỗi xảy ra khi tải dữ liệu: {error.message}
            </div>
          )}

          <CustomizationDataTable
            columns={columns}
            data={customizationsWithStoreName}
            isLoading={isLoading}
            onRowClick={handleEditCustomization}
          />

          <CopyCustomizationModal
            open={modals.isCopyModalOpen}
            onOpenChange={modals.closeCopyModal}
            selectedCustomization={modals.selectedCustomization}
            copyName={copy.copyName}
            onCopyNameChange={copy.setCopyName}
            onCancel={handleCancelCopyModal}
            onConfirm={handleConfirmCopyCustomization}
            isLoading={copy.isLoading}
          />

          <DeleteCustomizationModal
            open={modals.isDeleteModalOpen}
            onOpenChange={modals.closeDeleteModal}
            selectedCustomization={modals.selectedCustomization}
            onCancel={modals.closeDeleteModal}
            onConfirm={handleConfirmDeleteCustomization}
            isLoading={deleteHook.isLoading}
          />

          <ExportCustomizationModal
            open={modals.isExportModalOpen}
            onOpenChange={modals.closeExportModal}
            showParsedData={exportHook.showParsedData}
            exportStoreId={exportHook.exportStoreId}
            onExportStoreIdChange={exportHook.setExportStoreId}
            stores={exportHook.stores}
            selectedFile={exportHook.selectedFile}
            parsedData={exportHook.parsedData}
            isExporting={exportHook.isExporting}
            isSaving={exportHook.isSaving}
            onCancel={handleCloseExportModal}
            onConfirm={exportHook.showParsedData ? handleSaveImportedData : handleCloseExportModal}
            onDownloadExportFile={exportHook.handleDownloadExportFile}
            onUploadFile={exportHook.handleUploadFile}
          />

          <ImportCustomizationModal
            open={modals.isImportModalOpen}
            onOpenChange={modals.closeImportModal}
            showImportParsedData={importHook.showImportParsedData}
            importSelectedFile={importHook.importSelectedFile}
            importParsedData={importHook.importParsedData}
            isLoading={importHook.isLoading}
            storeName={search.stores.find(s => s.id === search.selectedStoreId)?.store_name}
            onCancel={handleCloseImportModal}
            onConfirm={handleSaveImportedCustomizations}
            onDownloadTemplate={importHook.handleDownloadTemplate}
            onImportFileUpload={importHook.handleImportFileUpload}
          />

          <input
            ref={importHook.importFileInputRef}
            type='file'
            accept='.xlsx,.xls'
            onChange={importHook.handleImportFileChange}
            className='hidden'
          />

          <input
            ref={exportHook.fileInputRef}
            type='file'
            accept='.xlsx,.xls'
            onChange={exportHook.handleFileChange}
            className='hidden'
          />
        </div>
      </Main>
    </>
  )
}
