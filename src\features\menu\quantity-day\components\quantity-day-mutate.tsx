import React from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { useCreateQuantityDay, useUpdateQuantityDay, useStoresData, useItemsData } from '@/hooks/api'

import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { DatePicker } from '@/components/ui/date/date-picker'
import { Dialog, DialogClose, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { MultiSelect } from '@/components/multi-select'

import { QuantityDay } from '../data'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: QuantityDay
}

const formSchema = z.object({
  store_uid: z.string().min(1, 'Vui lòng chọn cửa hàng.'),
  item_list: z.array(z.string()).min(1, 'Vui lòng chọn ít nhất một món.'),
  from_date: z.date({ required_error: 'Vui lòng chọn ngày bắt đầu.' }),
  to_date: z.date({ required_error: 'Vui lòng chọn ngày kết thúc.' }),
  quantity_per_day: z.number().min(1, 'Số lượng phải lớn hơn 0.'),
  time_sale_date_week: z.number(),
  require_update_pos: z.boolean()
})

type QuantityDayForm = z.infer<typeof formSchema>

export function QuantityDayMutate({ open, onOpenChange, currentRow }: Props) {
  const { data: stores = [], isLoading: isLoadingStores } = useStoresData()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { createQuantityDay } = useCreateQuantityDay()
  const { updateQuantityDay } = useUpdateQuantityDay()
  const isUpdate = !!currentRow

  const form = useForm<QuantityDayForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      store_uid: currentRow?.storeUid || '',
      from_date: currentRow?.fromDate || new Date(),
      to_date: currentRow?.toDate || new Date(),
      quantity_per_day: currentRow?.quantity || 1,
      time_sale_date_week: 0,
      item_list: currentRow?.originalData?.item_list || [],
      require_update_pos: false
    }
  })

  const selectedStoreUid = form.watch('store_uid')

  const { data: items = [], isLoading: isLoadingItems } = useItemsData({
    params: {
      list_store_uid: selectedStoreUid || undefined
    },
    enabled: !!selectedStoreUid
  })

  React.useEffect(() => {
    if (stores.length > 0 && !isUpdate && !selectedStoreUid) {
      form.setValue('store_uid', stores[0].id)
    }
  }, [stores, isUpdate, selectedStoreUid, form])

  React.useEffect(() => {
    if (selectedStoreUid && !isUpdate) {
      form.setValue('item_list', [])
    }
  }, [selectedStoreUid, form, isUpdate])

  const onSubmit = async (data: QuantityDayForm) => {
    if (!company?.id || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    const payload = {
      require_update: Date.now().toString(),
      store_uid: data.store_uid,
      time_sale_date_week: data.time_sale_date_week,
      quantity_per_day: data.quantity_per_day.toString(),
      item_list: data.item_list,
      from_date: data.from_date.getTime(),
      to_date: data.to_date.getTime(),
      company_uid: company.id,
      brand_uid: selectedBrand.id
    }

    if (isUpdate && currentRow) {
      const updatePayload = {
        require_update: Date.now().toString(),
        store_uid: data.store_uid,
        time_sale_date_week: data.time_sale_date_week,
        quantity_per_day: data.quantity_per_day,
        item_list: data.item_list,
        from_date: data.from_date.getTime(),
        to_date: data.to_date.getTime(),
        brand_uid: selectedBrand.id,
        company_uid: company.id,
        deleted: false,
        created_by: currentRow.originalData?.created_by || 'system',
        created_at: currentRow.originalData?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        __v: currentRow.originalData?.__v || 0,
        id: currentRow.id
      }
      updateQuantityDay(updatePayload)
    } else {
      createQuantityDay(payload)
    }

    onOpenChange(false)
    form.reset()
  }

  return (
    <Dialog
      open={open}
      onOpenChange={v => {
        onOpenChange(v)
        form.reset()
      }}
    >
      <DialogContent className='top-[50%] w-full max-w-4xl translate-y-[-50%]'>
        <DialogHeader>
          <DialogTitle>{isUpdate ? 'Chỉnh sửa cấu hình' : 'Tạo số món theo khoảng thời gian'}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            {/* Store Selection */}
            <FormField
              control={form.control}
              name='store_uid'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cửa hàng *</FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange} disabled={isLoadingStores}>
                      <SelectTrigger>
                        <SelectValue placeholder={isLoadingStores ? 'Đang tải...' : 'Chọn cửa hàng'} />
                      </SelectTrigger>
                      <SelectContent>
                        {stores.map(store => (
                          <SelectItem key={store.id} value={store.id}>
                            {store.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Items Selection */}
            <FormField
              control={form.control}
              name='item_list'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Áp dụng cho món</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={items.map(item => ({
                        label: item.item_name,
                        value: item.item_id
                      }))}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      placeholder={isLoadingItems ? 'Đang tải...' : '0 món được chọn'}
                      variant='default'
                      animation={0.2}
                      maxCount={3}
                      disabled={isLoadingItems}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Quantity */}
            <FormField
              control={form.control}
              name='quantity_per_day'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Số lượng</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      min='1'
                      placeholder='Nhập số lượng...'
                      {...field}
                      onChange={e => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* POS Update Checkbox */}
            <FormField
              control={form.control}
              name='require_update_pos'
              render={({ field }) => (
                <FormItem className='flex flex-row items-start space-y-0 space-x-3'>
                  <FormControl>
                    <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                  <div className='space-y-1 leading-none'>
                    <FormLabel>
                      Yêu cầu máy POS thực hiện cập nhật lại số lượng trong khai báo ngay sau cập nhập
                    </FormLabel>
                  </div>
                </FormItem>
              )}
            />

            {/* Date Range */}
            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='from_date'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày bắt đầu</FormLabel>
                    <FormControl>
                      <DatePicker date={field.value} onDateChange={field.onChange} placeholder='19/07/2025' />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='to_date'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ngày kết thúc</FormLabel>
                    <FormControl>
                      <DatePicker date={field.value} onDateChange={field.onChange} placeholder='19/07/2025' />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <DialogClose asChild>
                <Button variant='outline' type='button'>
                  Hủy
                </Button>
              </DialogClose>
              <Button type='submit'>Lưu</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
