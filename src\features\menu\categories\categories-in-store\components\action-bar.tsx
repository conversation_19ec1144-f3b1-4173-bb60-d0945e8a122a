import { IconPlus, IconChevronDown, IconDownload, IconFileImport } from '@tabler/icons-react'

import { toast } from 'sonner'

import { useExportItemCategories } from '@/hooks/api'

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

import { Button, Input, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui'

import { useCategoriesInStoreImport } from '../hooks/use-categories-import'
import { ImportCategoriesModal } from './modals/import-categories-modal'

function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message
  }
  if (typeof error === 'string') {
    return error
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }
  return 'Đã xảy ra lỗi không xác định'
}

interface ActionBarProps {
  searchQuery: string
  onSearchQueryChange: (value: string) => void
  onSearchSubmit: () => void
  onCreateCategory: () => void
  isCreating: boolean
  selectedStoreUid: string
  onStoreChange: (value: string) => void
  storeOptions: Array<{ value: string; label: string }>
  viewType: 'store-brand' | 'store'
  onViewTypeChange: (value: 'store-brand' | 'store') => void
}

export function ActionBar({
  searchQuery,
  onSearchQueryChange,
  onSearchSubmit,
  onCreateCategory,
  isCreating,
  selectedStoreUid,
  onStoreChange,
  storeOptions,
  viewType,
  onViewTypeChange
}: ActionBarProps) {
  const exportCategoriesMutation = useExportItemCategories()
  const importHook = useCategoriesInStoreImport(selectedStoreUid)

  const handleExportCategories = async () => {
    try {
      // Determine export parameters based on view type and selected store
      const exportParams =
        viewType === 'store' && selectedStoreUid
          ? {
              store_uid: selectedStoreUid,
              apply_with_store: true
            }
          : undefined

      const blob = await exportCategoriesMutation.mutateAsync(exportParams)
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `nhom-mon-${new Date().toISOString().split('T')[0]}.xlsx`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success('Xuất nhóm món tại cửa hàng thành công!')
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleImportCategories = () => {
    importHook.handleOpenImportModal()
  }

  const handleImportModalCancel = () => {
    importHook.resetImportState()
  }

  const handleImportModalConfirm = async () => {
    const success = await importHook.handleSaveImportedCategories()
    if (success) {
      // Modal will be closed by resetImportState in the hook
    }
  }

  return (
    <div className='mb-6 flex items-center gap-4'>
      <h2 className='text-2xl font-semibold'>Nhóm món tại cửa hàng</h2>

      <Select value={selectedStoreUid} onValueChange={onStoreChange}>
        <SelectTrigger className='w-[200px]'>
          <SelectValue placeholder='Chọn cửa hàng' />
        </SelectTrigger>
        <SelectContent>
          {storeOptions.map(option => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Input
        placeholder='Tìm kiếm nhóm món...'
        className='w-64'
        value={searchQuery}
        onChange={e => onSearchQueryChange(e.target.value)}
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault()
            onSearchSubmit()
          }
        }}
      />

      <Select value={viewType} onValueChange={onViewTypeChange}>
        <SelectTrigger className='w-[180px]'>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value='store-brand'>Cửa hàng và thương hiệu</SelectItem>
          <SelectItem value='store'>Cửa hàng</SelectItem>
        </SelectContent>
      </Select>

      <div className='ml-auto flex items-center gap-2'>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size='sm'>
              Tiện ích
              <IconChevronDown className='ml-2 h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={handleImportCategories} className='flex items-center gap-2'>
              <IconFileImport className='h-4 w-4' />
              Thêm nhóm từ file
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleExportCategories}
              className='flex items-center gap-2'
              disabled={exportCategoriesMutation.isPending}
            >
              <IconDownload className='h-4 w-4' />
              {exportCategoriesMutation.isPending ? 'Đang xuất...' : 'Xuất nhóm món'}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <Button onClick={onCreateCategory} className='flex items-center gap-2' disabled={isCreating}>
          <IconPlus className='h-4 w-4' />
          {isCreating ? 'Đang tạo...' : 'Tạo nhóm'}
        </Button>
      </div>

      <ImportCategoriesModal
        open={importHook.importModalOpen}
        onOpenChange={importHook.setImportModalOpen}
        showImportParsedData={importHook.showImportParsedData}
        importSelectedFile={importHook.importSelectedFile}
        importParsedData={importHook.importParsedData}
        isLoading={importHook.isLoading}
        onCancel={handleImportModalCancel}
        onConfirm={handleImportModalConfirm}
        onDownloadTemplate={importHook.handleDownloadTemplate}
        onImportFileUpload={importHook.handleImportFileUpload}
      />

      {/* Hidden file input for import */}
      <input
        ref={importHook.importFileInputRef}
        type='file'
        accept='.xlsx,.xls'
        onChange={importHook.handleImportFileChange}
        className='hidden'
      />
    </div>
  )
}
