import { useState, useEffect } from 'react'

import { IconX } from '@tabler/icons-react'

import { usePosCompanyData, useBrandsData } from '@/hooks'
import type { ExtraPointItem } from '@/types/api/crm'

import { useMembershipType, useCreateExtraPoint, useUpdateExtraPoint } from '@/hooks/crm'

import {
  Button,
  Checkbox,
  Input,
  Label,
  RadioGroup,
  RadioGroupItem,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch
} from '@/components/ui'

import { MarketingTimeframeSection } from './marketing-timeframe-section'

interface ExtraPointModalFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  extraPoint?: ExtraPointItem
}

export function ExtraPointModalForm({ open, onOpenChange, extraPoint }: ExtraPointModalFormProps) {
  const editMode = !!extraPoint
  const [isLoading, setIsLoading] = useState(false)
  const [selectedMembershipType, setSelectedMembershipType] = useState<string>('')
  const [startDate, setStartDate] = useState<string>(() => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  })
  const [endDate, setEndDate] = useState<string>('')
  const [timeType, setTimeType] = useState<string>('all')
  const [isActive, setIsActive] = useState<boolean>(true)
  const [extraRate, setExtraRate] = useState<string>('')
  const [beforeBirthdayDays, setBeforeBirthdayDays] = useState<string>('')
  const [afterBirthdayDays, setAfterBirthdayDays] = useState<string>('')
  const [onlyBirthdayDay, setOnlyBirthdayDay] = useState<boolean>(false)
  const [marketingDays, setMarketingDays] = useState<string[]>([])
  const [marketingHours, setMarketingHours] = useState<string[]>([])
  const [timeFrameValues, setTimeFrameValues] = useState<{ time_date_week: number; time_hour_day: number }>({
    time_date_week: 0,
    time_hour_day: 0
  })

  const membershipTypeQuery = useMembershipType()
  const membershipTypes = membershipTypeQuery.data?.list_membership_type || []
  const createExtraPointMutation = useCreateExtraPoint()
  const updateExtraPointMutation = useUpdateExtraPoint()
  const posCompanyData = usePosCompanyData()
  const brandsData = useBrandsData()

  const selectedMembershipTypeData = membershipTypes.find(type => type.type_id === selectedMembershipType)
  const pointRate = selectedMembershipTypeData?.point_rate || 0

  const calculateResult = () => {
    const extraRateNum = parseFloat(extraRate) || 0
    return (pointRate * extraRateNum * 100).toFixed(3)
  }

  // Helper function to decode time_date_week back to day values
  const decodeBinaryToDays = (time_date_week: number): string[] => {
    const dayMapping = [
      { value: 2, dayValue: '6' }, // CN = 2^1 = 2 → value '6'
      { value: 4, dayValue: '0' }, // T2 = 2^2 = 4 → value '0'
      { value: 8, dayValue: '1' }, // T3 = 2^3 = 8 → value '1'
      { value: 16, dayValue: '2' }, // T4 = 2^4 = 16 → value '2'
      { value: 32, dayValue: '3' }, // T5 = 2^5 = 32 → value '3'
      { value: 64, dayValue: '4' }, // T6 = 2^6 = 64 → value '4'
      { value: 128, dayValue: '5' } // T7 = 2^7 = 128 → value '5'
    ]

    return dayMapping.filter(day => (time_date_week & day.value) === day.value).map(day => day.dayValue)
  }

  // Helper function to decode time_hour_day back to hour values
  const decodeBinaryToHours = (time_hour_day: number): string[] => {
    const hours = []
    for (let i = 0; i < 24; i++) {
      if ((time_hour_day & Math.pow(2, i)) === Math.pow(2, i)) {
        hours.push(i.toString())
      }
    }
    return hours
  }

  useEffect(() => {
    if (editMode && extraPoint) {
      setSelectedMembershipType(extraPoint.type_id)
      setStartDate(extraPoint.start_date.split(' ')[0])
      setEndDate(extraPoint.end_date.split(' ')[0])
      setExtraRate(extraPoint.extra_rate.toString())
      setIsActive(extraPoint.active === 1)

      if (extraPoint.extra_rate_type === 'SPECIAL_TIME') {
        // Check if it's "all time" values
        if (extraPoint.time_hour_day === 16777215 && extraPoint.time_date_week === 254) {
          setTimeType('all')
        } else {
          setTimeType('timeframe')

          // Decode binary values back to selected days and hours
          const decodedDays = decodeBinaryToDays(extraPoint.time_date_week)
          const decodedHours = decodeBinaryToHours(extraPoint.time_hour_day)

          setMarketingDays(decodedDays)
          setMarketingHours(decodedHours)
          setTimeFrameValues({
            time_date_week: extraPoint.time_date_week,
            time_hour_day: extraPoint.time_hour_day
          })
        }
      } else if (extraPoint.extra_rate_type === 'BIRTHDAY') {
        setTimeType('birthday')

        // Map birthday fields
        const beforeDays = extraPoint.day_before_birthday || 0
        const afterDays = extraPoint.day_after_birthday || 0

        setBeforeBirthdayDays(beforeDays.toString())
        setAfterBirthdayDays(afterDays.toString())

        // If both are 0, it means "only birthday day"
        if (beforeDays === 0 && afterDays === 0) {
          setOnlyBirthdayDay(true)
        } else {
          setOnlyBirthdayDay(false)
        }
      } else {
        setTimeType('all')
      }
    } else {
      setSelectedMembershipType('')
      const today = new Date()
      setStartDate(today.toISOString().split('T')[0])
      setEndDate('')
      setTimeType('all')
      setIsActive(true)
      setExtraRate('')
    }
  }, [editMode, extraPoint, open])

  // Effect to handle timeType changes - when switching from "all" to "timeframe", select all days and hours
  useEffect(() => {
    if (timeType === 'timeframe' && marketingDays.length === 0 && marketingHours.length === 0) {
      // Select all days and hours when switching to timeframe mode
      const allDays = ['6', '0', '1', '2', '3', '4', '5'] // CN, T2, T3, T4, T5, T6, T7
      const allHours = Array.from({ length: 24 }, (_, i) => i.toString()) // 0-23

      setMarketingDays(allDays)
      setMarketingHours(allHours)
      setTimeFrameValues({
        time_date_week: 254, // All days: 2+4+8+16+32+64+128 = 254
        time_hour_day: 16777215 // All hours: 2^0 + 2^1 + ... + 2^23 = 16777215
      })
    }
  }, [timeType])

  const handleSubmit = async () => {
    if (
      !posCompanyData?.company_id ||
      !selectedMembershipType ||
      !startDate ||
      !endDate ||
      !extraRate ||
      !brandsData?.[0]?.brand_id
    ) {
      console.error('Missing required fields')
      return
    }

    setIsLoading(true)

    try {
      if (editMode && extraPoint) {
        // Update extra point
        const updateData = {
          id: extraPoint.id,
          company_id: posCompanyData.company_id,
          type_id: selectedMembershipType,
          type_name: selectedMembershipTypeData?.type_name || '',
          start_date: `${startDate} 00:00:00`,
          end_date: `${endDate} 23:59:59`,
          extra_rate: parseFloat(extraRate),
          active: isActive ? 1 : 0,
          extra_rate_type: timeType === 'all' ? 'SPECIAL_TIME' : timeType === 'timeframe' ? 'SPECIAL_TIME' : 'BIRTHDAY',
          created_at: extraPoint.created_at,
          ...(timeType === 'all' || timeType === 'timeframe'
            ? {
                time_date_week: timeType === 'all' ? 254 : timeFrameValues.time_date_week,
                time_hour_day: timeType === 'all' ? 16777215 : timeFrameValues.time_hour_day
              }
            : {}),
          ...(timeType === 'birthday' &&
            !onlyBirthdayDay && {
              day_before_birthday: parseInt(beforeBirthdayDays) || 0,
              day_after_birthday: parseInt(afterBirthdayDays) || 0
            })
        }

        await updateExtraPointMutation.mutateAsync(updateData)
      } else {
        const createData = {
          company_id: posCompanyData.company_id,
          type_id: selectedMembershipType,
          type_name: selectedMembershipTypeData?.type_name || '',
          start_date: `${startDate} 00:00:00`,
          end_date: `${endDate} 23:59:59`,
          extra_rate: parseFloat(extraRate),
          active: isActive ? 1 : 0,
          extra_rate_type: timeType === 'all' ? 'SPECIAL_TIME' : timeType === 'timeframe' ? 'SPECIAL_TIME' : 'BIRTHDAY',
          time_date_week: timeType === 'all' ? 254 : timeType === 'timeframe' ? timeFrameValues.time_date_week : 0,
          time_hour_day: timeType === 'all' ? 16777215 : timeType === 'timeframe' ? timeFrameValues.time_hour_day : 0,
          ...(timeType === 'birthday' &&
            !onlyBirthdayDay && {
              day_before_birthday: parseInt(beforeBirthdayDays) || 0,
              day_after_birthday: parseInt(afterBirthdayDays) || 0
            })
        }

        const params = {
          pos_parent: brandsData[0].brand_id
        }

        await createExtraPointMutation.mutateAsync({ data: createData, params })
      }

      onOpenChange(false)
    } catch (error) {
      console.error('Error submitting extra point:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (!open) return null

  return (
    <div className='fixed inset-0 z-50 flex'>
      {/* Backdrop */}
      <div className='fixed inset-0 bg-black/50 transition-opacity duration-300' onClick={() => onOpenChange(false)} />

      {/* Modal */}
      <div
        className={`fixed top-0 right-0 h-full w-full max-w-2xl transform bg-white shadow-xl transition-transform duration-300 ease-in-out ${open ? 'translate-x-0' : 'translate-x-full'} `}
      >
        {/* Header */}
        <div className='flex items-center justify-between border-b px-6 py-4'>
          <div>
            <h2 className='text-xl font-semibold'>{editMode ? 'Chỉnh sửa hệ số tích điểm' : 'Tạo hệ số tích điểm'}</h2>
          </div>
          <Button variant='ghost' size='sm' onClick={() => onOpenChange(false)} className='h-8 w-8 p-0'>
            <IconX className='h-4 w-4' />
          </Button>
        </div>

        <div className='border-b bg-blue-50 px-6 py-4'>
          <p className='text-sm text-blue-800'>
            <strong>Lưu ý:</strong> Nếu bạn tạo nhiều hệ số gia tăng tỷ lệ tích điểm khác nhau cùng áp dụng trong một
            thời điểm, hệ thống sẽ ưu tiên chính sách áp dụng cho sinh nhật thành viên, sau đó đến ngày trong tuần và
            giờ trong ngày; và ưu tiên hệ số tạo gần nhất.
          </p>
        </div>

        <div className='flex-1 overflow-y-auto px-6 py-6'>
          <div className='space-y-6'>
            <div className='space-y-2'>
              <Label htmlFor='membership-type' className='text-sm font-medium'>
                Hạng thành viên <span className='text-red-500'>*</span>
              </Label>
              <Select value={selectedMembershipType} onValueChange={setSelectedMembershipType}>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='Chọn hạng thành viên' />
                </SelectTrigger>
                <SelectContent>
                  {membershipTypeQuery.isLoading ? (
                    <SelectItem value='loading' disabled>
                      Đang tải...
                    </SelectItem>
                  ) : membershipTypes.length > 0 ? (
                    membershipTypes.map(type => (
                      <SelectItem key={type.id} value={type.type_id}>
                        {type.type_name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value='no-data' disabled>
                      Không có dữ liệu
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className='space-y-2'>
              <Label className='text-sm font-medium'>
                Khoảng thời gian áp dụng <span className='text-red-500'>*</span>
              </Label>
              <div className='space-y-3'>
                <div className='flex items-center gap-3'>
                  <Label htmlFor='start-date' className='min-w-[40px] text-sm font-medium'>
                    Từ
                  </Label>
                  <Input
                    id='start-date'
                    type='date'
                    value={startDate}
                    onChange={e => setStartDate(e.target.value)}
                    min={editMode ? undefined : new Date().toISOString().split('T')[0]}
                    className='flex-1'
                  />
                </div>
                <div className='flex items-center gap-3'>
                  <Label htmlFor='end-date' className='min-w-[40px] text-sm font-medium'>
                    Đến
                  </Label>
                  <Input
                    id='end-date'
                    type='date'
                    value={endDate}
                    onChange={e => setEndDate(e.target.value)}
                    min={startDate || (editMode ? undefined : new Date().toISOString().split('T')[0])}
                    className='flex-1'
                  />
                </div>
              </div>
            </div>

            <div className='space-y-2'>
              <Label className='text-sm font-medium'>Thay đổi thời gian tích điểm</Label>
              <div className='flex items-center gap-2 text-sm'>
                <span>{(pointRate * 100).toFixed(1)}%</span>
                <span>x</span>
                <Input
                  type='number'
                  value={extraRate}
                  onChange={e => {
                    const value = e.target.value
                    if (value === '' || /^\d*\.?\d*$/.test(value)) {
                      setExtraRate(value)
                    }
                  }}
                  onKeyDown={e => {
                    if (
                      !/[\d.]/.test(e.key) &&
                      !['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(e.key)
                    ) {
                      e.preventDefault()
                    }
                  }}
                  placeholder='0'
                  className='w-20 text-center'
                  min='0'
                  step='0.1'
                  pattern='[0-9]*\.?[0-9]*'
                />
                <span>=</span>
                <span className='font-medium text-green-600'>{calculateResult()}%</span>
              </div>
            </div>

            <div className='space-y-2'>
              <RadioGroup value={timeType} onValueChange={setTimeType} className='flex flex-row space-x-6'>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='all' id='all' />
                  <Label htmlFor='all' className='cursor-pointer text-sm font-medium'>
                    Tất cả khung giờ
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='timeframe' id='timeframe' />
                  <Label htmlFor='timeframe' className='cursor-pointer text-sm font-medium'>
                    Khung thời gian
                  </Label>
                </div>
                <div className='flex items-center space-x-2'>
                  <RadioGroupItem value='birthday' id='birthday' />
                  <Label htmlFor='birthday' className='cursor-pointer text-sm font-medium'>
                    Ngày sinh nhật
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {timeType === 'timeframe' && (
              <MarketingTimeframeSection
                marketingDays={marketingDays}
                marketingHours={marketingHours}
                onDaysChange={setMarketingDays}
                onHoursChange={setMarketingHours}
                onTimeValuesChange={setTimeFrameValues}
              />
            )}

            {timeType === 'birthday' && (
              <div className='space-y-3'>
                <Label className='text-sm font-medium'>Áp dụng cho sinh nhật</Label>

                {!onlyBirthdayDay && (
                  <div className='grid grid-cols-2 gap-4'>
                    <div className='flex items-center gap-2'>
                      <Label className='text-sm font-medium whitespace-nowrap'>Từ trước sinh nhật</Label>
                      <Input
                        type='number'
                        value={beforeBirthdayDays}
                        onChange={e => {
                          const value = e.target.value
                          if (value === '' || /^\d+$/.test(value)) {
                            setBeforeBirthdayDays(value)
                          }
                        }}
                        placeholder='0'
                        className='w-16 text-center'
                        min='0'
                      />
                      <span className='text-sm'>ngày</span>
                    </div>

                    <div className='flex items-center gap-2'>
                      <Label className='text-sm font-medium whitespace-nowrap'>Đến sau sinh nhật</Label>
                      <Input
                        type='number'
                        value={afterBirthdayDays}
                        onChange={e => {
                          const value = e.target.value
                          if (value === '' || /^\d+$/.test(value)) {
                            setAfterBirthdayDays(value)
                          }
                        }}
                        placeholder='0'
                        className='w-16 text-center'
                        min='0'
                      />
                      <span className='text-sm'>ngày</span>
                    </div>
                  </div>
                )}

                <div className='flex items-center space-x-2'>
                  <Checkbox
                    id='only-birthday-day'
                    checked={onlyBirthdayDay}
                    onCheckedChange={checked => setOnlyBirthdayDay(checked === true)}
                  />
                  <Label htmlFor='only-birthday-day' className='cursor-pointer text-sm font-medium'>
                    Chỉ áp dụng trong ngày sinh nhật
                  </Label>
                </div>
              </div>
            )}

            <div className='flex items-center justify-between'>
              <Label htmlFor='status' className='text-sm font-medium'>
                Trạng thái
              </Label>
              <Switch id='status' checked={isActive} onCheckedChange={setIsActive} />
            </div>
          </div>
        </div>

        <div className='border-t px-6 py-4'>
          <div className='flex justify-end'>
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? (editMode ? 'Đang cập nhật...' : 'Đang tạo...') : editMode ? 'Cập nhật' : 'Tạo mới'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
