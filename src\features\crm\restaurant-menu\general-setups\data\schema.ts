import { z } from 'zod'

export const storeInfoSchema = z.object({
  name: z.string().min(1, 'Tên nhà hàng là bắt buộc'),
  address: z.string().min(1, 'Đ<PERSON>a chỉ nhà hàng là bắt buộc'),
  phone: z.string().min(1, 'SĐT cửa hàng là bắt buộc'),
  partnerDriverPhone: z.string().optional(),
  active: z.boolean(),
  onlineSales: z.boolean(),
  deliverySales: z.boolean(),
  onlineReservation: z.boolean(),
  emailList: z.array(z.string().email('Email không hợp lệ')),
  banner: z.string().optional()
})

export type StoreInfoFormData = z.infer<typeof storeInfoSchema>
