import { Button, Label } from '@/components/ui'

interface ItemAssignmentSectionProps {
  selectedItems: string[]
  onItemSelection: () => void
  getSelectedItemsDisplay: () => string
  isFieldsDisabledAfterCreation: boolean
}

export function ItemAssignmentSection({
  selectedItems: _selectedItems,
  onItemSelection,
  getSelectedItemsDisplay,
  isFieldsDisabledAfterCreation
}: ItemAssignmentSectionProps) {
  return (
    <div className='space-y-4'>
      <div>
        <h3 className='text-lg font-medium text-gray-900'>Thêm các món vào nhóm</h3>
        <p className='text-sm text-gray-600'>
          Các món đang thuộc nhóm khác sẽ được gán lại vào nhóm này.
        </p>
      </div>

      <div className='flex items-center gap-4'>
        <Label className='min-w-[200px] text-sm font-medium'>Á<PERSON> dụng cho món</Label>
        <div className='flex-1'>
          <Button
            type='button'
            variant='outline'
            onClick={onItemSelection}
            className='w-full justify-start text-left'
            disabled={isFieldsDisabledAfterCreation}
          >
            {getSelectedItemsDisplay()}
          </Button>
        </div>
      </div>
    </div>
  )
}
