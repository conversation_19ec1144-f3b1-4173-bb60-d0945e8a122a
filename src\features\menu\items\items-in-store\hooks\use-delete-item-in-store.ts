import { useMutation, useQueryClient } from '@tanstack/react-query'

import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { api } from '@/lib/api/pos/pos-api'

import { QUERY_KEYS } from '@/constants/query-keys'

export const useDeleteItemInStore = () => {
  const queryClient = useQueryClient()
  const { company, brands } = useAuthStore(state => state.auth)
  const selectedBrand = brands?.[0]

  const { mutate, isPending } = useMutation({
    mutationFn: async (id: string) => {
      if (!company?.id || !selectedBrand?.id) {
        throw new Error('Missing company or brand information')
      }

      const response = await api.delete('/mdata/v1/item', {
        params: {
          company_uid: company.id,
          brand_uid: selectedBrand.id,
          id: id
        }
      })

      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST]
      })
      toast.success('Xóa món ăn thành công!')
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi xóa món ăn: ${error.message}`)
    }
  })

  return { deleteItemInStore: mutate, isDeleting: isPending }
}
