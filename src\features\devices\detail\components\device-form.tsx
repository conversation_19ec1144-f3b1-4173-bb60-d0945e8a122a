import { useState, useEffect } from 'react'

import { useNavigate } from '@tanstack/react-router'

import type { Device, DeviceType, DeviceStatus } from '@/types/device'
import { X, ChevronDown, ChevronRight, Check, ChevronsUpDown } from 'lucide-react'
import { toast } from 'sonner'

import { useAuthStore } from '@/stores/authStore'

import { cn } from '@/lib/utils'

import { getErrorMessage } from '@/utils/error-utils'

import {
  useDeviceDetail,
  useUpdateDevice,
  useCreateDevice,
  usePrintersData,
  useDeletePrinter,
  useItemTypesData,
  useCombosData,
  useStoresData
} from '@/hooks/api'

import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'

import { ConfirmModal, PosModal } from '@/components/pos'
import {
  Button,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Checkbox,
  RadioGroup,
  RadioGroupItem,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui'

import { PrinterModal, OrderLogModal, CancelDeviceModal } from './'

// Constants for device configuration
const KDS_NOTIFICATION_OPTIONS = [
  { value: 'Không hiển thị', label: 'Không hiển thị', apiValue: 0 },
  {
    value: 'Nhận thông báo từ KDS với order từ thiết bị',
    label: 'Nhận thông báo từ KDS với order từ thiết bị',
    apiValue: 1
  },
  { value: 'Nhận tất cả thông báo từ KDS', label: 'Nhận tất cả thông báo từ KDS', apiValue: 2 }
] as const

const DEVICE_TYPE_LOCAL_OPTIONS = [
  { value: 'None', label: 'None', apiValue: 0 },
  { value: 'Máy chủ', label: 'Máy chủ', apiValue: 1 },
  { value: 'Máy trạm', label: 'Máy trạm', apiValue: 2 }
] as const

const TYPE_ALLOW_CONNECT_POS_OPTIONS = [
  { value: 'Không cho phép', label: 'Không cho phép', apiValue: 0 },
  { value: 'Cho phép', label: 'Cho phép', apiValue: 1 }
] as const

// Helper functions to convert between API values and display values
const getKdsNotificationLabel = (apiValue: number): string => {
  console.log('🔍 getKdsNotificationLabel called with:', {
    apiValue,
    type: typeof apiValue,
    isNull: apiValue === null,
    isUndefined: apiValue === undefined,
    isNaN: isNaN(apiValue),
    availableOptions: KDS_NOTIFICATION_OPTIONS
  })

  // Always default to 'Không hiển thị' for safety
  if (apiValue === null || apiValue === undefined || isNaN(apiValue)) {
    console.log('🔍 Returning default due to null/undefined/NaN')
    return 'Không hiển thị'
  }

  const option = KDS_NOTIFICATION_OPTIONS.find(opt => opt.apiValue === apiValue)
  const result = option?.value || 'Không hiển thị'

  console.log('🔍 Found option:', option, 'Returning:', result)
  return result
}

const getKdsNotificationApiValue = (label: string): number => {
  const option = KDS_NOTIFICATION_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 0
}

const getDeviceTypeLocalLabel = (apiValue: number): string => {
  const option = DEVICE_TYPE_LOCAL_OPTIONS.find(opt => opt.apiValue === apiValue)
  return option?.value || 'None'
}

const getDeviceTypeLocalApiValue = (label: string): number => {
  const option = DEVICE_TYPE_LOCAL_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 0
}

const getTypeAllowConnectPosLabel = (apiValue: number): string => {
  const option = TYPE_ALLOW_CONNECT_POS_OPTIONS.find(opt => opt.apiValue === apiValue)
  return option?.value || 'Không cho phép'
}

const getTypeAllowConnectPosApiValue = (label: string): number => {
  const option = TYPE_ALLOW_CONNECT_POS_OPTIONS.find(opt => opt.value === label)
  return option?.apiValue ?? 0
}

function DeviceForm({ id, storeUid }: { id?: string; storeUid?: string }) {
  const navigate = useNavigate()
  const isEditMode = !!id

  const { mutate: updateDevice, isPending: isUpdating } = useUpdateDevice()
  const { mutate: createDevice, isPending: isCreating } = useCreateDevice()
  const { mutate: deletePrinter, isPending: isDeletingPrinter } = useDeletePrinter()
  const { data: deviceData, isLoading: isLoadingDevice } = useDeviceDetail(id || '', isEditMode)
  const { data: storesData } = useStoresData()

  // Fetch printers data for the device
  const { data: printersData, isLoading: isLoadingPrinters } = usePrintersData({
    params: { pos_device_code: deviceData?.device_code },
    enabled: isEditMode && !!deviceData?.device_code
  })

  // Fetch item types (nhóm món) and combos data
  const { data: itemTypesData } = useItemTypesData({
    store_uid: deviceData?.storeId || storeUid,
    enabled: (isEditMode && !!deviceData) || !!storeUid
  })

  const { data: combosData } = useCombosData({
    storeUid: deviceData?.storeId || storeUid,
    enabled: (isEditMode && !!deviceData) || !!storeUid
  })

  // Debug logging for API calls
  console.log('🔍 Group Selection Modal API Debug:', {
    deviceStoreId: deviceData?.storeId,
    propStoreUid: storeUid,
    finalStoreUid: deviceData?.storeId || storeUid,
    itemTypesDataCount: itemTypesData?.length || 0,
    combosDataCount: combosData?.length || 0,
    isEditMode,
    hasDeviceData: !!deviceData
  })

  // useEffect to handle pre-selected items in Group Selection Modal
  useEffect(() => {
    if (deviceData && isEditMode) {
      const extraData = (deviceData as any).extra_data || {}
      const itemTypeIgnore = extraData.item_type_ignore || []

      console.log('🔍 Loading pre-selected items:', {
        itemTypeIgnore,
        itemTypesDataCount: itemTypesData?.length || 0,
        combosDataCount: combosData?.length || 0
      })

      // Reset selections first
      setSelectedGroups(new Set())
      setSelectedCombos(new Set())

      if (Array.isArray(itemTypeIgnore) && itemTypeIgnore.length > 0) {
        const newSelectedGroups = new Set<string>()
        const newSelectedCombos = new Set<string>()

        itemTypeIgnore.forEach((ignoreItemId: string) => {
          console.log('🔍 Processing ignore item:', ignoreItemId)

          // Check if it's a group (item type) - starts with "ITEM_TYPE-"
          if (ignoreItemId.startsWith('ITEM_TYPE-')) {
            const foundGroup = itemTypesData?.find(
              group => group.item_type_id === ignoreItemId || group.id === ignoreItemId
            )

            if (foundGroup) {
              newSelectedGroups.add(foundGroup.id)
              console.log('🔍 Found and selected group:', foundGroup.item_type_name)
            } else {
              console.warn('🔍 Group not found for ID:', ignoreItemId)
            }
          }
          // Check if it's a combo - starts with "COMBO-"
          else if (ignoreItemId.startsWith('COMBO-')) {
            const foundCombo = combosData?.find(combo => combo.package_id === ignoreItemId || combo.id === ignoreItemId)

            if (foundCombo) {
              newSelectedCombos.add(foundCombo.id)
              console.log('🔍 Found and selected combo:', foundCombo.package_name)
            } else {
              console.warn('🔍 Combo not found for ID:', ignoreItemId)
            }
          }
          // Fallback: try to find by ID in both groups and combos
          else {
            const foundGroup = itemTypesData?.find(group => group.id === ignoreItemId)
            const foundCombo = combosData?.find(combo => combo.id === ignoreItemId)

            if (foundGroup) {
              newSelectedGroups.add(foundGroup.id)
              console.log('🔍 Found group by ID:', foundGroup.item_type_name)
            } else if (foundCombo) {
              newSelectedCombos.add(foundCombo.id)
              console.log('🔍 Found combo by ID:', foundCombo.package_name)
            } else {
              console.warn('🔍 Item not found for ID:', ignoreItemId)
            }
          }
        })

        setSelectedGroups(newSelectedGroups)
        setSelectedCombos(newSelectedCombos)

        console.log('🔍 Set pre-selected items:', {
          selectedGroupsCount: newSelectedGroups.size,
          selectedCombosCount: newSelectedCombos.size,
          selectedGroups: Array.from(newSelectedGroups),
          selectedCombos: Array.from(newSelectedCombos)
        })
      }
    }
  }, [deviceData, itemTypesData, combosData, isEditMode])

  const [formData, setFormData] = useState({
    // Editable fields
    deviceName: '', // Thêm field cho tên thiết bị
    deviceType: '', // Thêm field cho loại máy
    newIpAddress: '',
    isActive: true,
    // Config fields from extra_data
    displayColumns: '5', // Cấu hình hiển thị khu vực (5,6,7,8 cột)
    enableTabManagement: true, // Hiển thị tab quản lý khu vực
    enableScreen2: true, // Kích hoạt màn hình 2
    useItemInStore: true, // Dùng mẫu in tem của máy chủ
    kdsNotificationConfig: 'Không hiển thị', // Cấu hình hiển thị thông báo KDS
    enablePosNutMode: false, // Hiển thị nút mở két ở POS
    specialConfigType: '', // Nhập cấu hình kỳ tự đặc biệt
    enableComboGroup: false, // Ẩn nhóm hoặc combo không hiển thị trên thiết bị
    enableTabDisplay: true, // Giao diện mới ở tab nhà hàng
    enableTabKds: true, // Kích hoạt tab KDS
    deviceTypeLocal: 'None', // Loại máy: None/Máy chủ/Máy trạm
    typeAllowConnectPos: 'Cho phép' // Cho phép kết nối đến POS máy chủ qua internet
  })

  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showPrinterModal, setShowPrinterModal] = useState(false)
  const [showDeletePrinterModal, setShowDeletePrinterModal] = useState(false)
  const [printerToDelete, setPrinterToDelete] = useState<any>(null)
  const [printerToEdit, setPrinterToEdit] = useState<any>(null)
  const [isPrinterEditMode, setIsPrinterEditMode] = useState(false)
  const [showOrderLogModal, setShowOrderLogModal] = useState(false)
  const [showGroupSelectionModal, setShowGroupSelectionModal] = useState(false)
  const [activeTab, setActiveTab] = useState('groups') // 'groups' or 'combos'
  const [openKdsNotification, setOpenKdsNotification] = useState(false)
  const [openDeviceTypeLocal, setOpenDeviceTypeLocal] = useState(false)
  const [allowEditIpServer, setAllowEditIpServer] = useState(false)

  // State for groups/combos selection
  const [selectedGroups, setSelectedGroups] = useState<Set<string>>(new Set())
  const [selectedCombos, setSelectedCombos] = useState<Set<string>>(new Set())
  const [groupSearchTerm, setGroupSearchTerm] = useState('')
  const [selectedSectionOpen, setSelectedSectionOpen] = useState(true)
  const [remainingSectionOpen, setRemainingSectionOpen] = useState(true)

  // Process item types (nhóm món) data
  const itemTypes = itemTypesData || []
  const filteredItemTypes = itemTypes.filter((item: any) =>
    item.item_type_name.toLowerCase().includes(groupSearchTerm.toLowerCase())
  )

  // Process combos data
  const combos = combosData || []
  const filteredCombos = combos.filter((combo: any) =>
    combo.package_name.toLowerCase().includes(groupSearchTerm.toLowerCase())
  )

  // Get current tab data
  const getCurrentTabData = () => {
    if (activeTab === 'groups') {
      return {
        allItems: filteredItemTypes.map((item: any) => ({
          id: item.id,
          name: item.item_type_name,
          type: 'group' as const
        })),
        selectedItems: selectedGroups,
        setSelectedItems: setSelectedGroups
      }
    } else {
      return {
        allItems: filteredCombos.map((combo: any) => ({
          id: combo.id,
          name: combo.package_name,
          type: 'combo' as const
        })),
        selectedItems: selectedCombos,
        setSelectedItems: setSelectedCombos
      }
    }
  }

  const { allItems, selectedItems, setSelectedItems } = getCurrentTabData()
  const selectedTabItems = allItems.filter(item => selectedItems.has(item.id))
  const remainingTabItems = allItems.filter(item => !selectedItems.has(item.id))

  const handleItemToggle = (itemId: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  const getTotalSelectedCount = () => {
    return selectedGroups.size + selectedCombos.size
  }

  // Handle Group Selection Modal confirm
  const handleGroupSelectionConfirm = () => {
    // Convert selected groups and combos to the format expected by API
    const selectedGroupIds = Array.from(selectedGroups)
    const selectedComboIds = Array.from(selectedCombos)

    // Find the actual item_type_id and package_id for API
    const groupIgnoreList: string[] = []
    const comboIgnoreList: string[] = []

    selectedGroupIds.forEach(groupId => {
      const foundGroup = itemTypesData?.find(group => group.id === groupId)
      if (foundGroup) {
        groupIgnoreList.push(foundGroup.item_type_id || foundGroup.id)
      }
    })

    selectedComboIds.forEach(comboId => {
      const foundCombo = combosData?.find(combo => combo.id === comboId)
      if (foundCombo) {
        comboIgnoreList.push(foundCombo.package_id || foundCombo.id)
      }
    })

    // Combine both lists for item_type_ignore
    const itemTypeIgnore = [...groupIgnoreList, ...comboIgnoreList]

    console.log('🔍 Group Selection Confirm:', {
      selectedGroupIds,
      selectedComboIds,
      groupIgnoreList,
      comboIgnoreList,
      finalItemTypeIgnore: itemTypeIgnore
    })

    // Update form data with the new ignore list
    setFormData(
      prev =>
        ({
          ...prev,
          extra_data: {
            ...(prev as any).extra_data,
            item_type_ignore: itemTypeIgnore
          }
        }) as any
    )

    setShowGroupSelectionModal(false)
  }

  useEffect(() => {
    if (deviceData && isEditMode) {
      const extraData = (deviceData as any).extra_data || {}
      console.log('📊 Extra data from API:', extraData)

      // Only set editable fields
      setFormData({
        deviceName: (deviceData as any)?.device_name || deviceData?.name || '',
        deviceType: deviceData?.type || '',
        newIpAddress: '',
        isActive: (deviceData as any)?.active === 1 || deviceData.isActive === true,
        // Set config from extra_data if available - mapping theo dữ liệu thực tế từ chi_tiet_thiet_bi.md
        displayColumns: extraData.column_table ?? '5',
        enableTabManagement: extraData.area_manager_enable === 1,
        enableScreen2: extraData.dual_screen_enable === 1,
        useItemInStore: extraData.allow_print_label === 1,
        kdsNotificationConfig: (() => {
          console.log('🔍 KDS Notification Debug:', {
            state_operate: extraData.state_operate,
            type: typeof extraData.state_operate,
            result: getKdsNotificationLabel(extraData.state_operate)
          })
          return getKdsNotificationLabel(extraData.state_operate)
        })(),
        enablePosNutMode: extraData.enable_cash_drawer === 1,
        specialConfigType: extraData.special_character || '',
        enableComboGroup: Array.isArray(extraData.item_type_ignore) && extraData.item_type_ignore.length > 0,
        enableTabDisplay: extraData.enable_tab_order_ta === 1,
        enableTabKds: extraData.enable_tab_kds === 1,
        deviceTypeLocal: getDeviceTypeLocalLabel(extraData.device_type_local ?? 0),
        typeAllowConnectPos: getTypeAllowConnectPosLabel(extraData.type_allow_connect_pos ?? 0)
      })
    } else if (!isEditMode) {
      // Set default values for create mode
      setFormData(prev => ({
        ...prev,
        deviceName: '',
        deviceType: 'POS', // Default device type
        newIpAddress: '',
        isActive: true,
        // Default config values
        displayColumns: '5',
        enableTabManagement: true,
        enableScreen2: false,
        useItemInStore: true,
        kdsNotificationConfig: 'Không hiển thị',
        enablePosNutMode: false,
        specialConfigType: '',
        enableComboGroup: false,
        enableTabDisplay: true,
        enableTabKds: true,
        deviceTypeLocal: 'None',
        typeAllowConnectPos: 'Không cho phép'
      }))
    }
  }, [deviceData, isEditMode])

  const handleBack = () => {
    navigate({ to: '/devices/list' })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Debug localStorage and auth store
    console.log('🔍 Debug localStorage and auth store:')
    console.log('pos_user_data:', localStorage.getItem('pos_user_data'))
    console.log('pos_company_data:', localStorage.getItem('pos_company_data'))
    console.log('pos_brands_data:', localStorage.getItem('pos_brands_data'))

    const authState = useAuthStore.getState().auth
    console.log('Auth store state:', {
      hasUser: !!authState.user,
      hasCompany: !!authState.company,
      brandsCount: authState.brands?.length || 0,
      companyId: authState.company?.id,
      firstBrandId: authState.brands?.[0]?.id
    })

    try {
      if (isEditMode && deviceData) {
        // UPDATE: Prepare update data with editable fields
        const updateData: Partial<Device> & { extra_data?: any } = {
          name: formData.deviceName, // Cập nhật tên thiết bị
          type: formData.deviceType as DeviceType, // Cập nhật loại máy với proper type
          storeId: deviceData.storeId, // Keep original storeId
          status: (formData.isActive ? 'active' : 'inactive') as DeviceStatus, // Map isActive to status
          // Update extra_data config
          extra_data: {
            ...(deviceData as any).extra_data,
            column_table: formData.displayColumns,
            area_manager_enable: formData.enableTabManagement ? 1 : 0,
            dual_screen_enable: formData.enableScreen2 ? 1 : 0,
            allow_print_label: formData.useItemInStore ? 1 : 0,
            enable_cash_drawer: formData.enablePosNutMode ? 1 : 0,
            enable_tab_order_ta: formData.enableTabDisplay ? 1 : 0,
            enable_tab_kds: formData.enableTabKds ? 1 : 0,
            state_operate: getKdsNotificationApiValue(formData.kdsNotificationConfig),
            special_character: formData.specialConfigType,
            device_type_local: getDeviceTypeLocalApiValue(formData.deviceTypeLocal),
            type_allow_connect_pos: getTypeAllowConnectPosApiValue(formData.typeAllowConnectPos),
            // Use item_type_ignore from formData if enableComboGroup is true, otherwise clear it
            item_type_ignore: formData.enableComboGroup
              ? (formData as any).extra_data?.item_type_ignore || (deviceData as any).extra_data?.item_type_ignore || []
              : []
          }
        }

        // Add new IP if provided
        if (formData.newIpAddress.trim()) {
          updateData.ipAddress = formData.newIpAddress
        }

        console.log('🔍 Update data being sent:', updateData)
        console.log('🔍 Form data deviceName:', formData.deviceName)
        console.log('🔍 Current device name:', (deviceData as any)?.device_name || deviceData?.name)
        console.log('🔍 Group Selection Debug:', {
          enableComboGroup: formData.enableComboGroup,
          formDataExtraData: (formData as any).extra_data,
          deviceDataItemTypeIgnore: (deviceData as any).extra_data?.item_type_ignore,
          finalItemTypeIgnore: updateData.extra_data?.item_type_ignore,
          selectedGroupsCount: selectedGroups.size,
          selectedCombosCount: selectedCombos.size,
          selectedGroupsArray: Array.from(selectedGroups),
          selectedCombosArray: Array.from(selectedCombos)
        })

        console.log('🚀 About to call updateDevice with:', { id: deviceData.id, ...updateData })

        updateDevice(
          { id: deviceData.id, ...updateData },
          {
            onSuccess: result => {
              console.log('✅ Update successful:', result)
              toast.success('Cập nhật thiết bị thành công!')
              navigate({ to: '/devices/list' })
            },
            onError: error => {
              console.error('❌ Update failed:', error)
              const errorMessage = getErrorMessage(error)
              toast.error(errorMessage)
            }
          }
        )
      } else {
        // CREATE: Tạo thiết bị mới
        if (!storeUid) {
          throw new Error('Store UID is required for creating new device')
        }

        // Lấy brandUid từ store data
        const selectedStore = storesData?.find(store => store.id === storeUid)
        if (!selectedStore) {
          throw new Error('Store not found')
        }

        const createData = {
          deviceName: formData.deviceName,
          storeId: storeUid,
          deviceType: formData.deviceType,
          brandUid: selectedStore.brandId
        }

        createDevice(createData, {
          onSuccess: result => {
            console.log('✅ Create successful:', result)
            toast.success('Tạo thiết bị thành công!')
            navigate({ to: '/devices/list' })
          },
          onError: error => {
            console.error('❌ Create failed:', error)
            const errorMessage = getErrorMessage(error)
            toast.error(errorMessage)
          }
        })
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error)
      toast.error(errorMessage)
    }
  }

  const handleSavePrinter = (printerData: any) => {
    // TODO: Implement save printer logic
    console.log('Save printer:', printerData)
    toast.success('Tạo máy in thành công!')
  }

  const handleDeletePrinter = (printer: any) => {
    setPrinterToDelete(printer)
    setShowDeletePrinterModal(true)
  }

  const confirmDeletePrinter = () => {
    if (!printerToDelete) return

    deletePrinter(printerToDelete.id, {
      onSuccess: () => {
        toast.success(`Xóa máy in "${printerToDelete.printer_name}" thành công!`)
        setShowDeletePrinterModal(false)
        setPrinterToDelete(null)
      },
      onError: error => {
        const errorMessage = getErrorMessage(error)
        toast.error(errorMessage)
      }
    })
  }

  if (isLoadingDevice) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='mb-8'>
          <div className='mb-4 flex items-center justify-between'>
            <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
              <X className='h-4 w-4' />
            </Button>
            <div className='text-center'>
              <h1 className='mb-2 text-3xl font-medium'>{isEditMode ? 'Chi tiết thiết bị' : 'Tạo thiết bị mới'}</h1>
            </div>
            <Button type='button' disabled={isUpdating || isCreating} className='min-w-[100px]' onClick={handleSubmit}>
              {isUpdating ? 'Đang cập nhật...' : isCreating ? 'Đang tạo...' : 'Lưu'}
            </Button>
          </div>
        </div>
        <div className='mx-auto max-w-4xl'>
          <div className='p-6'>
            <p className='text-center'>Đang tải...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button variant='ghost' size='sm' onClick={handleBack} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <div className='text-center'>
            <h1 className='mb-2 text-3xl font-medium'>{isEditMode ? 'Chi tiết thiết bị' : 'Tạo thiết bị mới'}</h1>
          </div>
          <Button type='button' disabled={isUpdating || isCreating} className='min-w-[100px]' onClick={handleSubmit}>
            {isUpdating ? 'Đang cập nhật...' : isCreating ? 'Đang tạo...' : 'Lưu'}
          </Button>
        </div>
      </div>

      <div className='mx-auto max-w-2xl'>
        <div className='mt-8 pt-6'>
          <h3 className='mb-4 text-lg font-semibold text-gray-900'>Chi tiết</h3>
        </div>
        <form onSubmit={handleSubmit} className='space-y-6'>
          {/* Single form layout - từ trên xuống dưới như hình */}
          <div className='space-y-4'>
            {/* Row 1: Tên thiết bị */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Tên thiết bị *</Label>
              <div className='col-span-2'>
                <Input
                  value={formData.deviceName}
                  onChange={e => setFormData(prev => ({ ...prev, deviceName: e.target.value }))}
                  placeholder='Nhập tên thiết bị'
                />
              </div>
            </div>

            {/* Row 2: Device code - chỉ hiển thị khi edit */}
            {isEditMode && (
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Device code</Label>
                <div className='col-span-2'>
                  <Input
                    value={(deviceData as any)?.device_code || deviceData?.serialNumber || ''}
                    className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                  />
                </div>
              </div>
            )}

            {/* Row 3: Loại thiết bị */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Loại thiết bị *</Label>
              <div className='col-span-2'>
                {isEditMode ? (
                  <Input
                    value={deviceData?.type || ''}
                    className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                  />
                ) : (
                  <Select
                    value={formData.deviceType}
                    onValueChange={value => setFormData(prev => ({ ...prev, deviceType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='Chọn loại thiết bị' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='POS'>POS</SelectItem>
                      <SelectItem value='POS_MINI'>POS MINI</SelectItem>
                      <SelectItem value='PDA'>PDA</SelectItem>
                      <SelectItem value='KDS'>KDS</SelectItem>
                      <SelectItem value='KDS_ORDER_CONTROL'>KDS ORDER CONTROL</SelectItem>
                      <SelectItem value='KDS_MAKER'>KDS MAKER</SelectItem>
                      <SelectItem value='SELF_ORDER'>SELF ORDER</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            {/* Row 4: Điểm bán hàng */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Điểm bán hàng *</Label>
              <div className='col-span-2'>
                <Input
                  value={
                    isEditMode
                      ? deviceData?.storeName || 'N/A'
                      : storesData?.find(store => store.id === storeUid)?.name || 'N/A'
                  }
                  disabled
                  className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                />
              </div>
            </div>

            {/* Row 5.5: Địa chỉ IP local của thiết bị - chỉ hiển thị khi edit */}
            {isEditMode && (
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Địa chỉ IP local </Label>
                <div className='col-span-2'>
                  <Input
                    value={(deviceData as any)?.my_ip_local || 'N/A'}
                    disabled
                    className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                  />
                </div>
              </div>
            )}

            {/* Row 6: Phiên bản - chỉ hiển thị khi edit */}
            {isEditMode && (
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Phiên bản</Label>
                <div className='col-span-2'>
                  <Input
                    value={(deviceData as any)?.version_app || deviceData?.version || 'N/A'}
                    disabled
                    className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                  />
                </div>
              </div>
            )}

            {/* Row 7: Múi giờ tại POS - chỉ hiển thị khi edit */}
            {isEditMode && (
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Múi giờ tại POS</Label>
                <div className='col-span-2'>
                  <Input
                    value={(deviceData as any)?.time_zone || 'GMT+07:00'}
                    disabled
                    className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                  />
                </div>
              </div>
            )}

            {/* Row 8: Thời gian cập nhật - chỉ hiển thị khi edit */}
            {isEditMode && (
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Thời gian cập nhật</Label>
                <div className='col-span-2'>
                  <Input
                    value={
                      (deviceData as any)?.updated_at
                        ? new Date((deviceData as any).updated_at).toLocaleString('vi-VN')
                        : 'N/A'
                    }
                    disabled
                    className='cursor-not-allowed bg-gray-50 font-medium text-gray-600'
                  />
                </div>
              </div>
            )}

            {/* Separator */}
            <div className='mt-8 pt-6'>
              <h3 className='mb-4 text-lg font-semibold text-gray-900'>Cấu hình thiết bị</h3>
            </div>

            {/* Row 9: Loại máy (Device Type Local) */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Loại máy</Label>
              <div className='col-span-2'>
                <Popover open={openDeviceTypeLocal} onOpenChange={setOpenDeviceTypeLocal}>
                  <PopoverTrigger asChild>
                    <Button
                      variant='outline'
                      role='combobox'
                      aria-expanded={openDeviceTypeLocal}
                      className='w-full justify-between'
                    >
                      {formData.deviceTypeLocal || 'Chọn loại máy'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className='w-full p-0'>
                    <Command>
                      <CommandInput placeholder='Tìm kiếm loại máy...' />
                      <CommandList>
                        <CommandEmpty>Không tìm thấy loại máy.</CommandEmpty>
                        <CommandGroup>
                          {DEVICE_TYPE_LOCAL_OPTIONS.map(option => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={() => {
                                setFormData(prev => ({ ...prev, deviceTypeLocal: option.value }))
                                setOpenDeviceTypeLocal(false)
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  formData.deviceTypeLocal === option.value ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Row 10: Địa chỉ IP máy chủ cho phép nhập khi tạo mới */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Địa chỉ IP máy chủ</Label>
              <div className='col-span-2 flex items-center gap-3'>
                {isEditMode ? (
                  <Input
                    value={
                      allowEditIpServer
                        ? formData.newIpAddress
                        : (deviceData as any)?.ip_local_server || (deviceData as any)?.ipAddress || ''
                    }
                    disabled={!allowEditIpServer}
                    onChange={e => setFormData(prev => ({ ...prev, newIpAddress: e.target.value }))}
                    className={allowEditIpServer ? '' : 'cursor-not-allowed bg-gray-50 font-medium text-gray-600'}
                    placeholder={allowEditIpServer ? 'Nhập địa chỉ IP máy chủ mới' : ''}
                  />
                ) : (
                  <Input
                    value={formData.newIpAddress}
                    onChange={e => setFormData(prev => ({ ...prev, newIpAddress: e.target.value }))}
                    placeholder='Nhập địa chỉ IP máy chủ'
                  />
                )}
                {isEditMode && (
                  <Checkbox
                    checked={allowEditIpServer}
                    onCheckedChange={checked => {
                      setAllowEditIpServer(!!checked)
                      if (checked) {
                        setFormData(prev => ({ ...prev, newIpAddress: '' }))
                      } else {
                        setFormData(prev => ({
                          ...prev,
                          newIpAddress: (deviceData as any)?.ip_local_server || (deviceData as any)?.ipAddress || ''
                        }))
                      }
                    }}
                  />
                )}
              </div>
            </div>

            {/* Row 11: Hiển thị tab quản lý khu vực */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Hiển thị tab quản lý khu vực</Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.enableTabManagement}
                  onCheckedChange={checked => setFormData(prev => ({ ...prev, enableTabManagement: !!checked }))}
                />
              </div>
            </div>

            {/* Row 12: Cấu hình hiển thị khu vực - chỉ hiển thị khi enableTabManagement được tick */}
            {formData.enableTabManagement && (
              <div className='grid grid-cols-3 items-center gap-4'>
                <Label className='text-right font-medium text-gray-700'>Cấu hình hiển thị khu vực</Label>
                <div className='col-span-2'>
                  <RadioGroup
                    value={formData.displayColumns}
                    onValueChange={value => setFormData(prev => ({ ...prev, displayColumns: value }))}
                    className='flex space-x-6'
                  >
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='5' id='col5' />
                      <Label htmlFor='col5'>5 cột</Label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='6' id='col6' />
                      <Label htmlFor='col6'>6 cột</Label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='7' id='col7' />
                      <Label htmlFor='col7'>7 cột</Label>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <RadioGroupItem value='8' id='col8' />
                      <Label htmlFor='col8'>8 cột</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            )}

            {/* Row 13: Kích hoạt màn hình 2 */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Kích hoạt màn hình 2</Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.enableScreen2}
                  onCheckedChange={checked => setFormData(prev => ({ ...prev, enableScreen2: !!checked }))}
                />
              </div>
            </div>

            {/* Row 14: Dùng mẫu in tem của máy chủ */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Dùng mẫu in tem của máy chủ</Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.useItemInStore}
                  onCheckedChange={checked => setFormData(prev => ({ ...prev, useItemInStore: !!checked }))}
                />
              </div>
            </div>

            {/* Row 15: Cấu hình hiển thị thông báo KDS */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-left font-medium text-gray-700'>Cấu hình hiện thông báo KDS</Label>
              <div className='col-span-2'>
                <Popover open={openKdsNotification} onOpenChange={setOpenKdsNotification}>
                  <PopoverTrigger asChild>
                    <Button
                      variant='outline'
                      role='combobox'
                      aria-expanded={openKdsNotification}
                      className='w-full justify-between'
                    >
                      {formData.kdsNotificationConfig || 'Chọn cấu hình thông báo KDS'}
                      <ChevronsUpDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className='w-full p-0'>
                    <Command>
                      <CommandInput placeholder='Tìm kiếm cấu hình...' />
                      <CommandList>
                        <CommandEmpty>Không tìm thấy cấu hình.</CommandEmpty>
                        <CommandGroup>
                          {KDS_NOTIFICATION_OPTIONS.map(option => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={() => {
                                setFormData(prev => ({ ...prev, kdsNotificationConfig: option.value }))
                                setOpenKdsNotification(false)
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  formData.kdsNotificationConfig === option.value ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Row 16: Hiển thị nút mở két ở POS */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Hiển thị nút mở két ở POS</Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.enablePosNutMode}
                  onCheckedChange={checked => setFormData(prev => ({ ...prev, enablePosNutMode: !!checked }))}
                />
              </div>
            </div>

            {/* Row 17: Cấu hình ký tự đặc biệt */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Cấu hình ký tự đặc biệt</Label>
              <div className='col-span-2'>
                <Input
                  value={formData.specialConfigType}
                  onChange={e => setFormData(prev => ({ ...prev, specialConfigType: e.target.value }))}
                  placeholder='Nhập cấu hình ký tự đặc biệt'
                />
              </div>
            </div>

            {/* Row 19: Ẩn nhóm hoặc combo */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-left font-medium text-gray-700'>
                Ẩn nhóm hoặc combo không hiển thị trên thiết bị
              </Label>
              <div className='col-span-2'>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setShowGroupSelectionModal(true)}
                  className='w-full justify-start text-left'
                >
                  {getTotalSelectedCount() > 0 ? `${getTotalSelectedCount()} nhóm hoặc combo` : '0 nhóm hoặc combo'}
                </Button>
              </div>
            </div>

            {/* Row 19: Giao diện mới ở tab nhà hàng */}
            {/* <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-left font-medium text-gray-700'>
                Cho phép kết nối đến POS máy chủ qua internet
              </Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.typeAllowConnectPos === 'Cho phép'}
                  onCheckedChange={checked =>
                    setFormData(prev => ({
                      ...prev,
                      typeAllowConnectPos: checked ? 'Cho phép' : 'Không cho phép'
                    }))
                  }
                />
              </div>
            </div> */}
            {/* Row 20: Giao diện mới ở tab nhà hàng */}
            <div className='grid grid-cols-3 items-center gap-4'>
              <Label className='text-right font-medium text-gray-700'>Giao diện mới ở tab nhà hàng</Label>
              <div className='col-span-2'>
                <Checkbox
                  checked={formData.enableTabDisplay}
                  onCheckedChange={checked => setFormData(prev => ({ ...prev, enableTabDisplay: !!checked }))}
                />
              </div>
            </div>
          </div>

          {/* Danh sách máy in */}
          {isEditMode && (
            <div className='mt-8 pt-6'>
              <div className='mb-4 flex items-center justify-between'>
                <h3 className='text-lg font-semibold text-gray-900'>Danh sách máy in</h3>
                <Button
                  type='button'
                  size='sm'
                  onClick={() => {
                    setPrinterToEdit(null)
                    setIsPrinterEditMode(false)
                    setShowPrinterModal(true)
                  }}
                  className='bg-blue-600 hover:bg-blue-700'
                >
                  Tạo máy in mới
                </Button>
              </div>

              <div className='rounded-md border'>
                <div className='bg-gray-50 px-4 py-3'>
                  <div className='grid grid-cols-4 gap-4 text-sm font-medium text-gray-700'>
                    <div>#</div>
                    <div>Tên máy in</div>
                    <div>Kiểu kết nối</div>
                    <div>Loại máy in</div>
                  </div>
                </div>
                <div className='divide-y divide-gray-200'>
                  {isLoadingPrinters ? (
                    <div className='px-4 py-8 text-center text-sm text-gray-500'>Đang tải danh sách máy in...</div>
                  ) : printersData?.data && printersData.data.length > 0 ? (
                    printersData.data.map((printer, index) => (
                      <div
                        key={printer.id}
                        className='grid cursor-pointer grid-cols-4 gap-4 px-4 py-3 text-sm hover:bg-gray-50'
                        onClick={() => {
                          console.log('Edit printer:', printer)
                          setPrinterToEdit(printer)
                          setIsPrinterEditMode(true)
                          setShowPrinterModal(true)
                        }}
                      >
                        <div>{index + 1}</div>
                        <div>{printer.printer_name}</div>
                        <div>{printer.type_printer == 'Wifi' ? 'LAN' : printer.type_printer.toUpperCase()}</div>
                        <div className='flex items-center justify-between'>
                          <span>
                            {printer.type === 'PRINT_HS'
                              ? printer.print_order === true
                                ? 'In order'
                                : 'In hoá đơn'
                              : printer.type === 'PRINT_LABLE'
                                ? 'In tem'
                                : printer.type}
                          </span>
                          <Button
                            variant='ghost'
                            size='sm'
                            className='h-6 w-6 p-0 text-gray-400 hover:text-red-600'
                            onClick={e => {
                              e.stopPropagation() // Prevent row click
                              handleDeletePrinter(printer)
                            }}
                            disabled={isDeletingPrinter}
                          >
                            ✕
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className='px-4 py-8 text-center text-sm text-gray-500'>Chưa có máy in nào được cấu hình</div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Nhật ký order của thiết bị */}
          {isEditMode && (
            <div className='mt-8'>
              <h3 className='mb-4 text-lg font-semibold text-gray-900'>Nhật ký order của thiết bị</h3>
              <div className='w-full text-center'>
                <Button type='button' variant='outline' onClick={() => setShowOrderLogModal(true)}>
                  Xem nhật ký order
                </Button>
              </div>
            </div>
          )}

          {/* Hủy thiết bị */}
          {isEditMode && (
            <div className='mt-8'>
              <h3 className='mb-2 text-lg font-semibold text-gray-900'>Hủy thiết bị</h3>
              <p className='mb-4 text-sm text-gray-600'>Thiết bị đã hủy sẽ không thể sử dụng lại.</p>
              <Button type='button' variant='destructive' onClick={() => setShowDeleteModal(true)}>
                Hủy thiết bị
              </Button>
            </div>
          )}
        </form>
      </div>

      <CancelDeviceModal
        open={showDeleteModal}
        onOpenChange={setShowDeleteModal}
        storeName={deviceData?.storeName}
        storeId={(deviceData as any)?.store_uid || deviceData?.storeId}
      />

      {/* Delete Printer Confirm Modal */}
      <ConfirmModal
        open={showDeletePrinterModal}
        onOpenChange={setShowDeletePrinterModal}
        content={`Bạn có muốn xóa máy in ${printerToDelete?.printer_name || 'In hoá đơn'}?`}
        confirmText='Xóa'
        onConfirm={confirmDeletePrinter}
        isLoading={isDeletingPrinter}
      />

      <PrinterModal
        open={showPrinterModal}
        onOpenChange={setShowPrinterModal}
        onSave={handleSavePrinter}
        deviceCode={(deviceData as any)?.device_code}
        printerData={printerToEdit}
        isEditMode={isPrinterEditMode}
      />

      <OrderLogModal
        open={showOrderLogModal}
        onOpenChange={setShowOrderLogModal}
        deviceCode={deviceData?.device_code}
      />

      {/* Group Selection Modal */}
      <PosModal
        title='Chọn nhóm hoặc combo không hiển thị trên thiết bị'
        centerTitle
        open={showGroupSelectionModal}
        onOpenChange={setShowGroupSelectionModal}
        onCancel={() => setShowGroupSelectionModal(false)}
        onConfirm={handleGroupSelectionConfirm}
        confirmText='Xác nhận'
        cancelText='Hủy'
        maxWidth='sm:max-w-2xl'
      >
        <div className='space-y-4'>
          <Input
            placeholder='Tìm kiếm nhóm hoặc combo'
            value={groupSearchTerm}
            onChange={e => setGroupSearchTerm(e.target.value)}
            className='w-full'
          />

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger value='groups'>Nhóm món</TabsTrigger>
              <TabsTrigger value='combos'>Combo</TabsTrigger>
            </TabsList>

            <TabsContent value='groups' className='space-y-4'>
              <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
                <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                  <span className='font-medium'>Đã chọn ({selectedTabItems.length})</span>
                  {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                </CollapsibleTrigger>
                <CollapsibleContent className='mt-2'>
                  <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                    {selectedTabItems.length === 0 ? (
                      <p className='text-sm text-gray-500'>Chưa có nhóm món nào được chọn</p>
                    ) : (
                      selectedTabItems.map(item => (
                        <label
                          key={item.id}
                          className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                        >
                          <Checkbox
                            checked={selectedItems.has(item.id)}
                            onCheckedChange={() => handleItemToggle(item.id)}
                          />
                          <div className='flex-1'>
                            <p className='text-sm font-medium'>{item.name}</p>
                          </div>
                        </label>
                      ))
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>

              <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
                <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                  <span className='font-medium'>Còn lại ({remainingTabItems.length})</span>
                  {remainingSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                </CollapsibleTrigger>
                <CollapsibleContent className='mt-2'>
                  <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                    {remainingTabItems.length === 0 ? (
                      <p className='text-sm text-gray-500'>Không có nhóm món nào</p>
                    ) : (
                      remainingTabItems.map(item => (
                        <label
                          key={item.id}
                          className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                        >
                          <Checkbox
                            checked={selectedItems.has(item.id)}
                            onCheckedChange={() => handleItemToggle(item.id)}
                          />
                          <div className='flex-1'>
                            <p className='text-sm font-medium'>{item.name}</p>
                          </div>
                        </label>
                      ))
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </TabsContent>

            <TabsContent value='combos' className='space-y-4'>
              <Collapsible open={selectedSectionOpen} onOpenChange={setSelectedSectionOpen}>
                <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                  <span className='font-medium'>Đã chọn ({selectedTabItems.length})</span>
                  {selectedSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                </CollapsibleTrigger>
                <CollapsibleContent className='mt-2'>
                  <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                    {selectedTabItems.length === 0 ? (
                      <p className='text-sm text-gray-500'>Chưa có combo nào được chọn</p>
                    ) : (
                      selectedTabItems.map(item => (
                        <label
                          key={item.id}
                          className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                        >
                          <Checkbox
                            checked={selectedItems.has(item.id)}
                            onCheckedChange={() => handleItemToggle(item.id)}
                          />
                          <div className='flex-1'>
                            <p className='text-sm font-medium'>{item.name}</p>
                          </div>
                        </label>
                      ))
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>

              <Collapsible open={remainingSectionOpen} onOpenChange={setRemainingSectionOpen}>
                <CollapsibleTrigger className='flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50'>
                  <span className='font-medium'>Còn lại ({remainingTabItems.length})</span>
                  {remainingSectionOpen ? <ChevronDown className='h-4 w-4' /> : <ChevronRight className='h-4 w-4' />}
                </CollapsibleTrigger>
                <CollapsibleContent className='mt-2'>
                  <div className='max-h-60 space-y-2 overflow-y-auto rounded-md border p-3'>
                    {remainingTabItems.length === 0 ? (
                      <p className='text-sm text-gray-500'>Không có combo nào</p>
                    ) : (
                      remainingTabItems.map(item => (
                        <label
                          key={item.id}
                          className='flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50'
                        >
                          <Checkbox
                            checked={selectedItems.has(item.id)}
                            onCheckedChange={() => handleItemToggle(item.id)}
                          />
                          <div className='flex-1'>
                            <p className='text-sm font-medium'>{item.name}</p>
                          </div>
                        </label>
                      ))
                    )}
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </TabsContent>
          </Tabs>
        </div>
      </PosModal>
    </div>
  )
}

export { DeviceForm }
