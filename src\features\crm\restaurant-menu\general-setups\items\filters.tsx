import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { ItemsActionBar } from './action-bar'

import type { ItemsFilters } from '../types'

interface DanhSachMonFiltersProps {
  filters: ItemsFilters
  onFiltersChange: (filters: Partial<ItemsFilters>) => void
}

export function DanhSachMonFilters({ filters, onFiltersChange }: DanhSachMonFiltersProps) {
  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value })
  }

  const handleGroupChange = (value: string) => {
    onFiltersChange({ group: value })
  }

  const handleTypeChange = (value: string) => {
    onFiltersChange({ type: value })
  }

  const handleStatusChange = (value: string) => {
    onFiltersChange({ status: value })
  }

  const handleCanTakeawayChange = (value: string) => {
    onFiltersChange({ canTakeaway: value })
  }

  const handleCanDeliveryChange = (value: string) => {
    onFiltersChange({ canDelivery: value })
  }

  const handleOrderChange = (value: string) => {
    onFiltersChange({ order: value })
  }

  const handleFilter = () => {
    console.log('Applying filters:', filters)
  }

  const handleViewPreview = () => {
    console.log('Xem trước')
  }

  const handleCreateItem = () => {
    console.log('Tạo món cha')
  }

  const handleSyncItems = () => {
    console.log('Đồng bộ món')
  }

  const getItemCount = () => {
    // Mock count for items
    return 39
  }

  return (
    <div className='space-y-4 mb-6'>
      {/* Filters */}
      <div className='flex items-center gap-2 flex-wrap'>
        <div className='w-[180px]'>
          <Input
            placeholder='TÊN/MÃ MÓN'
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            className='h-10'
          />
        </div>
        <div className='w-[140px]'>
          <Select value={filters.group} onValueChange={handleGroupChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='NHÓM MÓN' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='uncategory'>Uncategory</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[130px]'>
          <Select value={filters.type} onValueChange={handleTypeChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='LOẠI MÓN' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='normal'>Món thường</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[130px]'>
          <Select value={filters.status} onValueChange={handleStatusChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='TRẠNG THÁI' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='available'>Có bán</SelectItem>
              <SelectItem value='unavailable'>Hết hàng</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[130px]'>
          <Select value={filters.canTakeaway} onValueChange={handleCanTakeawayChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='BÁN TẠI CHỖ' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='true'>Có</SelectItem>
              <SelectItem value='false'>Không</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[130px]'>
          <Select value={filters.canDelivery} onValueChange={handleCanDeliveryChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='BÁN MANG ĐI' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='true'>Có</SelectItem>
              <SelectItem value='false'>Không</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='w-[110px]'>
          <Select value={filters.order} onValueChange={handleOrderChange}>
            <SelectTrigger className='h-10'>
              <SelectValue placeholder='THỨ TỰ' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='asc'>Tăng dần</SelectItem>
              <SelectItem value='desc'>Giảm dần</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className='ml-2'>
          <Button onClick={handleFilter} className='h-10 px-6 bg-blue-600 text-white hover:bg-blue-700'>
            Lọc
          </Button>
        </div>
      </div>

      {/* Actions */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium'>SỐ LƯỢNG: {getItemCount()}</span>
            <button className='text-blue-600 text-sm hover:underline'>
              Sắp xếp thứ tự hiển thị
            </button>
          </div>
        </div>

        <ItemsActionBar
          onSyncItems={handleSyncItems}
          onViewPreview={handleViewPreview}
          onCreateItem={handleCreateItem}
        />
      </div>
    </div>
  )
}
