import React from 'react'

import { usePosStores } from '@/stores'

import { usePosData } from '@/hooks/use-pos-data'

import { Card } from '@/components/ui/card'
import { Toolt<PERSON>, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Button, Badge } from '@/components/ui'

import { usePromotionContext } from '../context'
import { InvoicePromotionModal } from './invoice-promotion-modal'
import { PromotionPickerDialog } from './promotion-picker-dialog'

const fmt = (n: number) => new Intl.NumberFormat('vi-VN').format(n)

export const PromotionTable: React.FC = () => {
  const {
    tableRows,
    isLoading,
    compareCities,
    compareStores,
    compareData,
    selectedStoreIds,
    compareStoreIds,
    finalStoreIds,
    startDate,
    endDate
  } = usePromotionContext()
  const { currentBrandStores } = usePosStores()
  const { company, activeBrands } = usePosData()

  const [pickerOpen, setPickerOpen] = React.useState(false)
  const [selectedPromotionIds, setSelectedPromotionIds] = React.useState<string[]>([])
  const [invoiceModalOpen, setInvoiceModalOpen] = React.useState(false)
  const [selectedPromotion, setSelectedPromotion] = React.useState<{
    promotion_id: string
    promotion_name: string
  } | null>(null)

  const visibleRows = React.useMemo(() => {
    if (selectedPromotionIds.length === 0) return tableRows
    return tableRows.filter(row => selectedPromotionIds.includes(row.promotion_id))
  }, [tableRows, selectedPromotionIds])

  const hasCompareData = compareCities?.length > 0 || compareStores?.length > 0

  const handleRowClick = (promotion: { promotion_id: string; promotion_name: string }) => {
    setSelectedPromotion(promotion)
    setInvoiceModalOpen(true)
  }

  const compareTableRows = React.useMemo(() => {
    if (!compareData || !hasCompareData) return []

    const compareMap = new Map()
    compareData.forEach((item: any) => {
      if (item.list_data) {
        item.list_data.forEach((promo: any) => {
          const key = promo.promotion_id || promo.promotion_name
          if (key) {
            compareMap.set(key, {
              promotion_id: key,
              promotion_name: promo.promotion_name || item.promotion_name,
              total_bill: promo.total_bill || 0,
              revenue_net: promo.revenue_net || 0,
              revenue_gross: promo.revenue_gross || 0,
              commission_amount: promo.commission_amount || 0,
              discount_amount: promo.discount_amount || 0,
              deduct_tax_amount: promo.deduct_tax_amount || 0
            })
          }
        })
      }
    })

    return visibleRows.map(row => {
      const compareRow = compareMap.get(row.promotion_id) || compareMap.get(row.promotion_name)
      return (
        compareRow || {
          promotion_id: row.promotion_id,
          promotion_name: row.promotion_name,
          total_bill: 0,
          revenue_net: 0,
          revenue_gross: 0,
          commission_amount: 0,
          discount_amount: 0,
          deduct_tax_amount: 0
        }
      )
    })
  }, [compareData, hasCompareData, visibleRows])

  if (isLoading) {
    return (
      <Card>
        <div className='text-muted-foreground flex h-32 items-center justify-center'>Đang tải dữ liệu...</div>
      </Card>
    )
  }

  const getMainStoreDisplayName = () => {
    if (selectedStoreIds.length === 0) {
      return 'Tất cả cửa hàng'
    } else if (selectedStoreIds.length === 1) {
      const store = currentBrandStores.find(s => s.id === selectedStoreIds[0] || s.store_id === selectedStoreIds[0])
      return store ? store.store_name : 'Cửa hàng đã chọn'
    } else {
      return `${selectedStoreIds.length} cửa hàng`
    }
  }

  const renderStoresHeader = (ids: string[]) => {
    const stores = ids
      .map(id => currentBrandStores.find(s => (s.id || s.store_id) === id))
      .filter(Boolean) as Array<any>
    const firstStoreName = stores[0]?.store_name || 'Tất cả cửa hàng'
    const extraCount = Math.max(0, stores.length - 1)

    return (
      <div className='flex items-center justify-center gap-2'>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant='outline' className='cursor-default'>
              {firstStoreName}
            </Badge>
          </TooltipTrigger>
          {stores.length > 0 && (
            <TooltipContent side='bottom'>
              <div className='max-h-60 max-w-[260px] overflow-auto pr-1'>
                {stores.map(s => (
                  <div key={s.id || s.store_id} className='whitespace-nowrap'>
                    {s.store_name}
                  </div>
                ))}
              </div>
            </TooltipContent>
          )}
        </Tooltip>
        {extraCount > 0 && <Badge variant='secondary'>+{extraCount}</Badge>}
      </div>
    )
  }

  const getCompareStoreDisplayName = () => {
    if (!hasCompareData || compareStoreIds.length === 0) return ''

    if (compareStoreIds.length === 1) {
      const store = currentBrandStores.find(s => s.id === compareStoreIds[0] || s.store_id === compareStoreIds[0])
      return store ? store.store_name : 'Cửa hàng so sánh'
    } else {
      return `${compareStoreIds.length} cửa hàng`
    }
  }

  return (
    <>
      <div className='mb-3 flex items-center gap-2'>
        <Button size='sm' onClick={() => setPickerOpen(true)}>
          Chọn CTKM
        </Button>
      </div>
      <PromotionPickerDialog
        open={pickerOpen}
        onOpenChange={setPickerOpen}
        selectedIds={selectedPromotionIds}
        onSelectedChange={setSelectedPromotionIds}
      />

      {hasCompareData ? (
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='w-10' rowSpan={2}>
                  #
                </TableHead>
                <TableHead rowSpan={2}>CTKM</TableHead>

                <TableHead className='border-r-2 border-b-2 text-center' colSpan={6}>
                  {selectedStoreIds.length > 0 ? renderStoresHeader(selectedStoreIds) : getMainStoreDisplayName()}
                </TableHead>

                <TableHead className='border-b-2 text-center' colSpan={6}>
                  {compareStoreIds.length > 0 ? renderStoresHeader(compareStoreIds) : getCompareStoreDisplayName()}
                </TableHead>
              </TableRow>

              <TableRow>
                <TableHead className='text-right'>Tổng hoá đơn</TableHead>
                <TableHead className='text-right'>Tổng doanh thu (net)</TableHead>
                <TableHead className='text-right'>Tổng doanh thu (gross)</TableHead>
                <TableHead className='text-right'>Hoa hồng</TableHead>
                <TableHead className='text-right'>Tổng giảm giá</TableHead>
                <TableHead className='border-r-2 text-right'>Thuế khấu trừ</TableHead>

                <TableHead className='border-l-2 text-right'>Tổng hoá đơn</TableHead>
                <TableHead className='text-right'>Tổng doanh thu (net)</TableHead>
                <TableHead className='text-right'>Tổng doanh thu (gross)</TableHead>
                <TableHead className='text-right'>Hoa hồng</TableHead>
                <TableHead className='text-right'>Tổng giảm giá</TableHead>
                <TableHead className='text-right'>Thuế khấu trừ</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {visibleRows.map((row, idx) => {
                const compareRow = compareTableRows[idx]
                return (
                  <TableRow
                    key={row.promotion_name + idx}
                    className='hover:bg-muted/50 cursor-pointer'
                    onClick={() => handleRowClick(row)}
                  >
                    <TableCell>{idx + 1}</TableCell>
                    <TableCell>{row.promotion_name}</TableCell>

                    <TableCell className='text-right font-mono'>{fmt(row.total_bill)}</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(row.revenue_net)} đ</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(row.revenue_gross)} đ</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(row.commission_amount)} đ</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(row.discount_amount)} đ</TableCell>
                    <TableCell className='border-r-2 text-right font-mono'>{fmt(row.deduct_tax_amount)} đ</TableCell>

                    <TableCell className='border-l-2 text-right font-mono'>{fmt(compareRow.total_bill)}</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(compareRow.revenue_net)} đ</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(compareRow.revenue_gross)} đ</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(compareRow.commission_amount)} đ</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(compareRow.discount_amount)} đ</TableCell>
                    <TableCell className='text-right font-mono'>{fmt(compareRow.deduct_tax_amount)} đ</TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
          {visibleRows.length === 0 && (
            <div className='text-muted-foreground flex h-24 items-center justify-center'>Không có dữ liệu</div>
          )}
        </div>
      ) : (
        <div className='rounded-md border'>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className='w-10'>#</TableHead>
                <TableHead>CTKM</TableHead>
                <TableHead className='text-right'>Tổng hoá đơn</TableHead>
                <TableHead className='text-right'>Tổng doanh thu (net)</TableHead>
                <TableHead className='text-right'>Tổng doanh thu (gross)</TableHead>
                <TableHead className='text-right'>Hoa hồng</TableHead>
                <TableHead className='text-right'>Tổng giảm giá</TableHead>
                <TableHead className='text-right'>Thuế khấu trừ</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {visibleRows.map((row, idx) => (
                <TableRow
                  key={row.promotion_name + idx}
                  className='hover:bg-muted/50 cursor-pointer'
                  onClick={() => handleRowClick(row)}
                >
                  <TableCell>{idx + 1}</TableCell>
                  <TableCell>{row.promotion_name}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.total_bill)}</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.revenue_net)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.revenue_gross)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.commission_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.discount_amount)} đ</TableCell>
                  <TableCell className='text-right font-mono'>{fmt(row.deduct_tax_amount)} đ</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {visibleRows.length === 0 && (
            <div className='text-muted-foreground flex h-24 items-center justify-center'>Không có dữ liệu</div>
          )}
        </div>
      )}

      <InvoicePromotionModal
        open={invoiceModalOpen}
        onOpenChange={setInvoiceModalOpen}
        promotionId={selectedPromotion?.promotion_id}
        promotionName={selectedPromotion?.promotion_name}
        companyUid={company?.id}
        brandUid={activeBrands[0]?.id}
        startDate={startDate}
        endDate={endDate}
        storeUids={finalStoreIds}
      />
    </>
  )
}
