import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import type { MenuItem } from '../types/menu-schedule-api'

interface MenuItemActionSelectorProps {
  selectedAction: string
  onActionChange: (action: string) => void
  selectedItem?: MenuItem | null
}

const getActionDisplayText = (action: string): string => {
  switch (action) {
    case 'CREATE':
      return 'Tạo món'
    case 'UPDATE':
      return 'Sửa món'
    case 'DELETE':
      return 'Xóa món'
    default:
      return action
  }
}

export function MenuItemActionSelector({
  selectedAction,
  onActionChange,
  selectedItem
}: MenuItemActionSelectorProps) {
  return (
    <div className='flex items-center gap-4'>
      <Label className='w-40 rounded-sm bg-gray-100 px-3 py-2 text-sm font-medium whitespace-nowrap text-gray-700'>
        Thao tác
      </Label>
      {selectedItem ? (
        <div className='flex-1 rounded-md border bg-gray-50 px-3 py-2 text-sm text-gray-700'>
          {getActionDisplayText(selectedItem.action)}
        </div>
      ) : (
        <Select value={selectedAction} onValueChange={onActionChange}>
          <SelectTrigger className='w-full'>
            <SelectValue placeholder='Chọn thao tác' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='CREATE'>Tạo món</SelectItem>
            <SelectItem value='UPDATE'>Sửa món</SelectItem>
            <SelectItem value='DELETE'>Xóa món</SelectItem>
          </SelectContent>
        </Select>
      )}
    </div>
  )
}
