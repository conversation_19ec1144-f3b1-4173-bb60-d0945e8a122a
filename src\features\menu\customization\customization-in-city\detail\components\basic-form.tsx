import { usePosData } from '@/hooks/use-pos-data'

import { Label, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Button, Input } from '@/components/ui'

import { useFormContext } from '../form-context'

interface BasicFormProps {
  customizationId?: string
  isLoadingItems: boolean
  handleCreateGroup: () => void
  handleEditGroup: (groupId: string) => void
}

export function BasicForm({ customizationId, isLoadingItems, handleCreateGroup, handleEditGroup }: BasicFormProps) {
  const { customizationForm, groupManagement, dishSelection, modalState } = useFormContext()
  const { cities } = usePosData()

  return (
    <div className='mx-auto max-w-4xl'>
      <div className='p-6'>
        <div className='space-y-6'>
          <div className='flex items-center gap-4'>
            <Label htmlFor='customization-name' className='min-w-[200px] text-sm font-medium'>
              Tên customization <span className='text-red-500'>*</span>
            </Label>
            <Input
              id='customization-name'
              value={customizationForm.customizationName}
              onChange={e => customizationForm.setCustomizationName(e.target.value)}
              placeholder='Nhập tên customization'
              className='flex-1'
            />
          </div>

          <div className='flex items-center gap-4'>
            <Label htmlFor='city-select' className='min-w-[200px] text-sm font-medium'>
              Thành phố <span className='text-red-500'>*</span>
            </Label>
            <Select
              value={customizationForm.selectedCityId}
              onValueChange={customizationForm.setSelectedCityId}
              disabled={!!customizationId}
            >
              <SelectTrigger className='flex-1'>
                <SelectValue placeholder='Chọn thành phố' />
              </SelectTrigger>
              <SelectContent>
                {cities.map(city => (
                  <SelectItem key={city.id} value={city.id}>
                    {city.city_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {customizationForm.selectedCityId && (
            <div className='space-y-4 pt-6'>
              <h3 className='text-lg font-medium'>Áp dụng customization cho món</h3>
              <div
                className={`cursor-pointer rounded-md border p-4 hover:bg-gray-50 ${
                  isLoadingItems ? 'cursor-not-allowed opacity-50' : ''
                }`}
                onClick={() => modalState.handleOpenDishModal(isLoadingItems)}
              >
                <div className='flex items-center justify-between'>
                  <span className='font-medium'>Món ăn</span>
                  <span className='text-sm text-gray-500'>
                    {isLoadingItems ? 'Đang tải...' : `${dishSelection.selectedDishesCount} món áp dụng`}
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className='flex justify-center pt-4'>
            <Button onClick={handleCreateGroup}>Tạo nhóm</Button>
          </div>

          {groupManagement.customizationGroups.length > 0 && (
            <div className='space-y-6 pt-6'>
              <h3 className='text-lg font-medium'>Danh sách nhóm đã tạo</h3>

              {groupManagement.customizationGroups.map(group => (
                <div key={group.id} className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <div>
                      <span className='font-medium'>{group.name}</span>
                      <span className='ml-2 text-sm text-gray-500'>
                        (Chọn từ {group.minRequired} đến {group.maxAllowed} món)
                      </span>
                    </div>
                    <div className='flex gap-2'>
                      <Button variant='outline' size='sm' onClick={() => handleEditGroup(group.id)}>
                        Sửa
                      </Button>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => groupManagement.handleDeleteGroup(group.id)}
                        className='text-red-600 hover:text-red-700'
                      >
                        Xóa
                      </Button>
                    </div>
                  </div>

                  <div className='grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4'>
                    {group.items.map(item => {
                      const isActive = (item as any).active === 1
                      return (
                        <div
                          key={item.id}
                          className={`rounded-md border p-3 text-center ${
                            isActive ? 'bg-gray-50' : 'cursor-not-allowed bg-gray-100 opacity-50'
                          }`}
                        >
                          <p className={`text-sm font-medium ${isActive ? '' : 'text-gray-400'}`}>{item.name}</p>
                          <p className='mt-1 text-xs text-gray-500'>({item.code})</p>
                          <p className={`mt-1 text-sm font-medium ${isActive ? 'text-green-600' : 'text-gray-400'}`}>
                            {item.price.toLocaleString('vi-VN', {
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0
                            })}{' '}
                            ₫
                          </p>
                        </div>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
