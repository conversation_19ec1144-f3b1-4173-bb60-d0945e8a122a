import { useState, useMemo } from 'react'

import { Download, ChevronUp, ChevronDown } from 'lucide-react'

import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, Button, Input } from '@/components/ui'

import { useAllInvoicePages, type InvoiceEntry } from '../hooks'
import { OrderLogModal } from './order-log-modal'

interface InvoiceSourceModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  sourceName?: string
  sourceId?: string
  companyUid?: string
  brandUid?: string
  startDate?: number
  endDate?: number
  storeUids?: string[]
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('vi-VN').format(amount) + ' ₫'
}

export function InvoiceSourceModal({
  open,
  onOpenChange,
  sourceName,
  sourceId,
  companyUid,
  brandUid,
  startDate,
  endDate,
  storeUids
}: InvoiceSourceModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [showOrderLogModal, setShowOrderLogModal] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)

  const shouldFetchApi = open && !!sourceId

  const fallbackStartDate = useMemo(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return today.getTime()
  }, [])

  const fallbackEndDate = useMemo(() => {
    const today = new Date()
    today.setHours(23, 59, 59, 999)
    return today.getTime()
  }, [])

  const { invoices, isLoading, error, totalInvoices } = useAllInvoicePages({
    companyUid,
    brandUid,
    startDate,
    endDate,
    sourceId,
    storeUids,
    fallbackStartDate,
    fallbackEndDate,
    shouldFetchApi
  })

  const filteredInvoices = useMemo(() => {
    return invoices.filter((invoice: InvoiceEntry) => {
      const searchLower = searchTerm.toLowerCase()
      return (
        invoice.invoiceCode.toLowerCase().includes(searchLower) ||
        invoice.partnerInvoiceNumber.toLowerCase().includes(searchLower)
      )
    })
  }, [invoices, searchTerm])

  const handleExportInvoice = () => {
    // TODO: Implement export functionality
  }

  const toggleItem = (invoiceId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(invoiceId)) {
        newSet.delete(invoiceId)
      } else {
        newSet.add(invoiceId)
      }
      return newSet
    })
  }

  const InvoiceItem = ({ invoice }: { invoice: InvoiceEntry }) => {
    const isExpanded = expandedItems.has(invoice.id)

    const handleItemClick = () => {
      toggleItem(invoice.id)
    }

    return (
      <div className='cursor-pointer rounded-lg border p-4 hover:bg-gray-50' onClick={handleItemClick}>
        <div className='flex items-start justify-between'>
          <div className='flex-1'>
            <div className='mb-1 flex items-center gap-2'>
              <span className='font-bold'>#{invoice.paymentReference?.slice(-5) || 'N/A'}</span>
              <span className='text-sm text-gray-500'>-</span>
              <span className='font-bold'>SỐ HĐ: {invoice.invoiceCode}</span>
            </div>
            <div className='text-sm text-gray-600'>Số hoá đơn đối tác: {invoice.partnerInvoiceNumber}</div>
            <div className='text-sm text-gray-600'>
              {invoice.branchName} - Kênh {invoice.sourceType} ({invoice.partnerInvoiceNumber}) -{' '}
              {formatCurrency(invoice.amount)}
            </div>
          </div>
          <div className='text-right'>
            <div className='flex items-center gap-2'>
              <div className='text-sm text-gray-600'>{invoice.dateTime}</div>
              <div className='flex h-6 w-6 items-center justify-center'>
                {isExpanded ? <ChevronUp className='h-4 w-4' /> : <ChevronDown className='h-4 w-4' />}
              </div>
            </div>
            <Button
              variant='link'
              className='h-auto p-0 text-sm text-blue-600'
              onClick={e => {
                e.stopPropagation()
                setSelectedInvoice(invoice)
                setShowOrderLogModal(true)
              }}
            >
              Xem nhật ký order
            </Button>
          </div>
        </div>

        {/* Collapsible Order Details */}
        {isExpanded && (
          <div className='mt-4 space-y-3 border-t pt-4'>
            {invoice.items && invoice.items.length > 0 ? (
              <div className='space-y-2'>
                {invoice.items.map((item, index) => (
                  <div key={index} className='flex justify-between text-sm'>
                    <span className='flex-1'>
                      <span className='text-gray-600'>(x{item.quantity})</span> {item.name}
                    </span>
                    <span className='font-mono'>{formatCurrency(item.price)}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className='text-sm text-gray-500 italic'>Không có thông tin chi tiết món hàng</div>
            )}

            {/* Order Summary */}
            <div className='space-y-2 border-t pt-3'>
              <div className='flex justify-between text-sm'>
                <span>Thành tiền:</span>
                <span className='font-mono'>{formatCurrency(invoice.amount)}</span>
              </div>
              <div className='flex justify-between text-base font-semibold'>
                <span>Tổng tiền:</span>
                <span className='font-mono'>{formatCurrency(invoice.amount)}</span>
              </div>
              <div className='flex justify-between text-sm'>
                <span>Phương thức thanh toán:</span>
                <span className='text-right'>
                  {invoice.paymentMethod}
                  {invoice.paymentReference && (
                    <div className='text-xs text-gray-500'>({invoice.paymentReference})</div>
                  )}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-2xl'>
        <DialogHeader>
          <DialogTitle className='flex items-center justify-center'>
            <span>Hoá đơn áp dụng nguồn {sourceName}</span>
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-4'>
          {/* Search Bar */}
          <div className='flex gap-4'>
            <div className='flex-1'>
              <Input
                placeholder='Tìm kiếm theo mã hoá đơn hoặc số hoá đơn đối tác'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Invoice List */}
          <div className='max-h-96 overflow-y-auto'>
            {isLoading && totalInvoices === 0 ? (
              <div className='py-8 text-center text-gray-500'>Đang tải dữ liệu...</div>
            ) : error ? (
              <div className='py-8 text-center text-red-500'>
                <div className='font-medium'>Có lỗi xảy ra khi tải dữ liệu:</div>
                <details className='mt-2 text-xs'>
                  <summary className='cursor-pointer'>Chi tiết lỗi</summary>
                  <pre className='mt-1 max-h-32 overflow-auto text-left'>{JSON.stringify(error, null, 2)}</pre>
                </details>
              </div>
            ) : filteredInvoices.length === 0 ? (
              <div className='py-8 text-center text-gray-500'>
                <div className='font-medium'>Không tìm thấy hóa đơn nào</div>
              </div>
            ) : (
              <div className='space-y-3'>
                {filteredInvoices.map(invoice => (
                  <InvoiceItem key={invoice.id} invoice={invoice} />
                ))}
              </div>
            )}
          </div>

          {/* Export Button */}
          <div className='flex justify-end border-t pt-4'>
            <Button onClick={handleExportInvoice} className='flex items-center gap-2'>
              <Download className='h-4 w-4' />
              Xuất hoá đơn
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Order Log Modal */}
      <OrderLogModal open={showOrderLogModal} onOpenChange={setShowOrderLogModal} invoice={selectedInvoice} />
    </Dialog>
  )
}
