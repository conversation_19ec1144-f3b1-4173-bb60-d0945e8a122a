import { useQuery } from '@tanstack/react-query'

import { useAuthStore } from '@/stores/authStore'

import { QUERY_KEYS } from '@/constants/query-keys'

import { useItemTypesData, useItemClassesData, useUnitsData } from '@/hooks/api'

import type { ItemsInStoreTable } from '../data'
import { itemsInStoreApiService } from './items-in-store-api'
import type { ItemInStore, GetItemByListIdParams } from './items-in-store-api'

export interface GetItemsInStoreParams {
  company_uid: string
  brand_uid: string
  store_uid?: string
  list_store_uid?: string
  page?: number
  reverse?: number
  skip_limit?: boolean
  active?: string
  apply_with_store?: string | number
  limit?: number
}

export interface UseItemsInStoreDataOptions {
  params?: Partial<GetItemsInStoreParams>
  enabled?: boolean
}

export const useItemsInStoreData = (options: UseItemsInStoreDataOptions = {}) => {
  const { params = {}, enabled = true } = options
  const { company, brands } = useAuthStore(state => state.auth)

  const selectedBrand = brands?.[0]

  const dynamicParams: GetItemsInStoreParams = {
    company_uid: company?.id || '',
    brand_uid: selectedBrand?.id || '',
    page: 1,
    reverse: 1,
    skip_limit: true, // Default to get all items
    ...params
  }

  const hasRequiredAuth = !!(company?.id && selectedBrand?.id)

  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST, JSON.stringify(dynamicParams)],
    queryFn: async (): Promise<ItemInStore[]> => {
      const response = await itemsInStoreApiService.getItemsInStore(dynamicParams)
      return response || []
    },
    enabled: enabled && hasRequiredAuth,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000 // 10 minutes
  })
}

export const useItemInStoreDetail = (id?: string, enabled = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_STORE_DETAIL, id],
    queryFn: () => itemsInStoreApiService.getItemInStoreDetail({ item_uid: id! }),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000
  })
}

export const useItemByListId = (params: GetItemByListIdParams, enabled = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.ITEMS_IN_STORE_LIST, 'by_list_id', params],
    queryFn: () => itemsInStoreApiService.getItemByListId(params),
    enabled: enabled && !!params.list_id,
    staleTime: 5 * 60 * 1000
  })
}

export const useItemsInStoreForTable = (options: UseItemsInStoreDataOptions = {}) => {
  const itemsQuery = useItemsInStoreData(options)
  const storeUid = options.params?.store_uid

  const { data: itemTypesData = [] } = useItemTypesData({
    skip_limit: true,
    ...(storeUid && storeUid !== 'all' ? { store_uid: storeUid } : {})
  })
  const { data: itemClassesData = [] } = useItemClassesData({ skip_limit: true })
  const { data: unitsData = [] } = useUnitsData()

  const transformedData =
    itemsQuery.data?.map(item => {
      const itemData = item as unknown as Record<string, unknown>
      const stores = (itemData.stores as Array<{ store_name: string }>) || []
      const storeName = stores[0]?.store_name || ''

      const itemType = itemData.item_type_uid ? itemTypesData.find(type => type.id === itemData.item_type_uid) : null
      const itemTypeName = itemType?.item_type_name || ''

      const itemClass = itemData.item_class_uid ? itemClassesData.find(cls => cls.id === itemData.item_class_uid) : null
      const itemClassName = itemClass?.item_class_name || ''

      const unit = itemData.unit_uid ? unitsData.find(u => u.id === itemData.unit_uid) : null
      const unitName = unit?.unit_name || ''

      const hasEatWith = itemData.is_eat_with === 1 || (itemData.item_id_eat_with && itemData.item_id_eat_with !== '')
      const sideItemsValue = hasEatWith ? (itemData.item_id_eat_with as string) || 'Món ăn kèm' : ''

      // Get city name from city_uid
      const cityUid = (itemData.city_uid as string) || ''
      let cityName = 'Không xác định'

      if (cityUid) {
        try {
          const citiesData = localStorage.getItem('pos_cities_data')
          if (citiesData) {
            const cities = JSON.parse(citiesData)
            if (Array.isArray(cities)) {
              const city = cities.find((c: any) => c.id === cityUid)
              cityName = city?.city_name || cityUid
            }
          }
        } catch (error) {
          cityName = cityUid
        }
      }

      return {
        id: item.id || '',
        code: (itemData.item_id as string) || '',
        name: item.item_name || 'Chưa có tên',
        price: item.ots_price || 0,
        vatPercent: item.ots_tax || 0,
        cookingTime: item.time_cooking || 0,
        categoryGroup: itemTypeName || 'Chưa phân loại',
        itemType: itemTypeName || 'Chưa phân loại',
        itemClass: itemClassName || 'Chưa phân loại',
        unit: unitName || 'Chưa có đơn vị',
        sideItems: sideItemsValue || 'Món chính',
        store: storeName || 'Cửa hàng',
        city: cityName, // Add city name here
        buffetConfig: (itemData.extra_data as Record<string, unknown>)?.is_buffet_item
          ? 'Đã cấu hình'
          : 'Chưa cấu hình',
        customization: (itemData.customization_uid as string) || '',
        isActive: Boolean(item.active),
        createdAt: new Date(
          typeof item.created_at === 'number' ? item.created_at * 1000 : new Date(item.created_at).getTime()
        ),
        // Add missing fields for better display
        imageUrl: (itemData.image_path as string) || undefined,
        imageThumb: (itemData.image_path_thumb as string) || undefined,
        description: (itemData.description as string) || undefined,
        originalData: {
          item_id: (itemData.item_id as string) || '',
          item_code: (itemData.item_id as string) || '',
          item_name: item.item_name || 'Chưa có tên',
          price: item.ots_price || 0,
          vat_percent: item.ots_tax || 0,
          category_group: itemTypeName || 'Chưa phân loại',
          item_type: itemTypeName || 'Chưa phân loại',
          unit: unitName || 'Chưa có đơn vị',
          side_items: sideItemsValue || 'Món chính',
          store_name: storeName || 'Cửa hàng',
          buffet_config: (itemData.extra_data as Record<string, unknown>)?.is_buffet_item
            ? 'Đã cấu hình'
            : 'Chưa cấu hình',
          customization_uid: (itemData.customization_uid as string) || '',
          active: item.active ? 1 : 0,
          created_at: typeof item.created_at === 'number' ? item.created_at : 0,
          store_uid: (itemData.store_uid as string) || '',
          city_uid: cityUid,
          city_name: cityName,
          apply_with_store: (itemData.apply_with_store as number) || 0,
          cities: (itemData.cities as Array<any>) || []
        }
      } as unknown as ItemsInStoreTable
    }) || []

  return {
    ...itemsQuery,
    data: transformedData
  }
}
