import React from 'react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'

interface ConfirmModalProps {
  /** Controls modal visibility */
  open: boolean
  /** Handles modal open/close state */
  onOpenChange: (open: boolean) => void
  /** Optional custom title (defaults to empty string) */
  title?: string
  /** Optional custom content (defaults to "Bạn có muốn xóa") */
  content?: string
  /** Callback when user clicks confirm/delete button */
  onConfirm: () => void
  /** Optional callback when user clicks cancel (defaults to closing modal) */
  onCancel?: () => void
  /** Optional custom confirm button text (defaults to "Xóa") */
  confirmText?: string
  /** Optional custom cancel button text (defaults to "Hủy") */
  cancelText?: string
  /** Optional loading state for the confirm button */
  isLoading?: boolean
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  open,
  onOpenChange,
  title = '',
  content = 'Bạn có muốn xóa',
  onConfirm,
  onCancel,
  confirmText = 'Xóa',
  cancelText = 'Hủy',
  isLoading = false
}) => {
  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    } else {
      onOpenChange(false)
    }
  }

  const handleConfirm = () => {
    onConfirm()
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[400px]'>
        {title && (
          <DialogHeader>
            <DialogTitle className='text-lg font-medium'>{title}</DialogTitle>
          </DialogHeader>
        )}

        <div className={`py-4 ${!title ? 'pt-6' : ''}`}>
          <p className={`${!title ? 'text-center text-base' : 'text-sm'} text-gray-600`}>{content}</p>
        </div>

        <DialogFooter>
          <div className='flex w-full justify-between'>
            <Button type='button' variant='outline' onClick={handleCancel} disabled={isLoading}>
              {cancelText}
            </Button>
            <Button variant='destructive' onClick={handleConfirm} disabled={isLoading}>
              {isLoading ? 'Đang xử lý...' : confirmText}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
