import { useEffect, useState, useRef } from 'react'

import { z } from 'zod'

import { useForm } from 'react-hook-form'

import { zodResolver } from '@hookform/resolvers/zod'

import { Upload, X, Trash2 } from 'lucide-react'
import { toast } from 'sonner'

import { useCurrentBrand, useCurrentCompany } from '@/stores/posStore'

import { formatNumberDisplay, handleNumberInputChange } from '@/lib/utils'
import {
  formatDate,
  formatSelectedDays,
  formatSelectedHours,
  convertBitFlagsToDays,
  convertBitFlagsToHours,
  convertTimestampToDate
} from '@/utils/date-utils'

import { useCreateCombo, useComboDetailByPackageUid, useUpdateCombo } from '@/hooks/api/use-combos'
import { useImageUpload } from '@/hooks/api/use-images'
import { useItemTypesData } from '@/hooks/api/use-item-types'
import { useItemsData } from '@/hooks/api/use-items'
import { usePromotionsData } from '@/hooks/api/use-promotions'
import { useSourcesData } from '@/hooks/api/use-sources'
import { useStoresData } from '@/hooks/api/use-stores'

import { DatePicker } from '@/components/ui/date/date-picker'

import {
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Checkbox,
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui'

import { GroupCreateDialog } from './group-create-dialog'
import { PriceSourceConfigDialog } from './price-source-config-dialog'
import { StoreSelectionDialog } from './store-selection-dialog'
import { TimeFrameConfigDialog } from './time-frame-config-dialog'

const convertBitmaskToArray = (bitmask: number, length: number): boolean[] => {
  const result = new Array(length).fill(false)
  for (let i = 0; i < length; i++) {
    result[i] = (bitmask & (1 << i)) !== 0
  }
  return result
}

const convertBitmaskToHours = (bitmask: number): Record<string, boolean> => {
  const hours: Record<string, boolean> = {}
  for (let i = 0; i < 24; i++) {
    hours[i.toString()] = (bitmask & (1 << i)) !== 0
  }
  return hours
}

const convertPriceTimesToTimeFrameConfigs = (price_times: any[]): Array<{
  id: string
  amount: string
  startDate: string
  endDate: string
  selectedDays: boolean[]
  selectedHours: Record<string, boolean>
}> => {
  return price_times.map((priceTime, index) => {
    const dayNumbers = convertBitFlagsToDays(priceTime.time_sale_date_week || 0)
    const hourNumbers = convertBitFlagsToHours(priceTime.time_sale_hour_day || 0)

    const selectedDays = Array(7).fill(false)
    dayNumbers.forEach(dayNum => {
      if (dayNum >= 0 && dayNum < 7) {
        selectedDays[dayNum] = true
      }
    })

    const selectedHours: Record<string, boolean> = {}
    for (let i = 0; i < 24; i++) {
      selectedHours[i.toString()] = hourNumbers.includes(i)
    }

    return {
      id: `timeframe-${index}`,
      amount: priceTime.price.toString(),
      startDate: convertTimestampToDate(priceTime.from_date),
      endDate: convertTimestampToDate(priceTime.to_date),
      selectedDays,
      selectedHours
    }
  })
}

const comboFormSchema = z.object({
  comboName: z.string().min(1, 'Tên combo là bắt buộc'),
  price: z.string().min(1, 'Giá là bắt buộc'),
  description: z.string().default(''),
  storeSelection: z.string().default(''),
  ctrmType: z.string().default(''),
  promotionId: z.string().optional(),
  groupSelection: z.string().default(''),
  comboCode: z.string().default(''),
  vat: z.string().default('0'),
  startDate: z.string().min(1, 'Ngày bắt đầu là bắt buộc'),
  endDate: z.string().min(1, 'Ngày kết thúc là bắt buộc'),
  displayOrder: z.string().default(''),
  image: z.any().optional(),
  selectedDays: z.array(z.boolean()).default([false, false, false, false, false, false, false]),
  selectedHours: z.record(z.string(), z.boolean()).default({})
})

type ComboFormData = z.infer<typeof comboFormSchema>

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  editingComboId?: string
}

export function CreateComboDialog({ open, onOpenChange, editingComboId }: Props) {
  const { selectedBrand } = useCurrentBrand()
  const { companyUid } = useCurrentCompany()

  const [storeDialogOpen, setStoreDialogOpen] = useState(false)
  const [selectedStoreIds, setSelectedStoreIds] = useState<string[]>([])

  const hasMappedRef = useRef<string | null>(null)

  const [priceSourceDialogOpen, setPriceSourceDialogOpen] = useState(false)
  const [editingPriceSourceIndex, setEditingPriceSourceIndex] = useState<number>(-1)
  const [timeFrameConfigDialogOpen, setTimeFrameConfigDialogOpen] = useState(false)
  const [selectedSourceName, setSelectedSourceName] = useState<string>('')
  const [currentTimeFrameOnSave, setCurrentTimeFrameOnSave] = useState<((data: any) => void) | null>(null)
  const [editingTimeFrameData, setEditingTimeFrameData] = useState<any>(null)
  const [groupDialogOpen, setGroupDialogOpen] = useState(false)
  const [groupDialogMode, setGroupDialogMode] = useState<'create' | 'edit'>('create')
  const [editingGroupId, setEditingGroupId] = useState<string | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null)
  const [enableComboCodeInput, setEnableComboCodeInput] = useState<boolean>(false)
  const [groups, setGroups] = useState<
    Array<{
      id: string;
      name: string;
      required: number;
      max: number;
      itemIds: string[];
      itemSettings?: Record<string, { finalPrice: number; discountPercent: number; pricingMode: 'custom' | 'base' }>
    }>
  >([])
  const [priceSourceConfigs, setPriceSourceConfigs] = useState<
    Array<{
      orderSource: string
      amount: string
      comboCode?: string
      autoGenerateCode: boolean
      timeFrameConfigs?: Array<{
        id: string
        amount: string
        startDate: string
        endDate: string
        selectedDays: boolean[]
        selectedHours: Record<string, boolean>
      }>
      price_times?: Array<{
        price: number
        from_date: number
        to_date: number
        time_sale_date_week: number
        time_sale_hour_day: number
      }>
    }>
  >([])

  const { data: sourceOptions = [] } = useSourcesData({
    enabled: open && selectedStoreIds.length > 0,
    skip_limit: true,
    list_store_uid: selectedStoreIds.length > 0 ? selectedStoreIds : undefined
  })

  const { promotions } = usePromotionsData({
    enabled: open && !!companyUid && !!selectedBrand?.id && selectedStoreIds.length > 0,
    params: {
      skip_limit: true,
      company_uid: companyUid!,
      brand_uid: selectedBrand?.id!,
      list_store_uid: selectedStoreIds.join(','),
      partner_auto_gen: 0,
      active: 1
    }
  })

  const { data: itemTypes = [] } = useItemTypesData({
    enabled: open && !!companyUid && !!selectedBrand?.id && selectedStoreIds.length > 0,
    company_uid: companyUid!,
    brand_uid: selectedBrand?.id!,
    store_uid: selectedStoreIds[0],
    skip_limit: true,
    active: 1
  })

  const { data: items = [] } = useItemsData({
    enabled: open && !!companyUid && !!selectedBrand?.id && selectedStoreIds.length > 0,
    params: {
      company_uid: companyUid!,
      brand_uid: selectedBrand?.id!,
      list_store_uid: selectedStoreIds.join(','),
      skip_limit: true,
      active: 1
    }
  })

  const { data: allStores = [], isLoading: storesLoading, error: storesError } = useStoresData()
  const stores = allStores.filter(store => store.isActive)

  const { mutate: createCombo, isPending: isCreating } = useCreateCombo()
  const { mutate: updateCombo, isPending: isUpdating } = useUpdateCombo()
  const { mutate: uploadImage, isPending: isUploadingImage } = useImageUpload()

  const { data: comboDetail, isLoading: isLoadingComboDetail } = useComboDetailByPackageUid(editingComboId || '', !!editingComboId && open)



  const form = useForm({
    resolver: zodResolver(comboFormSchema),
    defaultValues: {
      comboName: '',
      price: '',
      description: '',
      storeSelection: '',
      ctrmType: '',
      promotionId: '',
      groupSelection: '',
      comboCode: '',
      vat: '0',
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      displayOrder: '',
      image: undefined,
      selectedDays: [false, false, false, false, false, false, false],
      selectedHours: {}
    }
  })

  useEffect(() => {
    if (open) {
      const today = new Date().toISOString().split('T')[0]
      form.reset({
        comboName: '',
        price: '',
        description: '',
        storeSelection: '',
        ctrmType: '',
        promotionId: '',
        groupSelection: '',
        comboCode: '',
        vat: '0',
        startDate: today,
        endDate: today,
        displayOrder: '',
        image: undefined,
        selectedDays: [false, false, false, false, false, false, false],
        selectedHours: {}
      })
    }
  }, [open, form])

  useEffect(() => {
    if (editingComboId && comboDetail && selectedStoreIds.length === 0) {
      setSelectedStoreIds([comboDetail.store_uid])
    }
  }, [editingComboId, comboDetail, selectedStoreIds.length])

  useEffect(() => {
    if (comboDetail && editingComboId && !isLoadingComboDetail) {
      if (hasMappedRef.current === editingComboId) {
        return
      }


      const startDate = new Date(comboDetail.from_date).toISOString().split('T')[0]
      const endDate = new Date(comboDetail.to_date).toISOString().split('T')[0]

      const mappedGroups = comboDetail.package_detail.LstItem_Options.map((group: any) => {
        const itemSettings: Record<string, { finalPrice: number; discountPercent: number; pricingMode: 'custom' | 'base' }> = {}
        group.LstItem.forEach((item: any) => {
          itemSettings[item.item_id] = {
            finalPrice: item.ots_price || 0,
            discountPercent: (item.discount_combo_item || 0) * 100, // Convert decimal to percentage
            pricingMode: item.state_change_price === 1 ? 'custom' : 'base'
          }
        })

        return {
          id: group.id,
          name: group.Name,
          required: group.Min_Permitted,
          max: group.Max_Permitted,
          itemIds: group.LstItem.map((item: any) => item.item_id),
          itemSettings: itemSettings
        }
      })

      const mappedPriceConfigs = comboDetail.extra_data.price_by_source.map((source: any) => {
        const timeFrameConfigs = source.price_times && source.price_times.length > 0
          ? convertPriceTimesToTimeFrameConfigs(source.price_times)
          : []
        return {
          orderSource: source.source_id,
          amount: source.price.toString(),
          comboCode: comboDetail.package_id,
          autoGenerateCode: false,
          timeFrameConfigs: timeFrameConfigs,
          price_times: source.price_times || []
        }
      })

      form.reset({
        comboName: comboDetail.package_name,
        price: comboDetail.ta_value.toString(),
        description: comboDetail.description,
        storeSelection: comboDetail.store_uid,
        ctrmType: '',
        promotionId: '',
        groupSelection: '',
        comboCode: comboDetail.package_id,
        vat: (comboDetail.vat_tax_rate * 100).toString(),
        startDate,
        endDate,
        displayOrder: comboDetail.sort.toString(),
        image: undefined,
        selectedDays: convertBitmaskToArray(comboDetail.time_sale_date_week || 0, 7),
        selectedHours: convertBitmaskToHours(comboDetail.time_sale_hour_day || 0)
      })

      setGroups(mappedGroups)
      setPriceSourceConfigs(mappedPriceConfigs)
      setSelectedStoreIds([comboDetail.store_uid])

      // Load existing image if available
      if (comboDetail.image_path) {
        setImagePreview(comboDetail.image_path)
      }

      hasMappedRef.current = editingComboId
    }
  }, [comboDetail, editingComboId, promotions, itemTypes, isLoadingComboDetail])

  const handleClose = () => {
    hasMappedRef.current = null
    setImagePreview(null)
    setSelectedImageFile(null)
    setEnableComboCodeInput(false)
    onOpenChange(false)
  }

  const handleStoreSelectionConfirm = (selectedIds: string[]) => {
    setSelectedStoreIds(selectedIds)
    setStoreDialogOpen(false)
  }

  const handleStoreSelectionCancel = () => {
    setStoreDialogOpen(false)
  }

  const handleOpenStoreDialog = () => {
    if (storesError) {
      toast.error('Không thể tải danh sách cửa hàng')
      return
    }
    setStoreDialogOpen(true)
  }

  const handleOpenPriceSourceDialog = () => {
    setEditingPriceSourceIndex(-1)
    setPriceSourceDialogOpen(true)
  }

  const handleOpenGroupDialog = () => {
    setGroupDialogMode('create')
    setEditingGroupId(null)
    setGroupDialogOpen(true)
  }

  const handleEditGroup = (groupId: string) => {
    setGroupDialogMode('edit')
    setEditingGroupId(groupId)
    setGroupDialogOpen(true)
  }

  const handleDeleteGroup = (groupId: string) => {
    setGroups(prev => prev.filter(g => g.id !== groupId))
  }

  const handlePriceSourceConfirm = (data: {
    orderSource: string
    amount: string
    comboCode?: string
    autoGenerateCode: boolean
    timeFrameConfigs?: Array<{
      id: string
      amount: string
      startDate: string
      endDate: string
      selectedDays: boolean[]
      selectedHours: Record<string, boolean>
    }>
    price_times?: Array<{
      price: number
      from_date: number
      to_date: number
      time_sale_date_week: number
      time_sale_hour_day: number
    }>
  }) => {
    if (editingPriceSourceIndex >= 0) {
      const newConfigs = [...priceSourceConfigs]
      newConfigs[editingPriceSourceIndex] = data
      setPriceSourceConfigs(newConfigs)
    } else {
      setPriceSourceConfigs([...priceSourceConfigs, data])
    }
    setPriceSourceDialogOpen(false)
    setEditingPriceSourceIndex(-1)
  }

  const handlePriceSourceCancel = () => {
    setPriceSourceDialogOpen(false)
    setEditingPriceSourceIndex(-1)
  }

  const handleEditPriceSource = (index: number) => {
    setEditingPriceSourceIndex(index)
    setPriceSourceDialogOpen(true)
  }

  const convertSelectedDaysToNumbers = (selectedDays: boolean[]): number[] => {
    return selectedDays
      .map((selected, index) => selected ? index : -1)
      .filter(index => index !== -1)
  }

  const convertSelectedHoursToNumbers = (selectedHours: Record<string, boolean>): number[] => {
    return Object.entries(selectedHours)
      .filter(([_, selected]) => selected)
      .map(([hour, _]) => parseInt(hour))
      .sort((a, b) => a - b)
  }

  const handleTimeFrameConfigCancel = () => {
    setTimeFrameConfigDialogOpen(false)
    setSelectedSourceName('')
    setCurrentTimeFrameOnSave(null)
    setEditingTimeFrameData(null)
  }

  const onSubmit = async (data: ComboFormData) => {
    if (!companyUid || !selectedBrand?.id) {
      toast.error('Thiếu thông tin công ty hoặc thương hiệu')
      return
    }

    if (selectedStoreIds.length === 0) {
      toast.error('Vui lòng chọn ít nhất một cửa hàng')
      return
    }

    if (groups.length === 0) {
      toast.error('Vui lòng thêm ít nhất một nhóm món')
      return
    }

    try {
      // Upload image first if there's a selected file
      let imageUrl = ''
      if (selectedImageFile) {
        try {
          const uploadResponse = await new Promise<{ data: { image_url: string } }>((resolve, reject) => {
            uploadImage(selectedImageFile, {
              onSuccess: data => resolve(data),
              onError: error => reject(error)
            })
          })
          imageUrl = uploadResponse.data.image_url
        } catch (error) {
          toast.error('Tải ảnh lên thất bại. Vui lòng thử lại.')
          return
        }
      }

      const startDate = new Date(data.startDate)
      const endDate = new Date(data.endDate)

      endDate.setHours(23, 59, 59, 999)

      const timeSaleDateWeek = data.selectedDays.reduce((acc, isSelected, index) => {
        return isSelected ? acc | (1 << index) : acc
      }, 0)

      const timeSaleHourDay = Object.entries(data.selectedHours).reduce((acc, [hour, isSelected]) => {
        return isSelected ? acc | (1 << parseInt(hour)) : acc
      }, 0)

      const selectedPromotion = promotions.find(p => p.code === data.promotionId)

      const comboData = {
        package_detail: {
          LstItem_Options: groups.map(group => {
            const groupItems = items.filter(item => group.itemIds.includes(item.id))

            return {
              id: group.id,
              Name: group.name,
              LstItem: groupItems.map(item => {
                const itemSetting = group.itemSettings?.[item.id]
                const finalPrice = itemSetting?.finalPrice || item.ots_price || 0
                const discountPercent = itemSetting?.discountPercent || 0
                const pricingMode = itemSetting?.pricingMode || 'custom'

                return {
                  item_id: item.id,
                  item_name: item.item_name,
                  ta_price: finalPrice,
                  ots_price: finalPrice,
                  discount_combo_item: discountPercent / 100,
                  state_change_price: pricingMode === 'custom' ? 1 : 2
                }
              }),
              Min_Permitted: group.required || 0,
              Max_Permitted: group.max || 0
            }
          })
        },
        deleted: false,
        extra_data: {
          price_by_source: priceSourceConfigs.map(config => ({
            source_id: config.orderSource,
            price: parseFloat(config.amount) || 0,
            price_times: config.price_times || [],
            is_source_exist_in_city: true
          }))
        },
        vat_tax_rate: parseFloat(data.vat) / 100 || 0,
        item_type_uid: data.groupSelection || '',
        from_date: startDate.getTime(),
        to_date: endDate.getTime(),
        promotion_id: data.promotionId || '',
        time_sale_date_week: timeSaleDateWeek,
        time_sale_hour_day: timeSaleHourDay,
        sort: parseInt(data.displayOrder) || 1000,
        package_name: data.comboName,
        ots_value: parseFloat(data.price) || 0,
        description: data.description || '',
        company_uid: companyUid,
        brand_uid: selectedBrand.id,
        package_id: data.comboCode || `COMBO-${Date.now()}`,
        ta_value: parseFloat(data.price) || 0,
        store_uid: selectedStoreIds[0], // Use first selected store
        promotion_uid: selectedPromotion?.originalData?.promotions?.[0]?.promotion_uid || '',
        use_same_data: 0,
        image_path: imageUrl || '' // Add uploaded image URL
      }

      if (editingComboId && comboDetail) {
        // Clean up empty UUID fields to avoid database errors
        const cleanComboData = { ...comboData }

        // Handle promotion fields - use original values if form fields are empty
        if (!data.promotionId || data.promotionId === '') {
          cleanComboData.promotion_id = comboDetail.promotion_id || ''
          cleanComboData.promotion_uid = comboDetail.promotion_uid || ''
        }
        if (!data.groupSelection || data.groupSelection === '') {
          cleanComboData.item_type_uid = comboDetail.item_type_uid || ''
        }

        const updateData = {
          ...comboDetail,
          ...cleanComboData,
          id: comboDetail.id,
          created_at: comboDetail.created_at,
          created_by: comboDetail.created_by,
          updated_at: Date.now(),
          updated_by: null
        }

        console.log('🔄 Update data:', {
          originalComboData: comboData,
          cleanComboData,
          updateData,
          formData: data
        })

        updateCombo(updateData, {
          onSuccess: () => {
            handleClose()
          }
        })
      } else {
        createCombo(comboData, {
          onSuccess: () => {
            handleClose()
          }
        })
      }
    } catch (error) {
      console.error('Error creating combo:', error)
      toast.error('Có lỗi xảy ra khi tạo combo')
    }
  }

  if (!open) return null
  if (editingComboId && isLoadingComboDetail) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <div className='flex h-64 items-center justify-center'>
          <div className='text-center'>
            <div className='border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2'></div>
            <p className='text-muted-foreground'>Đang tải thông tin combo...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='mb-8'>
        <div className='mb-4 flex items-center justify-between'>
          <Button type='button' variant='ghost' size='sm' onClick={handleClose} className='flex items-center'>
            <X className='h-4 w-4' />
          </Button>
          <Button
            type='submit'
            form='combo-form'
            disabled={!form.formState.isValid || isCreating || isUpdating || isUploadingImage}
          >
            {isCreating || isUpdating || isUploadingImage ? 'Đang lưu...' : 'Lưu'}
          </Button>
        </div>

        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold'>{editingComboId ? 'Chi tiết combo' : 'Tạo combo mới'}</h1>
        </div>
      </div>

      <div className='mx-auto max-w-4xl'>
        <div className='rounded-lg border bg-white p-6 shadow-sm'>
          <Form {...form}>
            <form id='combo-form' onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
              {/* Thông tin chi tiết */}
              <div className='space-y-4'>
                <h3 className='text-base font-medium text-gray-700'>Thông tin chi tiết</h3>

                <div className='grid grid-cols-1 gap-8 lg:grid-cols-12'>
                  <div className='space-y-6 lg:col-span-9'>
                    <FormField
                      control={form.control}
                      name='comboName'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex items-center gap-4'>
                            <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                              Tên combo <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl className='flex-1'>
                              <Input placeholder='Nhập tên combo' className='w-full' {...field} />
                            </FormControl>
                          </div>
                          <FormMessage className='mt-1 ml-36' />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='price'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex items-center gap-4'>
                            <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                              Giá <span className='text-red-500'>*</span>
                            </FormLabel>
                            <FormControl className='flex-1'>
                              <Input
                                placeholder='0'
                                className='w-full'
                                value={formatNumberDisplay(field.value || '')}
                                onChange={e => handleNumberInputChange(e.target.value, field.onChange)}
                              />
                            </FormControl>
                          </div>
                          <FormMessage className='mt-1 ml-36' />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name='description'
                      render={({ field }) => (
                        <FormItem>
                          <div className='flex items-start gap-4'>
                            <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                              Mô tả
                            </FormLabel>
                            <FormControl className='flex-1'>
                              <Textarea placeholder='' className='w-full' rows={3} {...field} />
                            </FormControl>
                          </div>
                          <FormMessage className='mt-1 ml-36' />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className='flex justify-center lg:col-span-3 lg:justify-end'>
                    <FormField
                      control={form.control}
                      name='image'
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <div className='relative'>
                              <div
                                className='flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-2 transition-colors hover:bg-gray-100'
                                style={{ width: '105.33px', height: '105.33px' }}
                                onClick={() => document.getElementById('image-upload')?.click()}
                              >
                                {imagePreview ? (
                                  <img
                                    src={imagePreview}
                                    alt='Preview'
                                    className='h-full w-full rounded-lg object-cover'
                                  />
                                ) : (
                                  <>
                                    <Upload className='mb-1 h-6 w-6 text-gray-400' />
                                    <span className='text-center text-xs text-gray-500'>Chọn ảnh</span>
                                  </>
                                )}
                              </div>
                              {imagePreview && (
                                <button
                                  type='button'
                                  onClick={e => {
                                    e.stopPropagation()
                                    setImagePreview(null)
                                    setSelectedImageFile(null)
                                    field.onChange(null)
                                  }}
                                  className='absolute -top-2 -right-2 rounded-full bg-red-500 p-1 text-white transition-colors hover:bg-red-600'
                                >
                                  <Trash2 className='h-3 w-3' />
                                </button>
                              )}
                              <input
                                type='file'
                                accept='image/*'
                                onChange={async e => {
                                  const file = e.target.files?.[0]
                                  if (file) {
                                    // Show local preview immediately
                                    const reader = new FileReader()
                                    reader.onload = e => {
                                      setImagePreview(e.target?.result as string)
                                    }
                                    reader.readAsDataURL(file)

                                    // Store file for later upload
                                    setSelectedImageFile(file)
                                    field.onChange(file)
                                  }
                                }}
                                className='hidden'
                                id='image-upload'
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className='space-y-6'>
                  <FormField
                    control={form.control}
                    name='storeSelection'
                    render={() => (
                      <FormItem>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                            Cửa hàng <span className='text-red-500'>*</span>
                          </FormLabel>
                          <FormControl className='flex-1'>
                            <Button
                              type='button'
                              variant='outline'
                              className='w-full justify-start text-blue-600 hover:text-blue-700'
                              onClick={handleOpenStoreDialog}
                              disabled={storesLoading}
                            >
                              {storesLoading
                                ? 'Đang tải...'
                                : selectedStoreIds.length > 0
                                  ? `${selectedStoreIds.length} cửa hàng`
                                  : 'Chọn cửa hàng'}
                            </Button>
                          </FormControl>
                        </div>
                        <FormMessage className='mt-1 ml-36' />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='ctrmType'
                    render={({ field }) => {
                      const getSelectedValue = () => {
                        if (editingComboId && comboDetail && !field.value) {
                          if (comboDetail.promotion_id) {
                            const matchingPromotion = promotions.find(p => p.code === comboDetail.promotion_id)
                            return matchingPromotion?.code || ''
                          }
                        }
                        return field.value
                      }

                      return (
                        <FormItem>
                          <div className='flex items-center gap-4'>
                            <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                              CTKM
                            </FormLabel>
                            <FormControl className='flex-1'>
                              <Select
                                onValueChange={field.onChange}
                                value={getSelectedValue()}
                                disabled={selectedStoreIds.length === 0}
                              >
                                <SelectTrigger className='w-full'>
                                  <SelectValue placeholder={'Chọn CTKM có tại các cửa hàng áp dụng combo'} />
                                </SelectTrigger>
                                <SelectContent>
                                  {promotions.map(promotion => (
                                    <SelectItem key={promotion.code} value={promotion.code}>
                                      {promotion.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                          </div>
                          <FormMessage className='mt-1 ml-36' />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name='groupSelection'
                    render={({ field }) => {
                      const getSelectedValue = () => {
                        if (editingComboId && comboDetail && !field.value) {
                          if (comboDetail.item_type_uid) {
                            const matchingItemType = itemTypes.find(
                              itemType => itemType.id === comboDetail.item_type_uid
                            )
                            return matchingItemType ? comboDetail.item_type_uid : ''
                          }
                        }
                        return field.value
                      }

                      return (
                        <FormItem>
                          <div className='flex items-center gap-4'>
                            <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                              Nhóm món
                            </FormLabel>
                            <FormControl className='flex-1'>
                              <Select
                                onValueChange={field.onChange}
                                value={getSelectedValue()}
                                disabled={selectedStoreIds.length === 0}
                              >
                                <SelectTrigger className='w-full'>
                                  <SelectValue placeholder={'Chọn nhóm món'} />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value='none'>None</SelectItem>
                                  {itemTypes.map(itemType => (
                                    <SelectItem key={itemType.id} value={itemType.id}>
                                      {itemType.item_type_name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                          </div>
                          <FormMessage className='mt-1 ml-36' />
                        </FormItem>
                      )
                    }}
                  />

                  <FormField
                    control={form.control}
                    name='comboCode'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                            Mã combo
                          </FormLabel>
                          <FormControl className='flex-1'>
                            <div className='flex items-center gap-3'>                             
                              <Input
                                placeholder={enableComboCodeInput ? 'Nhập mã combo' : 'Hệ thống sẽ tự động tạo mã combo'}
                                className='w-full'
                                disabled={!enableComboCodeInput}
                                {...field}
                              />
                              <Checkbox
                                checked={enableComboCodeInput}
                                onCheckedChange={(checked) => {
                                  setEnableComboCodeInput(!!checked)
                                  if (!checked) {
                                    field.onChange('')
                                  }
                                }}
                              />
                            </div>
                          </FormControl>
                        </div>
                        <FormMessage className='mt-1 ml-36' />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='vat'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                            VAT
                          </FormLabel>
                          <div className='flex flex-1 items-center gap-2'>
                            <FormControl>
                              <Input
                                placeholder='0'
                                className='w-full'
                                value={formatNumberDisplay(field.value || '')}
                                onChange={e => handleNumberInputChange(e.target.value, field.onChange)}
                              />
                            </FormControl>
                            <span className='text-sm'>%</span>
                          </div>
                        </div>
                        <FormMessage className='mt-1 ml-36' />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Cấu hình giá theo nguồn */}
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-base font-medium text-gray-700'>Cấu hình giá theo nguồn</h3>
                  <Button type='button' variant='outline' size='sm' onClick={handleOpenPriceSourceDialog}>
                    Thêm nguồn
                  </Button>
                </div>

                {priceSourceConfigs.length > 0 && (
                  <div className='space-y-2'>
                    {priceSourceConfigs.map((config, index) => {
                      const orderSource = sourceOptions.find(source => source.id === config.orderSource)
                      const hasTimeFrameConfigs = config.timeFrameConfigs && config.timeFrameConfigs.length > 0

                      return (
                        <div key={index} className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                          <div className='flex-1 flex justify-between items-center'>
                            <div
                              className='cursor-pointer p-1 rounded'
                              onClick={() => handleEditPriceSource(index)}
                            >
                              <div className='font-medium'>
                                {orderSource?.sourceName || config.orderSource} - Số tiền: {formatNumberDisplay(config.amount)} ₫
                              </div>
                            </div>

                            {hasTimeFrameConfigs && (
                              <div className='w-6 h-6 flex items-center justify-center'>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <div className='w-4 h-4 bg-blue-500 rounded-full cursor-help flex items-center justify-center text-white text-xs'>
                                      i
                                    </div>
                                  </PopoverTrigger>
                                  <PopoverContent className='w-96 p-3'>
                                    <div className='space-y-2'>
                                      <div className='space-y-2 mt-3'>
                                        {config.timeFrameConfigs?.map((timeFrame, tfIndex) => (
                                          <div key={tfIndex} className='text-xs text-gray-700 border-l-2 border-gray-300 pl-2'>
                                            <div>● Từ ngày {formatDate(timeFrame.startDate)} đến ngày {formatDate(timeFrame.endDate)} Giá: {timeFrame.amount} ₫</div>
                                            <div className='text-gray-500 mt-1'>
                                              Khung giờ {formatSelectedHours(convertSelectedHoursToNumbers(timeFrame.selectedHours))} {formatSelectedDays(convertSelectedDaysToNumbers(timeFrame.selectedDays))}
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </PopoverContent>
                                </Popover>
                              </div>
                            )}
                          </div>
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            onClick={() => {
                              const newConfigs = priceSourceConfigs.filter((_, i) => i !== index)
                              setPriceSourceConfigs(newConfigs)
                            }}
                          >
                            <X className='h-4 w-4' />
                          </Button>
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>

              {/* Thứ tự hiển thị trong menu */}
              <div className='space-y-4'>
                <h3 className='text-lg font-medium text-gray-700'>Thứ tự hiển thị trong menu</h3>
                <p className='text-sm text-gray-500'>
                  Combo có số nhỏ hơn sẽ được sắp xếp lên trên trong menu tại thiết bị bán hàng
                </p>
                <FormField
                  control={form.control}
                  name='displayOrder'
                  render={({ field }) => (
                    <FormItem>
                      <div className='flex items-center gap-4'>
                        <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                          Thứ tự hiển thị
                        </FormLabel>
                        <FormControl className='flex-1'>
                          <Input placeholder='Nhập số thứ tự hiển thị' className='w-full' {...field} />
                        </FormControl>
                      </div>
                      <FormMessage className='mt-1 ml-36' />
                    </FormItem>
                  )}
                />
              </div>

              {/* Ngày áp dụng */}
              <div className='space-y-4'>
                <h3 className='text-lg font-medium text-gray-700'>Ngày áp dụng</h3>
                <div className='flex gap-6'>
                  <FormField
                    control={form.control}
                    name='startDate'
                    render={({ field }) => (
                      <FormItem className='flex-1'>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                            Ngày bắt đầu
                          </FormLabel>
                          <FormControl className='flex-1'>
                            <DatePicker
                              date={field.value ? new Date(field.value) : undefined}
                              onDateChange={date => field.onChange(date ? date.toISOString().split('T')[0] : '')}
                              placeholder='Chọn ngày bắt đầu'
                              className='w-full'
                            />
                          </FormControl>
                        </div>
                        <FormMessage className='mt-1 ml-36' />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='endDate'
                    render={({ field }) => (
                      <FormItem className='flex-1'>
                        <div className='flex items-center gap-4'>
                          <FormLabel className='w-32 flex-shrink-0 rounded bg-gray-100 p-3 text-left text-sm font-medium'>
                            Ngày kết thúc
                          </FormLabel>
                          <FormControl className='flex-1'>
                            <DatePicker
                              date={field.value ? new Date(field.value) : undefined}
                              onDateChange={date => field.onChange(date ? date.toISOString().split('T')[0] : '')}
                              placeholder='Chọn ngày kết thúc'
                              className='w-full'
                            />
                          </FormControl>
                        </div>
                        <FormMessage className='mt-1 ml-36' />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Khung thời gian bán */}
              <div className='space-y-4'>
                <h3 className='text-lg font-medium text-gray-700'>Khung thời gian bán</h3>

                {/* Chọn ngày */}
                <FormField
                  control={form.control}
                  name='selectedDays'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm text-gray-600'>Chọn ngày</FormLabel>
                      <div className='grid grid-cols-7 gap-2'>
                        {['Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'].map((day, index) => {
                          const isSelected = Array.isArray(field.value) ? field.value[index] : false
                          return (
                            <Button
                              key={day}
                              type='button'
                              variant={isSelected ? 'default' : 'outline'}
                              size='sm'
                              className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                              onClick={() => {
                                const newValue = Array.isArray(field.value)
                                  ? [...field.value]
                                  : [false, false, false, false, false, false, false]
                                newValue[index] = !newValue[index]
                                field.onChange(newValue)
                              }}
                            >
                              {day}
                            </Button>
                          )
                        })}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Chọn giờ */}
                <FormField
                  control={form.control}
                  name='selectedHours'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm text-gray-600'>Chọn giờ</FormLabel>
                      <div className='grid grid-cols-10 gap-2'>
                        {Array.from({ length: 24 }, (_, i) => i).map(hourIndex => {
                          const hourKey = hourIndex.toString() // Use string key like "0", "1", "2"
                          const hourDisplay = `${hourIndex}h` // Display like "0h", "1h", "2h"
                          const isSelected = field.value && field.value[hourKey]
                          return (
                            <Button
                              key={hourIndex}
                              type='button'
                              variant={isSelected ? 'default' : 'outline'}
                              size='sm'
                              className={`text-xs ${isSelected ? 'bg-blue-600 text-white' : 'border-blue-600 text-blue-600'}`}
                              onClick={() => {
                                const newValue = { ...field.value }
                                newValue[hourKey] = !newValue[hourKey]
                                field.onChange(newValue)
                              }}
                            >
                              {hourDisplay}
                            </Button>
                          )
                        })}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className='flex items-center justify-between'>
                  <h3 className='text-base font-medium text-gray-700'>Nhóm</h3>
                  <Button type='button' variant='outline' size='sm' onClick={handleOpenGroupDialog}>
                    Tạo nhóm
                  </Button>
                </div>

                {/* Render saved groups summary like the screenshot */}
                {groups.map(group => (
                  <div key={group.id} className='mt-2 rounded-md border p-3'>
                    <div className='mb-2 flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <span className='text-gray-500'>⇅</span>
                        <span className='font-medium'>{group.name}</span>
                      </div>
                      <div className='flex items-center gap-1 text-sm'>
                        <Button
                          type='button'
                          variant='link'
                          size='sm'
                          className='px-0 text-blue-500'
                          onClick={() => handleEditGroup(group.id)}
                        >
                          Sửa
                        </Button>
                        <span className='text-gray-300'>/</span>
                        <Button
                          type='button'
                          variant='link'
                          size='sm'
                          className='px-0 text-blue-500'
                          onClick={() => handleDeleteGroup(group.id)}
                        >
                          Xoá
                        </Button>
                      </div>
                    </div>

                    <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3'>
                      {group.itemIds.map(itemId => {
                        const item = items.find(i => i.id === itemId)
                        if (!item) return null
                        return (
                          <div key={itemId} className='rounded-md border bg-gray-50 p-4'>
                            <div className='font-medium'>{item.item_name}</div>
                            <div className='text-sm text-gray-600'>({item.item_id})</div>
                            <div className='mt-2 text-sm'>Giá gốc</div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                ))}

                <div></div>
              </div>
            </form>
          </Form>
        </div>
      </div>

      {/* Store Selection Dialog */}
      <StoreSelectionDialog
        open={storeDialogOpen}
        onOpenChange={setStoreDialogOpen}
        stores={stores}
        selectedStores={selectedStoreIds}
        onConfirm={handleStoreSelectionConfirm}
        onCancel={handleStoreSelectionCancel}
      />

      {/* Price Source Config Dialog */}
      <PriceSourceConfigDialog
        open={priceSourceDialogOpen}
        onOpenChange={setPriceSourceDialogOpen}
        onConfirm={handlePriceSourceConfirm}
        onCancel={handlePriceSourceCancel}
        selectedStoreIds={selectedStoreIds}
        editingData={editingPriceSourceIndex >= 0 ? priceSourceConfigs[editingPriceSourceIndex] : null}
        onOpenTimeFrameConfig={(sourceName, onSave, editingData) => {
          setSelectedSourceName(sourceName)
          setCurrentTimeFrameOnSave(() => onSave)
          setEditingTimeFrameData(editingData || null)
          setTimeFrameConfigDialogOpen(true)
        }}
      />

      {/* Group Create Dialog */}
      <GroupCreateDialog
        open={groupDialogOpen}
        onOpenChange={setGroupDialogOpen}
        onConfirm={data => {
          if (groupDialogMode === 'edit' && editingGroupId) {
            setGroups(prev =>
              prev.map(g =>
                g.id === editingGroupId
                  ? { ...g, name: data.name, required: data.required, max: data.max, itemIds: data.items, itemSettings: data.itemSettings }
                  : g
              )
            )
          } else {
            setGroups(prev => [
              ...prev,
              {
                id: `${Date.now()}`,
                name: data.name,
                required: data.required,
                max: data.max,
                itemIds: data.items,
                itemSettings: data.itemSettings
              }
            ])
          }
          setGroupDialogOpen(false)
          setEditingGroupId(null)
          setGroupDialogMode('create')
        }}
        onCancel={() => {
          setGroupDialogOpen(false)
          setEditingGroupId(null)
          setGroupDialogMode('create')
        }}
        items={items}
        selectedItemIds={editingGroupId ? groups.find(g => g.id === editingGroupId)?.itemIds || [] : []}
        initialName={editingGroupId ? groups.find(g => g.id === editingGroupId)?.name : undefined}
        initialRequired={editingGroupId ? groups.find(g => g.id === editingGroupId)?.required : undefined}
        initialMax={editingGroupId ? groups.find(g => g.id === editingGroupId)?.max : undefined}
        initialItemSettings={editingGroupId ? groups.find(g => g.id === editingGroupId)?.itemSettings : undefined}
      />

      <TimeFrameConfigDialog
        open={timeFrameConfigDialogOpen}
        onOpenChange={setTimeFrameConfigDialogOpen}
        onConfirm={(data) => {
          if (currentTimeFrameOnSave) {
            currentTimeFrameOnSave(data)
          }
          setTimeFrameConfigDialogOpen(false)
          setSelectedSourceName('')
          setCurrentTimeFrameOnSave(null)
          setEditingTimeFrameData(null)
        }}
        onCancel={handleTimeFrameConfigCancel}
        sourceName={selectedSourceName}
        editingData={editingTimeFrameData}
      />
    </div>
  )
}
